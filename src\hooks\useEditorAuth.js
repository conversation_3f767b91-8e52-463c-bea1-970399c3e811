import { useState, useEffect } from 'react';
import { onAuthChange, logout } from '../services/auth/authService';

/**
 * 编辑器认证管理Hook
 * 处理用户认证状态和相关操作
 */
export const useEditorAuth = () => {
  const [user, setUser] = useState(null);
  const [showAuthModal, setShowAuthModal] = useState(false);

  // 监听用户认证状态
  useEffect(() => {
    const unsubscribe = onAuthChange((user) => {
      if (user) {
        setUser(user);
        setShowAuthModal(false);
      } else {
        setUser(null);
      }
    });
    return () => unsubscribe();
  }, []);

  // 显示认证弹窗
  const handleShowAuth = () => {
    setShowAuthModal(true);
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // 关闭认证弹窗
  const handleCloseAuthModal = () => {
    setShowAuthModal(false);
  };

  return {
    user,
    showAuthModal,
    handleShowAuth,
    handleLogout,
    handleCloseAuthModal
  };
};
