import { db } from '../../config/firebaseConfig';
import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit as firestoreLimit, 
  deleteDoc,
  writeBatch,
  serverTimestamp 
} from 'firebase/firestore';

/**
 * Firebase历史记录服务
 * 管理所有类型的历史记录存储和检索
 */

// 集合名称常量
const COLLECTIONS = {
  CHAT_HISTORY: 'chat_history',
  AI_ANALYSIS: 'ai_analysis',
  DICTIONARY_SEARCH: 'dictionary_search',
  WRITING_HISTORY: 'writing_history',
  DIARY_HISTORY: 'diary_history'
};

/**
 * 获取当前用户ID
 * @returns {string|null} 用户ID或null
 */
const getCurrentUserId = () => {
  // 这里需要从auth context获取当前用户ID
  // 暂时返回null，需要在组件中传入用户ID
  return null;
};

/**
 * 聊天历史记录服务
 */
export const chatHistoryService = {
  /**
   * 保存聊天会话到Firebase
   * @param {Object} sessionData - 会话数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 保存的会话数据
   */
  async saveChatSession(sessionData, userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    // 确保 sessionData.id 是字符串类型
    const sessionIdStr = String(sessionData.id);

    try {
      const sessionRef = doc(db, COLLECTIONS.CHAT_HISTORY, userId, 'sessions', sessionIdStr);
      const dataToSave = {
        ...sessionData,
        id: sessionIdStr, // 更新为字符串ID
        userId,
        updatedAt: serverTimestamp(),
        createdAt: sessionData.createdAt || serverTimestamp()
      };

      await setDoc(sessionRef, dataToSave);
      console.log('✅ 聊天会话保存到Firebase成功:', sessionIdStr);
      return dataToSave;
    } catch (error) {
      console.error('保存聊天会话到Firebase失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户的聊天历史
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 聊天历史数组
   */
  async getChatHistory(userId, limit = 50) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    // 确保limit是数字
    const limitCount = typeof limit === 'number' ? limit : 50;
    console.log('🔍 getChatHistory 参数:', { userId, limit, limitCount });

    try {
      const sessionsRef = collection(db, COLLECTIONS.CHAT_HISTORY, userId, 'sessions');
      const q = query(
        sessionsRef,
        orderBy('updatedAt', 'desc'),
        firestoreLimit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const sessions = [];

      querySnapshot.forEach((doc) => {
        sessions.push({
          id: doc.id,
          ...doc.data()
        });
      });

      console.log(`📚 从Firebase获取到 ${sessions.length} 条聊天历史`);
      return sessions;
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      throw error;
    }
  },

  /**
   * 删除指定的聊天会话
   * @param {string} userId - 用户ID
   * @param {string} sessionId - 会话ID
   * @returns {Promise<void>}
   */
  async deleteChatSession(userId, sessionId) {
    if (!userId || !sessionId) {
      throw new Error('用户ID和会话ID都是必需的');
    }

    // 确保 sessionId 是字符串类型
    const sessionIdStr = String(sessionId);

    try {
      const sessionRef = doc(db, COLLECTIONS.CHAT_HISTORY, userId, 'sessions', sessionIdStr);
      await deleteDoc(sessionRef);
      console.log('🗑️ 聊天会话删除成功:', sessionIdStr);
    } catch (error) {
      console.error('删除聊天会话失败:', error);
      throw error;
    }
  },

  /**
   * 清空用户的所有聊天历史
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async clearAllChatHistory(userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const sessionsRef = collection(db, COLLECTIONS.CHAT_HISTORY, userId, 'sessions');
      const querySnapshot = await getDocs(sessionsRef);
      const batch = writeBatch(db);

      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log('🗑️ 所有聊天历史清空成功');
    } catch (error) {
      console.error('清空聊天历史失败:', error);
      throw error;
    }
  }
};

/**
 * AI分析历史记录服务
 */
export const aiAnalysisService = {
  /**
   * 保存AI分析记录到Firebase
   * @param {Object} analysisData - 分析数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 保存的分析数据
   */
  async saveAnalysis(analysisData, userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const analysisRef = doc(collection(db, COLLECTIONS.AI_ANALYSIS, userId, 'analyses'));
      const dataToSave = {
        ...analysisData,
        userId,
        createdAt: serverTimestamp()
      };

      await setDoc(analysisRef, dataToSave);
      console.log('✅ AI分析记录保存到Firebase成功');
      return dataToSave;
    } catch (error) {
      console.error('保存AI分析记录失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户的AI分析历史
   * @param {string} userId - 用户ID
   * @param {number} limitCount - 限制数量
   * @returns {Promise<Array>} 分析历史数组
   */
  async getAnalysisHistory(userId, limitCount = 50) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const analysesRef = collection(db, COLLECTIONS.AI_ANALYSIS, userId, 'analyses');
      const q = query(
        analysesRef,
        orderBy('createdAt', 'desc'),
        firestoreLimit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const analyses = [];

      querySnapshot.forEach((doc) => {
        analyses.push({
          id: doc.id,
          ...doc.data()
        });
      });

      console.log(`📚 从Firebase获取到 ${analyses.length} 条AI分析历史`);
      return analyses;
    } catch (error) {
      console.error('获取AI分析历史失败:', error);
      throw error;
    }
  },

  /**
   * 删除指定的AI分析记录
   * @param {string} userId - 用户ID
   * @param {string} analysisId - 分析记录ID
   * @returns {Promise<void>}
   */
  async deleteAnalysis(userId, analysisId) {
    console.log('🔍 deleteAnalysis 被调用:', { userId, analysisId });
    console.log('📊 userId 类型:', typeof userId);
    console.log('📊 analysisId 类型:', typeof analysisId);
    console.log('📊 userId 值:', userId);
    console.log('📊 analysisId 值:', analysisId);
    
    if (!userId || !analysisId) {
      throw new Error('用户ID和分析记录ID都是必需的');
    }

    // 确保 analysisId 是字符串类型
    const analysisIdStr = String(analysisId);
    console.log('📊 转换后的 analysisId:', analysisIdStr, '类型:', typeof analysisIdStr);

    try {
      console.log('📊 开始创建 doc 引用...');
      console.log('📊 COLLECTIONS.AI_ANALYSIS:', COLLECTIONS.AI_ANALYSIS);
      const analysisRef = doc(db, COLLECTIONS.AI_ANALYSIS, userId, 'analyses', analysisIdStr);
      console.log('📊 doc 引用创建成功:', analysisRef);
      
      console.log('📊 开始执行 deleteDoc...');
      await deleteDoc(analysisRef);
      console.log('🗑️ AI分析记录删除成功:', analysisIdStr);
    } catch (error) {
      console.error('❌ 删除AI分析记录失败:', error);
      console.error('❌ 错误类型:', typeof error);
      console.error('❌ 错误名称:', error.name);
      console.error('❌ 错误消息:', error.message);
      console.error('❌ 错误堆栈:', error.stack);
      throw error;
    }
  },

  /**
   * 清空用户的所有AI分析历史
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async clearAllAnalysisHistory(userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const analysesRef = collection(db, COLLECTIONS.AI_ANALYSIS, userId, 'analyses');
      const querySnapshot = await getDocs(analysesRef);
      const batch = writeBatch(db);

      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log('🗑️ 所有AI分析历史清空成功');
    } catch (error) {
      console.error('清空AI分析历史失败:', error);
      throw error;
    }
  }
};

/**
 * 字典搜索历史记录服务
 */
export const dictionarySearchService = {
  /**
   * 保存搜索词到Firebase
   * @param {string} term - 搜索词
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async saveSearchTerm(term, userId) {
    if (!userId || !term) {
      throw new Error('用户ID和搜索词都是必需的');
    }

    try {
      const searchRef = doc(collection(db, COLLECTIONS.DICTIONARY_SEARCH, userId, 'searches'));
      const dataToSave = {
        term: term.trim(),
        userId,
        searchedAt: serverTimestamp()
      };

      await setDoc(searchRef, dataToSave);
      console.log('✅ 搜索词保存到Firebase成功:', term);
    } catch (error) {
      console.error('保存搜索词失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户的搜索历史
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 搜索历史数组
   */
  async getSearchHistory(userId, limit = 20) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    // 确保limit是数字
    const limitCount = typeof limit === 'number' ? limit : 20;
    console.log('🔍 getSearchHistory 参数:', { userId, limit, limitCount });

    try {
      const searchesRef = collection(db, COLLECTIONS.DICTIONARY_SEARCH, userId, 'searches');
      const q = query(
        searchesRef,
        orderBy('searchedAt', 'desc'),
        firestoreLimit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const searches = [];

      querySnapshot.forEach((doc) => {
        searches.push({
          id: doc.id,
          ...doc.data()
        });
      });

      console.log(`📚 从Firebase获取到 ${searches.length} 条搜索历史`);
      return searches;
    } catch (error) {
      console.error('获取搜索历史失败:', error);
      throw error;
    }
  },

  /**
   * 清空用户的搜索历史
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async clearSearchHistory(userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const searchesRef = collection(db, COLLECTIONS.DICTIONARY_SEARCH, userId, 'searches');
      const querySnapshot = await getDocs(searchesRef);
      const batch = writeBatch(db);

      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log('🗑️ 搜索历史清空成功');
    } catch (error) {
      console.error('清空搜索历史失败:', error);
      throw error;
    }
  },

  /**
   * 删除指定的搜索记录
   * @param {string} userId - 用户ID
   * @param {string} searchId - 搜索记录ID
   * @returns {Promise<void>}
   */
  async deleteSearch(userId, searchId) {
    if (!userId || !searchId) {
      throw new Error('用户ID和搜索记录ID都是必需的');
    }

    try {
      const searchRef = doc(db, COLLECTIONS.DICTIONARY_SEARCH, userId, 'searches', searchId);
      await deleteDoc(searchRef);
      console.log('🗑️ 搜索记录删除成功:', searchId);
    } catch (error) {
      console.error('删除搜索记录失败:', error);
      throw error;
    }
  },

  /**
   * 清空用户的所有搜索历史
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async clearAllSearch(userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const searchesRef = collection(db, COLLECTIONS.DICTIONARY_SEARCH, userId, 'searches');
      const querySnapshot = await getDocs(searchesRef);
      const batch = writeBatch(db);

      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log('🗑️ 所有搜索历史清空成功');
    } catch (error) {
      console.error('清空搜索历史失败:', error);
      throw error;
    }
  }
};

/**
 * 写作历史记录服务
 */
export const writingHistoryService = {
  /**
   * 保存写作记录到Firebase
   * @param {Object} writingData - 写作数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 保存的写作数据
   */
  async saveWriting(writingData, userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const writingRef = doc(collection(db, COLLECTIONS.WRITING_HISTORY, userId, 'writings'));
      const dataToSave = {
        ...writingData,
        userId,
        createdAt: serverTimestamp()
      };

      await setDoc(writingRef, dataToSave);
      console.log('✅ 写作记录保存到Firebase成功');
      return dataToSave;
    } catch (error) {
      console.error('保存写作记录失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户的写作历史
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 写作历史数组
   */
  async getWritingHistory(userId, limit = 50) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    // 确保limit是数字
    const limitCount = typeof limit === 'number' ? limit : 50;
    console.log('🔍 getWritingHistory 参数:', { userId, limit, limitCount });

    try {
      const writingsRef = collection(db, COLLECTIONS.WRITING_HISTORY, userId, 'writings');
      const q = query(
        writingsRef,
        orderBy('createdAt', 'desc'),
        firestoreLimit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const writings = [];

      querySnapshot.forEach((doc) => {
        writings.push({
          id: doc.id,
          ...doc.data()
        });
      });

      console.log(`📚 从Firebase获取到 ${writings.length} 条写作历史`);
      return writings;
    } catch (error) {
      console.error('获取写作历史失败:', error);
      throw error;
    }
  },

  /**
   * 删除指定的写作记录
   * @param {string} userId - 用户ID
   * @param {string} writingId - 写作记录ID
   * @returns {Promise<void>}
   */
  async deleteWriting(userId, writingId) {
    if (!userId || !writingId) {
      throw new Error('用户ID和写作记录ID都是必需的');
    }

    try {
      const writingRef = doc(db, COLLECTIONS.WRITING_HISTORY, userId, 'writings', writingId);
      await deleteDoc(writingRef);
      console.log('🗑️ 写作记录删除成功:', writingId);
    } catch (error) {
      console.error('删除写作记录失败:', error);
      throw error;
    }
  },

  /**
   * 清空用户的所有写作历史
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async clearAllWriting(userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const writingsRef = collection(db, COLLECTIONS.WRITING_HISTORY, userId, 'writings');
      const querySnapshot = await getDocs(writingsRef);
      const batch = writeBatch(db);

      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log('🗑️ 所有写作历史清空成功');
    } catch (error) {
      console.error('清空写作历史失败:', error);
      throw error;
    }
  }
};

/**
 * 日记历史记录服务
 */
export const diaryHistoryService = {
  /**
   * 保存日记到Firebase
   * @param {Object} diaryData - 日记数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 保存的日记数据
   */
  async saveDiary(diaryData, userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const diaryRef = doc(collection(db, COLLECTIONS.DIARY_HISTORY, userId, 'diaries'));
      const dataToSave = {
        ...diaryData,
        userId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      await setDoc(diaryRef, dataToSave);
      console.log('✅ 日记保存到Firebase成功');
      return dataToSave;
    } catch (error) {
      console.error('保存日记失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户的日记历史
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 日记历史数组
   */
  async getDiaryHistory(userId, limit = 30) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    // 确保limit是数字
    const limitCount = typeof limit === 'number' ? limit : 30;
    console.log('🔍 getDiaryHistory 参数:', { userId, limit, limitCount });

    try {
      const diariesRef = collection(db, COLLECTIONS.DIARY_HISTORY, userId, 'diaries');
      const q = query(
        diariesRef,
        orderBy('createdAt', 'desc'),
        firestoreLimit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const diaries = [];

      querySnapshot.forEach((doc) => {
        diaries.push({
          id: doc.id,
          ...doc.data()
        });
      });

      console.log(`📚 从Firebase获取到 ${diaries.length} 条日记历史`);
      return diaries;
    } catch (error) {
      console.error('获取日记历史失败:', error);
      throw error;
    }
  },

  /**
   * 删除指定的日记记录
   * @param {string} userId - 用户ID
   * @param {string} diaryId - 日记记录ID
   * @returns {Promise<void>}
   */
  async deleteDiary(userId, diaryId) {
    if (!userId || !diaryId) {
      throw new Error('用户ID和日记记录ID都是必需的');
    }

    try {
      const diaryRef = doc(db, COLLECTIONS.DIARY_HISTORY, userId, 'diaries', diaryId);
      await deleteDoc(diaryRef);
      console.log('🗑️ 日记记录删除成功:', diaryId);
    } catch (error) {
      console.error('删除日记记录失败:', error);
      throw error;
    }
  },

  /**
   * 清空用户的所有日记历史
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async clearAllDiary(userId) {
    if (!userId) {
      throw new Error('用户ID是必需的');
    }

    try {
      const diariesRef = collection(db, COLLECTIONS.DIARY_HISTORY, userId, 'diaries');
      const querySnapshot = await getDocs(diariesRef);
      const batch = writeBatch(db);

      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log('🗑️ 所有日记历史清空成功');
    } catch (error) {
      console.error('清空日记历史失败:', error);
      throw error;
    }
  }
};

/**
 * 数据迁移工具
 * 将本地存储的历史记录迁移到Firebase
 */
export const migrationService = {
  /**
   * 迁移聊天历史到Firebase
   * @param {string} userId - 用户ID
   * @returns {Promise<number>} 迁移的记录数量
   */
  async migrateChatHistory(userId) {
    try {
      const localHistory = localStorage.getItem('english_chat_history');
      if (!localHistory) {
        console.log('📱 本地没有聊天历史，跳过迁移');
        return 0;
      }

      const history = JSON.parse(localHistory);
      if (!history || history.length === 0) {
        console.log('📱 本地聊天历史为空，跳过迁移');
        return 0;
      }

      // 检查Firebase是否已有数据
      const existingHistory = await chatHistoryService.getChatHistory(userId, 1);
      if (existingHistory && existingHistory.length > 0) {
        console.log('✅ Firebase已有聊天历史，跳过迁移');
        return 0;
      }

      let migratedCount = 0;
      console.log(`🔄 开始迁移 ${history.length} 条聊天历史...`);

      for (const session of history) {
        try {
          await chatHistoryService.saveChatSession(session, userId);
          migratedCount++;
        } catch (error) {
          console.error('迁移聊天会话失败:', session.id, error);
        }
      }

      console.log(`✅ 成功迁移 ${migratedCount} 条聊天历史到Firebase`);
      return migratedCount;
    } catch (error) {
      console.error('迁移聊天历史失败:', error);
      throw error;
    }
  },

  /**
   * 迁移AI分析历史到Firebase
   * @param {string} userId - 用户ID
   * @returns {Promise<number>} 迁移的记录数量
   */
  async migrateAnalysisHistory(userId) {
    try {
      const localHistory = localStorage.getItem('ai_analysis_history');
      if (!localHistory) {
        console.log('📱 本地没有AI分析历史，跳过迁移');
        return 0;
      }

      const history = JSON.parse(localHistory);
      if (!history || history.length === 0) {
        console.log('📱 本地AI分析历史为空，跳过迁移');
        return 0;
      }

      // 检查Firebase是否已有数据
      const existingHistory = await aiAnalysisService.getAnalysisHistory(userId, 1);
      if (existingHistory && existingHistory.length > 0) {
        console.log('✅ Firebase已有AI分析历史，跳过迁移');
        return 0;
      }

      let migratedCount = 0;
      console.log(`🔄 开始迁移 ${history.length} 条AI分析历史...`);

      for (const analysis of history) {
        try {
          await aiAnalysisService.saveAnalysis(analysis, userId);
          migratedCount++;
        } catch (error) {
          console.error('迁移AI分析记录失败:', analysis.id, error);
        }
      }

      console.log(`✅ 成功迁移 ${migratedCount} 条AI分析历史到Firebase`);
      return migratedCount;
    } catch (error) {
      console.error('迁移AI分析历史失败:', error);
      throw error;
    }
  },

  /**
   * 迁移所有历史记录到Firebase
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 迁移结果
   */
  async migrateAllHistory(userId) {
    try {
      console.log('🚀 开始迁移所有历史记录到Firebase...');
      
      const results = {
        chat: await this.migrateChatHistory(userId),
        analysis: await this.migrateAnalysisHistory(userId),
        // 可以添加其他类型的历史记录迁移
      };

      const total = Object.values(results).reduce((sum, count) => sum + count, 0);
      console.log(`🎉 历史记录迁移完成！总共迁移 ${total} 条记录`);
      
      return results;
    } catch (error) {
      console.error('迁移所有历史记录失败:', error);
      throw error;
    }
  }
};
