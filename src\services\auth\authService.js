import { auth } from '../../config/firebaseConfig';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  signInWithPopup,
  GoogleAuthProvider,
} from 'firebase/auth';

/**
 * Registers a new user with email and password.
 * @param {string} email - The user's email.
 * @param {string} password - The user's password.
 * @returns {Promise<import('firebase/auth').UserCredential>} The user credential object.
 */
export const signUp = (email, password) => {
  return createUserWithEmailAndPassword(auth, email, password);
};

/**
 * Signs in an existing user with email and password.
 * @param {string} email - The user's email.
 * @param {string} password - The user's password.
 * @returns {Promise<import('firebase/auth').UserCredential>} The user credential object.
 */
export const signIn = (email, password) => {
  return signInWithEmailAndPassword(auth, email, password);
};

/**
 * Signs out the current user.
 * @returns {Promise<void>}
 */
export const logout = () => {
  return signOut(auth);
};

/**
 * Signs in with Google using popup.
 * @returns {Promise<import('firebase/auth').UserCredential>} The user credential object.
 */
export const signInWithGoogle = () => {
  const provider = new GoogleAuthProvider();
  // 添加额外的权限请求
  provider.addScope('email');
  provider.addScope('profile');
  return signInWithPopup(auth, provider);
};

/**
 * Listens for authentication state changes.
 * @param {function} callback - The function to call when the auth state changes. It receives the user object (or null) as an argument.
 * @returns {import('firebase/auth').Unsubscribe} A function to unsubscribe from the listener.
 */
export const onAuthChange = (callback) => {
  return onAuthStateChanged(auth, callback);
};
