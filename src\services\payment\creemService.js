// Creem 支付服务
// 处理与Creem API的所有交互，包括产品管理、订阅管理、支付处理等

import { Creem } from 'creem';
import { CREEM_CONFIG, CURRENT_ENVIRONMENT } from '../../config/creemConfig.js';

// 创建Creem客户端实例
const createCreemClient = async () => {
  const apiKey = await CREEM_CONFIG.getApiKey();
  if (!apiKey) {
    throw new Error('Creem API密钥未配置');
  }
  
  // 根据环境选择服务器
  const serverIdx = CURRENT_ENVIRONMENT === 'production' ? 0 : 1;
  
  return new Creem({ 
    serverIdx,
    retryConfig: {
      strategy: "backoff",
      backoff: { 
        initialInterval: 1000, 
        maxInterval: 5000, 
        exponent: 1.5, 
        maxElapsedTime: 30000 
      },
      retryConnectionErrors: true,
    }
  });
};

// 产品管理服务
export class CreemProductService {
  // 获取所有产品
  static async getProducts() {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.searchProducts({
        xApiKey: apiKey,
        // 可以添加搜索参数
      });
      
      return response.data || [];
    } catch (error) {
      console.error('获取产品列表失败:', error);
      throw new Error('获取产品列表失败: ' + error.message);
    }
  }
  
  // 获取单个产品详情
  static async getProduct(productId) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.retrieveProduct({
        productId,
        xApiKey: apiKey
      });
      
      return response;
    } catch (error) {
      console.error('获取产品详情失败:', error);
      throw new Error('获取产品详情失败: ' + error.message);
    }
  }
  
  // 创建产品（管理员功能）
  static async createProduct(productData) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.createProduct({
        xApiKey: apiKey,
        ...productData
      });
      
      return response;
    } catch (error) {
      console.error('创建产品失败:', error);
      throw new Error('创建产品失败: ' + error.message);
    }
  }
}

// 支付处理服务
export class CreemPaymentService {
  // 创建支付会话
  static async createCheckoutSession(productId, options = {}) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const checkoutData = {
        product_id: productId,
        ...options
      };
      
      // 如果有用户信息，添加到请求中
      if (options.customerEmail) {
        checkoutData.customer_email = options.customerEmail;
      }
      
      if (options.successUrl) {
        checkoutData.success_url = options.successUrl;
      }
      
      if (options.requestId) {
        checkoutData.request_id = options.requestId;
      }
      
      const response = await creem.createCheckout({
        xApiKey: apiKey,
        ...checkoutData
      });
      
      return response;
    } catch (error) {
      console.error('创建支付会话失败:', error);
      throw new Error('创建支付会话失败: ' + error.message);
    }
  }
  
  // 获取支付会话详情
  static async getCheckoutSession(checkoutId) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.retrieveCheckout({
        xApiKey: apiKey,
        // 注意：这里可能需要根据实际API调整参数
        checkoutId
      });
      
      return response;
    } catch (error) {
      console.error('获取支付会话失败:', error);
      throw new Error('获取支付会话失败: ' + error.message);
    }
  }
}

// 订阅管理服务
export class CreemSubscriptionService {
  // 获取客户信息
  static async getCustomer(customerId) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.retrieveCustomer({
        xApiKey: apiKey,
        // 注意：这里可能需要根据实际API调整参数
        customerId
      });
      
      return response;
    } catch (error) {
      console.error('获取客户信息失败:', error);
      throw new Error('获取客户信息失败: ' + error.message);
    }
  }
  
  // 获取订阅详情
  static async getSubscription(subscriptionId) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.retrieveSubscription({
        xApiKey: apiKey,
        // 注意：这里可能需要根据实际API调整参数
        subscriptionId
      });
      
      return response;
    } catch (error) {
      console.error('获取订阅详情失败:', error);
      throw new Error('获取订阅详情失败: ' + error.message);
    }
  }
  
  // 取消订阅
  static async cancelSubscription(subscriptionId) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.cancelSubscription({
        id: subscriptionId,
        xApiKey: apiKey
      });
      
      return response;
    } catch (error) {
      console.error('取消订阅失败:', error);
      throw new Error('取消订阅失败: ' + error.message);
    }
  }
  
  // 更新订阅
  static async updateSubscription(subscriptionId, updateData) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.updateSubscription({
        id: subscriptionId,
        xApiKey: apiKey,
        ...updateData
      });
      
      return response;
    } catch (error) {
      console.error('更新订阅失败:', error);
      throw new Error('更新订阅失败: ' + error.message);
    }
  }
  
  // 升级订阅
  static async upgradeSubscription(subscriptionId, newProductId) {
    try {
      const creem = await createCreemClient();
      const apiKey = await CREEM_CONFIG.getApiKey();
      
      const response = await creem.upgradeSubscription({
        id: subscriptionId,
        xApiKey: apiKey,
        product_id: newProductId
      });
      
      return response;
    } catch (error) {
      console.error('升级订阅失败:', error);
      throw new Error('升级订阅失败: ' + error.message);
    }
  }
}
