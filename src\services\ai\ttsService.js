/**
 * TTS (Text-to-Speech) 服务
 * 基于 Web Speech API 实现语音合成功能
 */

class TTSService {
  constructor() {
    this.synth = window.speechSynthesis;
    this.currentUtterance = null;
    this.isSupported = 'speechSynthesis' in window;
    this.voices = [];
    this.defaultSettings = {
      lang: 'en-US',
      rate: 0.9,
      pitch: 1.0,
      volume: 1.0
    };

    // 状态变化监听器
    this.statusListeners = [];

    // 用户主动停止标志
    this.userStopped = false;

    // 初始化语音列表
    this.loadVoices();

    // 监听语音列表变化（某些浏览器需要）
    if (this.synth.onvoiceschanged !== undefined) {
      this.synth.onvoiceschanged = () => this.loadVoices();
    }
  }

  /**
   * 加载可用语音列表
   */
  loadVoices() {
    this.voices = this.synth.getVoices();
  }

  /**
   * 添加状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  addStatusListener(listener) {
    this.statusListeners.push(listener);
  }

  /**
   * 移除状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeStatusListener(listener) {
    const index = this.statusListeners.indexOf(listener);
    if (index > -1) {
      this.statusListeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器状态变化
   */
  notifyStatusChange() {
    this.statusListeners.forEach(listener => {
      try {
        listener(this.status);
      } catch (error) {
        console.error('TTS状态监听器错误:', error);
      }
    });
  }

  /**
   * 获取可用的英语语音
   * @returns {Array} 英语语音列表
   */
  getEnglishVoices() {
    return this.voices.filter(voice =>
      voice.lang.startsWith('en') && voice.lang.includes('US', 'GB', 'AU')
    );
  }

  /**
   * 选择最佳语音
   * @param {string} lang - 语言代码
   * @returns {SpeechSynthesisVoice|null} 选中的语音
   */
  selectBestVoice(lang = 'en-US') {
    const englishVoices = this.getEnglishVoices();

    // 优先选择本地语音
    const localVoices = englishVoices.filter(voice => voice.localService);
    if (localVoices.length > 0) {
      // 优先选择美式英语
      const usVoice = localVoices.find(voice => voice.lang.includes('US'));
      return usVoice || localVoices[0];
    }

    // 备选：任何英语语音
    const usVoice = englishVoices.find(voice => voice.lang.includes('US'));
    return usVoice || englishVoices[0] || null;
  }

  /**
   * 预处理文本，移除不需要朗读的内容
   * @param {string} text - 原始文本
   * @returns {string} 处理后的文本
   */
  preprocessText(text) {
    return text
      // 移除表情符号
      .replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '')
      // 规范化空格
      .replace(/\s+/g, ' ')
      // 移除多余的标点
      .replace(/\.{2,}/g, '.')
      .replace(/!{2,}/g, '!')
      .replace(/\?{2,}/g, '?')
      .trim();
  }

  /**
   * 播放文本
   * @param {string} text - 要播放的文本
   * @param {Object} options - 播放选项
   * @returns {Promise<SpeechSynthesisUtterance>} 语音合成对象
   */
  speak(text, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('浏览器不支持语音合成功能'));
        return;
      }

      // 强制停止当前播放并等待清理完成
      this.stop();

      // 重置用户停止标志，准备新的播放
      this.userStopped = false;

      // 等待一小段时间确保之前的语音完全停止
      setTimeout(() => {
        // 预处理文本
        const processedText = this.preprocessText(text);
        if (!processedText) {
          reject(new Error('文本内容为空'));
          return;
        }

        // 创建语音合成对象
        const utterance = new SpeechSynthesisUtterance(processedText);

      // 合并设置
      const settings = { ...this.defaultSettings, ...options };

      // 配置语音参数
      utterance.lang = settings.lang;
      utterance.rate = Math.max(0.1, Math.min(10, settings.rate)); // 限制范围
      utterance.pitch = Math.max(0, Math.min(2, settings.pitch));   // 限制范围
      utterance.volume = Math.max(0, Math.min(1, settings.volume)); // 限制范围

      // 选择语音
      const selectedVoice = this.selectBestVoice(settings.lang);
      if (selectedVoice) {
        utterance.voice = selectedVoice;
      }

      // 设置事件监听器
      utterance.onstart = () => {
        console.log('TTS: 开始播放');
        this.notifyStatusChange();
      };

      utterance.onend = () => {
        console.log('TTS: 播放结束');
        this.currentUtterance = null;
        this.notifyStatusChange();
        resolve(utterance);
      };

      utterance.onerror = (event) => {
        console.error('TTS: 播放错误', event);
        this.currentUtterance = null;

        // 对于interrupted错误，只有在用户没有主动停止时才重新播放
        if (event.error === 'interrupted' && !this.userStopped) {
          console.log('TTS: 检测到interrupted错误，尝试重新播放');
          setTimeout(() => {
            try {
              this.synth.speak(utterance);
            } catch (retryError) {
              console.error('TTS: 重试播放失败', retryError);
              reject(new Error(`语音播放失败: ${event.error}`));
            }
          }, 200);
        } else if (event.error === 'interrupted' && this.userStopped) {
          console.log('TTS: 用户主动停止，不重新播放');
          this.userStopped = false; // 重置标志
          reject(new Error('用户主动停止播放'));
        } else {
          reject(new Error(`语音播放失败: ${event.error}`));
        }
      };

      utterance.onpause = () => {
        console.log('TTS: 播放暂停');
      };

      utterance.onresume = () => {
        console.log('TTS: 播放恢复');
      };

        // 保存当前语音对象
        this.currentUtterance = utterance;

        // 开始播放
        this.synth.speak(utterance);
      }, 100); // 等待100ms确保之前的语音完全停止
    });
  }

  /**
   * 暂停播放
   */
  pause() {
    if (this.synth.speaking && !this.synth.paused) {
      this.synth.pause();
      return true;
    }
    return false;
  }

  /**
   * 恢复播放
   */
  resume() {
    if (this.synth.paused) {
      this.synth.resume();
      return true;
    }
    return false;
  }

  /**
   * 停止播放
   */
  stop() {
    try {
      console.log('🔊 TTS停止调用，当前状态:', {
        speaking: this.synth.speaking,
        pending: this.synth.pending,
        hasUtterance: this.currentUtterance !== null
      });

      // 设置用户主动停止标志
      this.userStopped = true;

      if (this.synth.speaking || this.synth.pending) {
        console.log('🛑 调用synth.cancel()');
        this.synth.cancel();
      }
      this.currentUtterance = null;
      this.notifyStatusChange();
      console.log('✅ TTS停止完成');
      return true;
    } catch (error) {
      console.warn('TTS停止时出现错误:', error);
      this.currentUtterance = null;
      this.notifyStatusChange();
      return false;
    }
  }

  /**
   * 获取当前播放状态
   */
  get status() {
    return {
      isSupported: this.isSupported,
      isPlaying: this.synth.speaking && !this.synth.paused,
      isPaused: this.synth.paused,
      isStopped: !this.synth.speaking && !this.synth.pending,
      hasUtterance: this.currentUtterance !== null
    };
  }

  /**
   * 获取可用语音列表
   * @returns {Array} 语音列表
   */
  getVoices() {
    return this.voices;
  }

  /**
   * 更新默认设置
   * @param {Object} settings - 新的设置
   */
  updateSettings(settings) {
    this.defaultSettings = { ...this.defaultSettings, ...settings };
  }

  /**
   * 测试语音功能
   * @returns {Promise<boolean>} 是否支持
   */
  async test() {
    if (!this.isSupported) {
      return false;
    }

    try {
      await this.speak('Hello, this is a test.', { volume: 0.1 });
      return true;
    } catch (error) {
      console.error('TTS测试失败:', error);
      return false;
    }
  }
}

// 创建全局实例
export const ttsService = new TTSService();

// 默认导出
export default ttsService;
