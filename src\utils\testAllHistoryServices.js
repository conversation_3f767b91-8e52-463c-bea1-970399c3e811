/**
 * 测试所有历史记录服务的Firebase功能
 */

import { auth } from '../config/firebaseConfig';
import { generateUniqueId } from './idGenerator';

/**
 * 测试所有历史记录服务的完整功能
 */
export const testAllHistoryServices = async () => {
  console.log('🧪 开始测试所有历史记录服务的Firebase功能...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    console.log('👤 用户ID:', user.uid);
    
    const results = {
      chat: await testChatHistoryService(user.uid),
      analysis: await testAnalysisHistoryService(user.uid),
      dictionary: await testDictionarySearchService(user.uid),
      writing: await testWritingHistoryService(user.uid),
      diary: await testDiaryHistoryService(user.uid)
    };
    
    console.log('\n📊 测试结果汇总:', results);
    
    const successCount = Object.values(results).filter(Boolean).length;
    console.log(`✅ 成功测试 ${successCount}/5 种历史记录服务`);
    
    return results;
    
  } catch (error) {
    console.error('❌ 测试所有历史记录服务失败:', error);
    return false;
  }
};

/**
 * 测试聊天历史服务
 */
const testChatHistoryService = async (userId) => {
  console.log('\n💬 测试聊天历史服务...');
  
  try {
    const { chatHistoryService } = await import('../services/history/firebaseHistoryService');
    
    // 测试数据
    const testSession = {
      id: generateUniqueId(),
      title: '测试聊天会话',
      messages: [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' }
      ],
      timestamp: new Date().toISOString()
    };
    
    // 1. 测试保存
    console.log('📝 测试保存聊天会话...');
    await chatHistoryService.saveChatSession(testSession, userId);
    console.log('✅ 保存成功');
    
    // 2. 测试获取
    console.log('📖 测试获取聊天历史...');
    const history = await chatHistoryService.getChatHistory(userId, 10);
    console.log(`✅ 获取成功，找到 ${history.length} 条记录`);
    
    // 3. 测试删除
    console.log('🗑️ 测试删除聊天会话...');
    await chatHistoryService.deleteChatSession(userId, testSession.id);
    console.log('✅ 删除成功');
    
    return true;
    
  } catch (error) {
    console.error('❌ 聊天历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试AI分析历史服务
 */
const testAnalysisHistoryService = async (userId) => {
  console.log('\n📝 测试AI分析历史服务...');
  
  try {
    const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
    
    // 测试数据
    const testAnalysis = {
      id: generateUniqueId(),
      text: 'This is a test text for analysis.',
      rawAnalysis: 'AI analysis result...',
      analysis: { suggestions: [] },
      timestamp: new Date().toISOString()
    };
    
    // 1. 测试保存
    console.log('📝 测试保存AI分析...');
    await aiAnalysisService.saveAnalysis(testAnalysis, userId);
    console.log('✅ 保存成功');
    
    // 2. 测试获取
    console.log('📖 测试获取AI分析历史...');
    const history = await aiAnalysisService.getAnalysisHistory(userId, 10);
    console.log(`✅ 获取成功，找到 ${history.length} 条记录`);
    
    // 3. 测试删除
    console.log('🗑️ 测试删除AI分析...');
    await aiAnalysisService.deleteAnalysis(userId, testAnalysis.id);
    console.log('✅ 删除成功');
    
    return true;
    
  } catch (error) {
    console.error('❌ AI分析历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试字典搜索服务
 */
const testDictionarySearchService = async (userId) => {
  console.log('\n📚 测试字典搜索服务...');
  
  try {
    const { dictionarySearchService } = await import('../services/history/firebaseHistoryService');
    
    // 测试数据
    const testTerm = 'test-word-' + Date.now();
    
    // 1. 测试保存
    console.log('📝 测试保存搜索词...');
    await dictionarySearchService.saveSearchTerm(testTerm, userId);
    console.log('✅ 保存成功');
    
    // 2. 测试获取
    console.log('📖 测试获取搜索历史...');
    const history = await dictionarySearchService.getSearchHistory(userId, 10);
    console.log(`✅ 获取成功，找到 ${history.length} 条记录`);
    
    // 3. 测试删除（如果有记录）
    if (history.length > 0) {
      console.log('🗑️ 测试删除搜索记录...');
      await dictionarySearchService.deleteSearch(userId, history[0].id);
      console.log('✅ 删除成功');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 字典搜索服务测试失败:', error);
    return false;
  }
};

/**
 * 测试写作历史服务
 */
const testWritingHistoryService = async (userId) => {
  console.log('\n✍️ 测试写作历史服务...');
  
  try {
    const { writingHistoryService } = await import('../services/history/firebaseHistoryService');
    
    // 测试数据
    const testWriting = {
      id: generateUniqueId(),
      text: 'This is a test writing piece.',
      analysis: 'Writing analysis...',
      timestamp: new Date().toISOString()
    };
    
    // 1. 测试保存
    console.log('📝 测试保存写作记录...');
    await writingHistoryService.saveWriting(testWriting, userId);
    console.log('✅ 保存成功');
    
    // 2. 测试获取
    console.log('📖 测试获取写作历史...');
    const history = await writingHistoryService.getWritingHistory(userId, 10);
    console.log(`✅ 获取成功，找到 ${history.length} 条记录`);
    
    // 3. 测试删除
    console.log('🗑️ 测试删除写作记录...');
    await writingHistoryService.deleteWriting(userId, testWriting.id);
    console.log('✅ 删除成功');
    
    return true;
    
  } catch (error) {
    console.error('❌ 写作历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试日记历史服务
 */
const testDiaryHistoryService = async (userId) => {
  console.log('\n📔 测试日记历史服务...');
  
  try {
    const { diaryHistoryService } = await import('../services/history/firebaseHistoryService');
    
    // 测试数据
    const testDiary = {
      id: generateUniqueId(),
      content: 'This is a test diary entry.',
      date: new Date().toISOString().split('T')[0],
      timestamp: new Date().toISOString()
    };
    
    // 1. 测试保存
    console.log('📝 测试保存日记...');
    await diaryHistoryService.saveDiary(testDiary, userId);
    console.log('✅ 保存成功');
    
    // 2. 测试获取
    console.log('📖 测试获取日记历史...');
    const history = await diaryHistoryService.getDiaryHistory(userId, 10);
    console.log(`✅ 获取成功，找到 ${history.length} 条记录`);
    
    // 3. 测试删除
    console.log('🗑️ 测试删除日记...');
    await diaryHistoryService.deleteDiary(userId, testDiary.id);
    console.log('✅ 删除成功');
    
    return true;
    
  } catch (error) {
    console.error('❌ 日记历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试清空功能
 */
export const testClearAllFunctions = async () => {
  console.log('🧹 测试所有清空功能...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    const { 
      chatHistoryService, 
      aiAnalysisService, 
      dictionarySearchService,
      writingHistoryService,
      diaryHistoryService 
    } = await import('../services/history/firebaseHistoryService');
    
    const results = {};
    
    // 测试各种清空功能
    try {
      await chatHistoryService.clearAllChatHistory(user.uid);
      results.chat = true;
      console.log('✅ 聊天历史清空功能正常');
    } catch (error) {
      results.chat = false;
      console.error('❌ 聊天历史清空功能失败:', error);
    }
    
    try {
      await aiAnalysisService.clearAllAnalysisHistory(user.uid);
      results.analysis = true;
      console.log('✅ AI分析历史清空功能正常');
    } catch (error) {
      results.analysis = false;
      console.error('❌ AI分析历史清空功能失败:', error);
    }
    
    try {
      await dictionarySearchService.clearAllSearch(user.uid);
      results.dictionary = true;
      console.log('✅ 字典搜索历史清空功能正常');
    } catch (error) {
      results.dictionary = false;
      console.error('❌ 字典搜索历史清空功能失败:', error);
    }
    
    try {
      await writingHistoryService.clearAllWriting(user.uid);
      results.writing = true;
      console.log('✅ 写作历史清空功能正常');
    } catch (error) {
      results.writing = false;
      console.error('❌ 写作历史清空功能失败:', error);
    }
    
    try {
      await diaryHistoryService.clearAllDiary(user.uid);
      results.diary = true;
      console.log('✅ 日记历史清空功能正常');
    } catch (error) {
      results.diary = false;
      console.error('❌ 日记历史清空功能失败:', error);
    }
    
    console.log('📊 清空功能测试结果:', results);
    
    const successCount = Object.values(results).filter(Boolean).length;
    console.log(`✅ 成功测试 ${successCount}/5 种清空功能`);
    
    return results;
    
  } catch (error) {
    console.error('❌ 测试清空功能失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.testAllHistory = {
    testAllHistoryServices,
    testClearAllFunctions
  };
  
  console.log('🔧 所有历史记录服务测试工具已加载到 window.testAllHistory');
  console.log('💡 使用方法:');
  console.log('  - await window.testAllHistory.testAllHistoryServices() // 测试所有服务');
  console.log('  - await window.testAllHistory.testClearAllFunctions() // 测试清空功能');
}
