/**
 * 测试智能聊天标题生成功能
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试智能标题生成
export const testSmartChatTitles = () => {
  console.log('🧪 开始测试智能聊天标题生成功能...');
  
  // 模拟用户登录
  const testUserId = 'test_user_' + Date.now();
  simpleStorageService.init(testUserId);
  
  console.log('👤 测试用户ID:', testUserId);
  
  // 测试用例1: 帮助类对话
  console.log('💡 测试用例1: 帮助类对话');
  const helpMessages = [
    { id: 1, type: 'user', content: 'Can you help me with English grammar?' },
    { id: 2, type: 'ai', content: 'Of course! I\'d be happy to help you with English grammar.' }
  ];
  const helpSession = simpleStorageService.saveChatSession(helpMessages);
  console.log('✅ 帮助类标题:', helpSession.title);
  
  // 测试用例2: 写作类对话
  console.log('✍️ 测试用例2: 写作类对话');
  const writingMessages = [
    { id: 1, type: 'user', content: 'I want to write a story about my childhood' },
    { id: 2, type: 'ai', content: 'That sounds wonderful! Let me help you with your story.' }
  ];
  const writingSession = simpleStorageService.saveChatSession(writingMessages);
  console.log('✅ 写作类标题:', writingSession.title);
  
  // 测试用例3: 学习类对话
  console.log('📚 测试用例3: 学习类对话');
  const learningMessages = [
    { id: 1, type: 'user', content: 'I want to learn about vocabulary building' },
    { id: 2, type: 'ai', content: 'Great! Let\'s explore vocabulary building techniques.' }
  ];
  const learningSession = simpleStorageService.saveChatSession(learningMessages);
  console.log('✅ 学习类标题:', learningSession.title);
  
  // 测试用例4: 练习类对话
  console.log('🎯 测试用例4: 练习类对话');
  const practiceMessages = [
    { id: 1, type: 'user', content: 'Let\'s practice pronunciation together' },
    { id: 2, type: 'ai', content: 'Excellent! Let\'s work on your pronunciation.' }
  ];
  const practiceSession = simpleStorageService.saveChatSession(practiceMessages);
  console.log('✅ 练习类标题:', practiceSession.title);
  
  // 测试用例5: 日记类对话
  console.log('📖 测试用例5: 日记类对话');
  const diaryMessages = [
    { id: 1, type: 'user', content: 'I want to discuss my diary entry from yesterday' },
    { id: 2, type: 'ai', content: 'I\'d love to hear about your diary entry!' }
  ];
  const diarySession = simpleStorageService.saveChatSession(diaryMessages);
  console.log('✅ 日记类标题:', diarySession.title);
  
  // 测试用例6: 中文对话
  console.log('🇨🇳 测试用例6: 中文对话');
  const chineseMessages = [
    { id: 1, type: 'user', content: '我想学习英语语法规则' },
    { id: 2, type: 'ai', content: '好的！我来帮你学习英语语法规则。' }
  ];
  const chineseSession = simpleStorageService.saveChatSession(chineseMessages);
  console.log('✅ 中文标题:', chineseSession.title);
  
  // 测试用例7: 长内容截取
  console.log('📝 测试用例7: 长内容截取');
  const longMessages = [
    { id: 1, type: 'user', content: 'This is a very long message that should be truncated because it exceeds the maximum length limit for titles' },
    { id: 2, type: 'ai', content: 'I understand your long message.' }
  ];
  const longSession = simpleStorageService.saveChatSession(longMessages);
  console.log('✅ 长内容标题:', longSession.title);
  
  // 测试用例8: 短内容
  console.log('💬 测试用例8: 短内容');
  const shortMessages = [
    { id: 1, type: 'user', content: 'Hi' },
    { id: 2, type: 'ai', content: 'Hello!' }
  ];
  const shortSession = simpleStorageService.saveChatSession(shortMessages);
  console.log('✅ 短内容标题:', shortSession.title);
  
  // 测试用例9: 空消息
  console.log('❌ 测试用例9: 空消息');
  const emptyMessages = [];
  const emptySession = simpleStorageService.saveChatSession(emptyMessages);
  console.log('✅ 空消息标题:', emptySession.title);
  
  // 测试用例10: 只有AI消息
  console.log('🤖 测试用例10: 只有AI消息');
  const aiOnlyMessages = [
    { id: 1, type: 'ai', content: 'Hello! How can I help you today?' }
  ];
  const aiOnlySession = simpleStorageService.saveChatSession(aiOnlyMessages);
  console.log('✅ 只有AI消息标题:', aiOnlySession.title);
  
  // 测试用例11: 翻译类对话
  console.log('🔄 测试用例11: 翻译类对话');
  const translateMessages = [
    { id: 1, type: 'user', content: 'Can you translate this sentence for me?' },
    { id: 2, type: 'ai', content: 'Of course! What sentence would you like me to translate?' }
  ];
  const translateSession = simpleStorageService.saveChatSession(translateMessages);
  console.log('✅ 翻译类标题:', translateSession.title);
  
  // 测试用例12: 听力类对话
  console.log('👂 测试用例12: 听力类对话');
  const listeningMessages = [
    { id: 1, type: 'user', content: 'I want to improve my listening skills' },
    { id: 2, type: 'ai', content: 'Great! Let\'s work on your listening skills together.' }
  ];
  const listeningSession = simpleStorageService.saveChatSession(listeningMessages);
  console.log('✅ 听力类标题:', listeningSession.title);
  
  // 测试用例13: 阅读类对话
  console.log('📖 测试用例13: 阅读类对话');
  const readingMessages = [
    { id: 1, type: 'user', content: 'I need help with reading comprehension' },
    { id: 2, type: 'ai', content: 'I\'d be happy to help you with reading comprehension!' }
  ];
  const readingSession = simpleStorageService.saveChatSession(readingMessages);
  console.log('✅ 阅读类标题:', readingSession.title);
  
  // 测试用例14: 考试类对话
  console.log('📋 测试用例14: 考试类对话');
  const examMessages = [
    { id: 1, type: 'user', content: 'I have an English exam tomorrow' },
    { id: 2, type: 'ai', content: 'Good luck! Let me help you prepare for your exam.' }
  ];
  const examSession = simpleStorageService.saveChatSession(examMessages);
  console.log('✅ 考试类标题:', examSession.title);
  
  // 测试用例15: 复杂中文对话
  console.log('🇨🇳 测试用例15: 复杂中文对话');
  const complexChineseMessages = [
    { id: 1, type: 'user', content: '我想学习英语语法中的时态变化规则' },
    { id: 2, type: 'ai', content: '好的！时态是英语语法中很重要的部分。' }
  ];
  const complexChineseSession = simpleStorageService.saveChatSession(complexChineseMessages);
  console.log('✅ 复杂中文标题:', complexChineseSession.title);
  
  // 显示所有聊天历史
  console.log('📋 所有聊天历史:');
  const allChats = simpleStorageService.getChatHistory();
  allChats.forEach((chat, index) => {
    console.log(`${index + 1}. ${chat.title} (${chat.messageCount} 条消息)`);
  });
  
  console.log('🎉 智能聊天标题生成功能测试完成！');
  
  // 清理测试数据
  simpleStorageService.cleanup();
  console.log('🧹 测试数据已清理');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testSmartChatTitles = testSmartChatTitles;
}
