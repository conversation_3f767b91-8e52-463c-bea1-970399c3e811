import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import DictionaryModal from './DictionaryModal';

// Mock DictionaryLookup component
vi.mock('./DictionaryLookup', () => ({
  default: ({ word, isDarkMode }) => (
    <div data-testid="dictionary-lookup">
      Dictionary Lookup for: {word} (Dark: {isDarkMode ? 'Yes' : 'No'})
    </div>
  )
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  X: () => <div data-testid="close-icon">X</div>
}));

describe('DictionaryModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    word: 'test',
    isDarkMode: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该渲染模态框当isOpen为true时', () => {
    render(<DictionaryModal {...defaultProps} />);
    
    expect(screen.getByText('词典查询')).toBeInTheDocument();
    expect(screen.getByTestId('dictionary-lookup')).toBeInTheDocument();
  });

  it('应该不渲染模态框当isOpen为false时', () => {
    render(<DictionaryModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('词典查询')).not.toBeInTheDocument();
    expect(screen.queryByTestId('dictionary-lookup')).not.toBeInTheDocument();
  });

  it('应该渲染关闭按钮', () => {
    render(<DictionaryModal {...defaultProps} />);
    
    const closeButton = screen.getByTestId('close-icon').closest('button');
    expect(closeButton).toBeInTheDocument();
  });

  it('应该调用onClose当点击关闭按钮时', () => {
    render(<DictionaryModal {...defaultProps} />);
    
    const closeButton = screen.getByTestId('close-icon').closest('button');
    fireEvent.click(closeButton);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('应该调用onClose当点击背景遮罩时', () => {
    render(<DictionaryModal {...defaultProps} />);
    
    const backdrop = screen.getByText('词典查询').closest('div').parentElement.parentElement;
    fireEvent.click(backdrop);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('应该不调用onClose当点击模态框内容时', () => {
    render(<DictionaryModal {...defaultProps} />);
    
    const modalContent = screen.getByText('词典查询').closest('div');
    fireEvent.click(modalContent);
    
    expect(defaultProps.onClose).not.toHaveBeenCalled();
  });

  it('应该传递正确的props给DictionaryLookup', () => {
    render(<DictionaryModal {...defaultProps} word="hello" isDarkMode={true} />);
    
    expect(screen.getByTestId('dictionary-lookup')).toHaveTextContent('hello');
    expect(screen.getByTestId('dictionary-lookup')).toHaveTextContent('Dark: Yes');
  });

  it('应该传递浅色模式给DictionaryLookup', () => {
    render(<DictionaryModal {...defaultProps} isDarkMode={false} />);
    
    expect(screen.getByTestId('dictionary-lookup')).toHaveTextContent('Dark: No');
  });

  it('应该渲染标题', () => {
    render(<DictionaryModal {...defaultProps} />);
    
    const title = screen.getByText('词典查询');
    expect(title).toBeInTheDocument();
    expect(title).toHaveStyle('font-family: Georgia, "Noto Serif SC", serif');
  });

  it('应该应用正确的样式类', () => {
    render(<DictionaryModal {...defaultProps} />);
    
    const modal = screen.getByText('词典查询').closest('div').parentElement;
    expect(modal).toHaveClass('rounded-2xl', 'max-w-2xl', 'w-full', 'mx-8');
  });
});
