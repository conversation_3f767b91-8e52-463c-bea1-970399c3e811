/**
 * 测试前端组件数据渲染
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试前端组件数据渲染
export const testFrontendRendering = async () => {
  console.log('🧪 开始测试前端组件数据渲染...');
  
  // 模拟用户登录baocunluoji 
  const testUserId = 'test_user_' + Date.now();
  simpleStorageService.init(testUserId);
  
  console.log('👤 测试用户ID:', testUserId);
  
  // 测试1: 创建聊天历史数据
  console.log('💬 测试1: 创建聊天历史数据...');
  const chatMessages1 = [
    { id: 1, type: 'user', content: 'Can you help me with English grammar?' },
    { id: 2, type: 'ai', content: 'Of course! I\'d be happy to help you with English grammar.' }
  ];
  const chatSession1 = simpleStorageService.saveChatSession(chatMessages1, 'Grammar Help');
  console.log('✅ 聊天会话1创建成功:', chatSession1);
  
  const chatMessages2 = [
    { id: 1, type: 'user', content: 'I want to write a story about my childhood' },
    { id: 2, type: 'ai', content: 'That sounds wonderful! Let me help you with your story.' }
  ];
  const chatSession2 = simpleStorageService.saveChatSession(chatMessages2, 'Story Writing');
  console.log('✅ 聊天会话2创建成功:', chatSession2);
  
  // 测试2: 创建AI分析历史数据
  console.log('📝 测试2: 创建AI分析历史数据...');
  const analysis1 = simpleStorageService.saveAnalysis(
    'This is a test text for analysis',
    'Raw analysis result',
    { suggestions: ['suggestion1', 'suggestion2'], score: 85 }
  );
  console.log('✅ 分析记录1创建成功:', analysis1);
  
  const analysis2 = simpleStorageService.saveAnalysis(
    'Another text for analysis',
    'Another raw analysis result',
    { suggestions: ['suggestion3', 'suggestion4'], score: 90 }
  );
  console.log('✅ 分析记录2创建成功:', analysis2);
  
  // 测试3: 验证聊天历史数据结构
  console.log('🔍 测试3: 验证聊天历史数据结构...');
  const chatHistory = simpleStorageService.getChatHistory();
  console.log('📋 聊天历史记录数量:', chatHistory.length);
  
  chatHistory.forEach((session, index) => {
    console.log(`\n📄 聊天会话 ${index + 1}:`);
    console.log('  - ID:', session.id);
    console.log('  - 标题:', session.title);
    console.log('  - 消息数量:', session.messageCount);
    console.log('  - 时间戳:', session.timestamp);
    console.log('  - 用户ID:', session.userId);
    console.log('  - 消息数据:', session.messages ? session.messages.length : 0, '条');
    
    // 验证必要字段
    const requiredFields = ['id', 'title', 'messageCount', 'timestamp', 'userId'];
    const missingFields = requiredFields.filter(field => !session[field]);
    if (missingFields.length > 0) {
      console.warn('⚠️ 缺少必要字段:', missingFields);
    } else {
      console.log('✅ 数据结构完整');
    }
  });
  
  // 测试4: 验证AI分析历史数据结构
  console.log('\n🔍 测试4: 验证AI分析历史数据结构...');
  const analysisHistory = simpleStorageService.getAnalysisHistory();
  console.log('📋 AI分析历史记录数量:', analysisHistory.length);
  
  analysisHistory.forEach((record, index) => {
    console.log(`\n📄 分析记录 ${index + 1}:`);
    console.log('  - ID:', record.id);
    console.log('  - 文本:', record.text ? record.text.substring(0, 50) + '...' : 'N/A');
    console.log('  - 原始分析:', record.rawAnalysis ? record.rawAnalysis.substring(0, 50) + '...' : 'N/A');
    console.log('  - 分析结果:', record.analysis ? '有' : '无');
    console.log('  - 时间戳:', record.timestamp);
    console.log('  - 用户ID:', record.userId);
    
    // 验证必要字段
    const requiredFields = ['id', 'text', 'rawAnalysis', 'analysis', 'timestamp', 'userId'];
    const missingFields = requiredFields.filter(field => !record[field]);
    if (missingFields.length > 0) {
      console.warn('⚠️ 缺少必要字段:', missingFields);
    } else {
      console.log('✅ 数据结构完整');
    }
  });
  
  // 测试5: 模拟前端组件渲染
  console.log('\n🎨 测试5: 模拟前端组件渲染...');
  
  // 模拟ChatHistoryModal渲染
  console.log('💬 模拟ChatHistoryModal渲染:');
  chatHistory.forEach((session, index) => {
    const displayTitle = session.title || session.sessionTitle || '未命名对话';
    const displayMessageCount = session.messageCount || (session.messages ? session.messages.length : 0);
    const displayDate = new Date(session.timestamp).toLocaleDateString('zh-CN');
    
    console.log(`  ${index + 1}. ${displayTitle} (${displayMessageCount} 条消息) - ${displayDate}`);
  });
  
  // 模拟HistoryModal渲染
  console.log('\n📝 模拟HistoryModal渲染:');
  analysisHistory.forEach((record, index) => {
    const displayText = record.text ? record.text.substring(0, 30) + '...' : '无文本';
    const displayDate = new Date(record.timestamp).toLocaleDateString('zh-CN');
    
    console.log(`  ${index + 1}. ${displayText} - ${displayDate}`);
  });
  
  // 测试6: 验证数据兼容性
  console.log('\n🔧 测试6: 验证数据兼容性...');
  
  // 检查ChatHistoryModal需要的字段
  const chatCompatibility = chatHistory.every(session => {
    return session.title && 
           typeof session.messageCount === 'number' && 
           session.timestamp &&
           session.id;
  });
  console.log('✅ ChatHistoryModal数据兼容性:', chatCompatibility ? '通过' : '失败');
  
  // 检查HistoryModal需要的字段
  const analysisCompatibility = analysisHistory.every(record => {
    return record.text && 
           record.rawAnalysis && 
           record.analysis &&
           record.timestamp &&
           record.id;
  });
  console.log('✅ HistoryModal数据兼容性:', analysisCompatibility ? '通过' : '失败');
  
  // 测试7: 测试数据更新
  console.log('\n🔄 测试7: 测试数据更新...');
  
  // 更新聊天会话标题
  const updatedSession = { ...chatSession1, title: 'Updated Grammar Help' };
  simpleStorageService.saveChatSession(updatedSession.messages, updatedSession.title);
  
  // 验证更新
  const updatedHistory = simpleStorageService.getChatHistory();
  const foundUpdated = updatedHistory.find(s => s.id === chatSession1.id);
  console.log('✅ 聊天会话更新测试:', foundUpdated ? '成功' : '失败');
  
  // 最终统计
  const finalStats = simpleStorageService.getStats();
  console.log('\n📊 最终统计:', finalStats);
  
  console.log('🎉 前端组件数据渲染测试完成！');
  
  // 清理测试数据
  simpleStorageService.cleanup();
  console.log('🧹 测试数据已清理');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testFrontendRendering = testFrontendRendering;
}
