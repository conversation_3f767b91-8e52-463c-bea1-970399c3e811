import { describe, it, expect } from 'vitest';
import { 
  themeColors, 
  getThemeColors, 
  createThemeStyles, 
  useThemeStyles, 
  themeStyle, 
  createHoverHandlers, 
  commonStyles 
} from './themeUtils';

describe('themeUtils', () => {
  describe('themeColors', () => {
    it('应该包含浅色和深色主题配置', () => {
      expect(themeColors).toHaveProperty('light');
      expect(themeColors).toHaveProperty('dark');
      
      expect(themeColors.light).toHaveProperty('bg');
      expect(themeColors.light).toHaveProperty('text');
      expect(themeColors.light).toHaveProperty('border');
      expect(themeColors.light).toHaveProperty('accent');
      expect(themeColors.light).toHaveProperty('suggestion');
      
      expect(themeColors.dark).toHaveProperty('bg');
      expect(themeColors.dark).toHaveProperty('text');
      expect(themeColors.dark).toHaveProperty('border');
      expect(themeColors.dark).toHaveProperty('accent');
      expect(themeColors.dark).toHaveProperty('suggestion');
    });

    it('应该包含完整的颜色配置', () => {
      const lightTheme = themeColors.light;
      
      expect(lightTheme.bg).toHaveProperty('primary');
      expect(lightTheme.bg).toHaveProperty('surface');
      expect(lightTheme.bg).toHaveProperty('card');
      expect(lightTheme.bg).toHaveProperty('header');
      expect(lightTheme.bg).toHaveProperty('button');
      expect(lightTheme.bg).toHaveProperty('input');
      expect(lightTheme.bg).toHaveProperty('inputFocus');
      
      expect(lightTheme.text).toHaveProperty('primary');
      expect(lightTheme.text).toHaveProperty('secondary');
      expect(lightTheme.text).toHaveProperty('muted');
      expect(lightTheme.text).toHaveProperty('light');
      
      expect(lightTheme.accent).toHaveProperty('red');
      expect(lightTheme.accent).toHaveProperty('redHover');
      expect(lightTheme.accent).toHaveProperty('green');
      expect(lightTheme.accent).toHaveProperty('greenHover');
      expect(lightTheme.accent).toHaveProperty('blue');
      expect(lightTheme.accent).toHaveProperty('yellow');
      
      expect(lightTheme.suggestion).toHaveProperty('grammar');
      expect(lightTheme.suggestion).toHaveProperty('style');
      expect(lightTheme.suggestion).toHaveProperty('clarity');
    });
  });

  describe('getThemeColors', () => {
    it('应该返回深色主题颜色', () => {
      const colors = getThemeColors(true);
      expect(colors).toBe(themeColors.dark);
    });

    it('应该返回浅色主题颜色', () => {
      const colors = getThemeColors(false);
      expect(colors).toBe(themeColors.light);
    });
  });

  describe('createThemeStyles', () => {
    it('应该为浅色主题创建样式', () => {
      const styles = createThemeStyles(false);
      
      expect(styles).toHaveProperty('page');
      expect(styles).toHaveProperty('card');
      expect(styles).toHaveProperty('button');
      expect(styles).toHaveProperty('input');
      expect(styles).toHaveProperty('modal');
      expect(styles).toHaveProperty('suggestion');
      
      expect(styles.page.backgroundColor).toBe(themeColors.light.bg.primary);
      expect(styles.page.color).toBe(themeColors.light.text.primary);
    });

    it('应该为深色主题创建样式', () => {
      const styles = createThemeStyles(true);
      
      expect(styles.page.backgroundColor).toBe(themeColors.dark.bg.primary);
      expect(styles.page.color).toBe(themeColors.dark.text.primary);
    });

    it('应该包含按钮样式', () => {
      const styles = createThemeStyles(false);
      
      expect(styles.button).toHaveProperty('primary');
      expect(styles.button).toHaveProperty('secondary');
      expect(styles.button).toHaveProperty('success');
      
      expect(styles.button.primary.backgroundColor).toBe(themeColors.light.accent.red);
      expect(styles.button.primary.color).toBe(themeColors.light.text.light);
    });

    it('应该包含建议高亮样式', () => {
      const styles = createThemeStyles(false);
      
      expect(styles.suggestion).toHaveProperty('grammar');
      expect(styles.suggestion).toHaveProperty('style');
      expect(styles.suggestion).toHaveProperty('clarity');
      
      expect(styles.suggestion.grammar.borderBottom).toContain(themeColors.light.suggestion.grammar.border);
      expect(styles.suggestion.grammar.backgroundColor).toBe(themeColors.light.suggestion.grammar.bg);
    });
  });

  describe('useThemeStyles', () => {
    it('应该返回主题样式', () => {
      const styles = useThemeStyles(false);
      expect(styles).toEqual(createThemeStyles(false));
    });
  });

  describe('themeStyle', () => {
    it('应该返回基础样式', () => {
      const style = themeStyle(false, 'page');
      expect(style).toEqual(createThemeStyles(false).page);
    });

    it('应该合并自定义样式', () => {
      const customStyles = { fontSize: '18px' };
      const style = themeStyle(false, 'page', customStyles);
      
      expect(style).toEqual({
        ...createThemeStyles(false).page,
        ...customStyles
      });
    });

    it('应该处理嵌套样式路径', () => {
      const style = themeStyle(false, 'button.primary');
      expect(style).toEqual(createThemeStyles(false).button.primary);
    });
  });

  describe('createHoverHandlers', () => {
    it('应该创建悬停事件处理器', () => {
      const baseStyle = { backgroundColor: 'red' };
      const hoverStyle = { backgroundColor: 'blue' };
      const handlers = createHoverHandlers(false, baseStyle, hoverStyle);
      
      expect(handlers).toHaveProperty('onMouseEnter');
      expect(handlers).toHaveProperty('onMouseLeave');
      expect(typeof handlers.onMouseEnter).toBe('function');
      expect(typeof handlers.onMouseLeave).toBe('function');
    });

    it('应该正确处理鼠标进入事件', () => {
      const baseStyle = { backgroundColor: 'red' };
      const hoverStyle = { backgroundColor: 'blue' };
      const handlers = createHoverHandlers(false, baseStyle, hoverStyle);
      
      const mockEvent = {
        target: {
          style: {}
        }
      };
      
      handlers.onMouseEnter(mockEvent);
      expect(mockEvent.target.style.backgroundColor).toBe('blue');
    });

    it('应该正确处理鼠标离开事件', () => {
      const baseStyle = { backgroundColor: 'red' };
      const hoverStyle = { backgroundColor: 'blue' };
      const handlers = createHoverHandlers(false, baseStyle, hoverStyle);
      
      const mockEvent = {
        target: {
          style: {}
        }
      };
      
      handlers.onMouseLeave(mockEvent);
      expect(mockEvent.target.style.backgroundColor).toBe('red');
    });
  });

  describe('commonStyles', () => {
    describe('roundButton', () => {
      it('应该创建圆角按钮样式', () => {
        const style = commonStyles.roundButton(false, 60);
        
        expect(style.width).toBe('60px');
        expect(style.height).toBe('60px');
        expect(style.borderRadius).toBe('12px');
        expect(style.backgroundColor).toBe(themeColors.light.bg.button);
        expect(style.color).toBe(themeColors.light.text.secondary);
        expect(style.cursor).toBe('pointer');
      });

      it('应该使用默认尺寸', () => {
        const style = commonStyles.roundButton(false);
        expect(style.width).toBe('48px');
        expect(style.height).toBe('48px');
      });
    });

    describe('textInput', () => {
      it('应该创建文本输入框样式', () => {
        const style = commonStyles.textInput(false);
        
        expect(style.backgroundColor).toBe(themeColors.light.bg.input);
        expect(style.color).toBe(themeColors.light.text.primary);
        expect(style.border).toContain(themeColors.light.border.primary);
        expect(style.borderRadius).toBe('12px');
        expect(style.padding).toBe('16px 20px');
        expect(style.fontSize).toBe('16px');
      });
    });

    describe('card', () => {
      it('应该创建卡片样式', () => {
        const style = commonStyles.card(false);
        
        expect(style.backgroundColor).toBe(themeColors.light.bg.surface);
        expect(style.color).toBe(themeColors.light.text.primary);
        expect(style.border).toContain(themeColors.light.border.primary);
        expect(style.borderRadius).toBe('16px');
      });
    });
  });
});
