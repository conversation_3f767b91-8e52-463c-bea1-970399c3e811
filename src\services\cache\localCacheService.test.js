import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import localCacheService, { CACHE_CONFIG, CACHE_KEYS } from './localCacheService';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

// Mock hybrid services
vi.mock('../history/hybridHistoryService', () => ({
  hybridChatHistoryService: {
    getChatHistory: vi.fn()
  },
  hybridAnalysisService: {
    getAnalysisHistory: vi.fn()
  },
  hybridDictionarySearchService: {
    getSearchHistory: vi.fn()
  },
  hybridWritingHistoryService: {
    getWritingHistory: vi.fn()
  },
  hybridDiaryHistoryService: {
    getDiaryHistory: vi.fn()
  },
  getStorageStatus: vi.fn(() => ({ canUseFirebase: true }))
}));

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true
});

describe('LocalCacheService', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });
    
    // Reset service state
    localCacheService.syncInProgress = false;
    localCacheService.syncCallbacks.clear();
  });

  afterEach(() => {
    // Clean up timers
    if (localCacheService.backgroundSyncTimer) {
      clearInterval(localCacheService.backgroundSyncTimer);
      localCacheService.backgroundSyncTimer = null;
    }
  });

  describe('缓存基础功能', () => {
    it('应该正确设置和获取缓存', () => {
      const testData = { message: 'test' };
      const testKey = 'test_key';
      
      localCacheService.setCache(testKey, testData);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        testKey, 
        expect.stringContaining('{"data":{"message":"test"},"timestamp":')
      );
    });

    it('应该从缓存获取有效数据', () => {
      const testData = { message: 'test' };
      const testKey = 'test_key';
      const mockCacheItem = {
        data: testData,
        timestamp: Date.now(),
        version: 1
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = localCacheService.getCache(testKey, 60000);
      
      expect(result).toEqual(testData);
      expect(localStorageMock.getItem).toHaveBeenCalledWith(testKey);
    });

    it('应该返回null当缓存过期时', () => {
      const testKey = 'test_key';
      const expiredCacheItem = {
        data: { message: 'test' },
        timestamp: Date.now() - 120000, // 2分钟前
        version: 1
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredCacheItem));
      
      const result = localCacheService.getCache(testKey, 60000); // 1分钟过期
      
      expect(result).toBeNull();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(testKey);
    });

    it('应该返回null当缓存不存在时', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const result = localCacheService.getCache('nonexistent', 60000);
      
      expect(result).toBeNull();
    });

    it('应该清除指定缓存', () => {
      localCacheService.clearCache('test_key');
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test_key');
    });

    it('应该清除所有缓存', () => {
      localCacheService.clearAllCache();
      
      Object.values(CACHE_KEYS).forEach(key => {
        expect(localStorageMock.removeItem).toHaveBeenCalledWith(key);
      });
    });
  });

  describe('聊天历史缓存', () => {
    it('应该从缓存获取聊天历史', async () => {
      const mockHistory = [
        { id: '1', message: 'Hello' },
        { id: '2', message: 'World' }
      ];
      const mockCacheItem = {
        data: mockHistory,
        timestamp: Date.now(),
        version: 1
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = await localCacheService.getChatHistory(10, false);
      
      expect(result).toEqual(mockHistory.slice(0, 10));
    });

    it('应该从远程获取聊天历史当缓存过期时', async () => {
      const { hybridChatHistoryService } = await import('../history/hybridHistoryService');
      const mockHistory = [{ id: '1', message: 'Hello' }];
      
      hybridChatHistoryService.getChatHistory.mockResolvedValue(mockHistory);
      localStorageMock.getItem.mockReturnValue(null);
      
      const result = await localCacheService.getChatHistory(10, false);
      
      expect(hybridChatHistoryService.getChatHistory).toHaveBeenCalledWith(10);
      expect(result).toEqual(mockHistory);
    });

    it('应该强制刷新时跳过缓存', async () => {
      const { hybridChatHistoryService } = await import('../history/hybridHistoryService');
      const mockHistory = [{ id: '1', message: 'Hello' }];
      
      hybridChatHistoryService.getChatHistory.mockResolvedValue(mockHistory);
      
      const result = await localCacheService.getChatHistory(10, true);
      
      expect(hybridChatHistoryService.getChatHistory).toHaveBeenCalledWith(10);
      expect(result).toEqual(mockHistory);
    });
  });

  describe('数据保存功能', () => {
    it('应该保存聊天数据', async () => {
      const mockSessionData = { id: '1', message: 'Hello' };
      
      // 由于 saveData 依赖外部服务，我们只测试错误处理
      await expect(localCacheService.saveData('chat', mockSessionData)).rejects.toThrow();
    });

    it('应该保存分析数据', async () => {
      const mockAnalysisData = { id: '1', analysis: 'Good' };
      
      // 由于 saveData 依赖外部服务，我们只测试错误处理
      await expect(localCacheService.saveData('analysis', mockAnalysisData)).rejects.toThrow();
    });

    it('应该抛出错误当数据类型未知时', async () => {
      await expect(localCacheService.saveData('unknown', {})).rejects.toThrow('未知的数据类型: unknown');
    });
  });

  describe('缓存失效功能', () => {
    it('应该使指定类型的缓存失效', () => {
      localCacheService.invalidateCache('chat');
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(CACHE_KEYS.CHAT_HISTORY);
    });

    it('应该更新最后同步时间', () => {
      const mockLastSync = { chat: 1234567890 };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockLastSync));
      
      localCacheService.updateLastSync('analysis');
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        CACHE_KEYS.LAST_SYNC,
        expect.stringContaining('"analysis"')
      );
    });

    it('应该获取最后同步时间', () => {
      const mockLastSync = { chat: 1234567890, analysis: 1234567891 };
      const mockCacheItem = {
        data: mockLastSync,
        timestamp: Date.now(),
        version: 1
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = localCacheService.getLastSync('chat');
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('cache_last_sync');
      expect(result).toBe(1234567890);
    });
  });

  describe('同步功能', () => {
    it('应该启动后台同步定时器', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      
      localCacheService.startBackgroundSync();
      
      expect(setIntervalSpy).toHaveBeenCalled();
      expect(localCacheService.backgroundSyncTimer).toBeTruthy();
    });

    it('应该停止后台同步定时器', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      localCacheService.backgroundSyncTimer = setInterval(() => {}, 1000);
      
      localCacheService.stopBackgroundSync();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(localCacheService.backgroundSyncTimer).toBeNull();
    });

    it('应该添加和移除同步监听器', () => {
      const callback = vi.fn();
      
      localCacheService.addSyncListener(callback);
      expect(localCacheService.syncCallbacks.has(callback)).toBe(true);
      
      localCacheService.removeSyncListener(callback);
      expect(localCacheService.syncCallbacks.has(callback)).toBe(false);
    });
  });

  describe('缓存统计功能', () => {
    it('应该返回缓存统计信息', () => {
      const mockData1 = '{"data":"test1"}';
      const mockData2 = '{"data":"test2"}';
      
      localStorageMock.getItem
        .mockReturnValueOnce(mockData1) // CACHE_KEYS.CHAT_HISTORY
        .mockReturnValueOnce(mockData2) // CACHE_KEYS.ANALYSIS_HISTORY
        .mockReturnValueOnce(null)      // CACHE_KEYS.DICTIONARY_SEARCH
        .mockReturnValueOnce(null)      // CACHE_KEYS.WRITING_HISTORY
        .mockReturnValueOnce(null)      // CACHE_KEYS.DIARY_HISTORY
        .mockReturnValueOnce(null)      // CACHE_KEYS.LAST_SYNC
        .mockReturnValueOnce(null);     // CACHE_KEYS.SYNC_STATUS
      
      const stats = localCacheService.getCacheStats();
      
      expect(stats.totalSize).toBeGreaterThan(0);
      expect(stats.itemCount).toBe(2);
      expect(stats.isOnline).toBe(true);
      expect(stats.syncInProgress).toBe(false);
    });
  });

  describe('错误处理', () => {
    it('应该处理localStorage错误', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      const result = localCacheService.getCache('test', 60000);
      
      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '获取缓存失败 (test):',
        expect.any(Error)
      );
      
      consoleErrorSpy.mockRestore();
    });

    it('应该处理设置缓存错误', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      localCacheService.setCache('test', { data: 'test' });
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '设置缓存失败 (test):',
        expect.any(Error)
      );
      
      consoleErrorSpy.mockRestore();
    });
  });
});
