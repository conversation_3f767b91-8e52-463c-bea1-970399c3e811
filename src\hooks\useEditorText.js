import { useState, useEffect } from 'react';
import { getWritingText, getWritingSuggestions, autoSaveWritingText, clearWritingText } from '../services/writing/writingTextService';

/**
 * 编辑器文本管理Hook
 * 处理文本状态、建议状态和自动保存
 */
export const useEditorText = (setSuggestions) => {
  // 内部状态 - 从localStorage恢复文本
  const [text, setText] = useState(() => getWritingText());
  const [hoveredSuggestion, setHoveredSuggestion] = useState(null);
  const [activeBubble, setActiveBubble] = useState(null);
  const [bubblePosition, setBubblePosition] = useState({ x: 0, y: 0 });

  // 组件挂载时恢复保存的建议
  useEffect(() => {
    const savedSuggestions = getWritingSuggestions();
    if (savedSuggestions.length > 0) {
      setSuggestions(savedSuggestions);
    }
  }, [setSuggestions]);

  // 处理文本变化
  const handleTextChange = (newText, suggestions) => {
    setText(newText);
    // 自动保存文本
    autoSaveWritingText(newText, suggestions);
    
    if (newText.trim().length < 10) {
      setSuggestions([]);
    }
  };

  // 当文本变化时保存文本（但不清空建议）
  useEffect(() => {
    if (text) {
      // 只保存文本，不清空建议
      const savedSuggestions = getWritingSuggestions();
      autoSaveWritingText(text, savedSuggestions);
    }
  }, [text]);

  // 处理新文档
  const handleNewDocument = (showConfirm) => {
    if (text.trim()) {
      showConfirm({
        title: '创建新文档',
        message: '确定要创建新文档吗？当前内容将被清除。',
        confirmText: '创建新文档',
        cancelText: '取消',
        type: 'warning',
        onConfirm: () => {
          setText('');
          setSuggestions([]);
          clearWritingText();
        }
      });
    } else {
      setText('');
      setSuggestions([]);
      clearWritingText();
    }
  };

  // 处理选择历史记录
  const handleSelectHistory = (historyRecord, closeModal) => {
    if (historyRecord) {
      setText(historyRecord.text);
      setSuggestions(historyRecord.suggestions || []);
    }
    closeModal();
  };

  return {
    text,
    setText,
    hoveredSuggestion,
    setHoveredSuggestion,
    activeBubble,
    setActiveBubble,
    bubblePosition,
    setBubblePosition,
    handleTextChange,
    handleNewDocument,
    handleSelectHistory
  };
};
