import React, { useState, useEffect } from 'react';
import { X, Calendar, Leaf, ChevronLeft, ChevronRight, Trash2 } from 'lucide-react';
import VoicePlayButton from './VoicePlayButton';
import { getDiaries, deleteDiary } from '../services/writing/diaryService';
import ConfirmDialog from './ConfirmDialog';
import { useConfirmDialog } from '../hooks/useConfirmDialog';

const DiaryHistoryModal = ({ isOpen, onClose, isDarkMode }) => {
  const [diaries, setDiaries] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showTranslation, setShowTranslation] = useState(false);
  const { dialogState, showConfirm, hideConfirm } = useConfirmDialog();

  useEffect(() => {
    if (isOpen) {
      loadDiaries();
    }
  }, [isOpen]);

  const loadDiaries = () => {
    const allDiaries = getDiaries();
    setDiaries(allDiaries);
    setCurrentIndex(0);
    setShowTranslation(false);
  };

  const handleDelete = (diaryId) => {
    showConfirm({
      title: '删除日记',
      message: '确定要删除这篇日记吗？',
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: () => {
        try {
          deleteDiary(diaryId);
          loadDiaries();
        } catch (error) {
          console.error('删除日记失败:', error);
          alert('删除失败，请重试');
        }
      }
    });
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setShowTranslation(false);
    }
  };

  const handleNext = () => {
    if (currentIndex < diaries.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setShowTranslation(false);
    }
  };

  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天';
    } else {
      return date.toLocaleDateString('zh-CN', { 
        year: 'numeric',
        month: 'long', 
        day: 'numeric',
        weekday: 'short'
      });
    }
  };

  if (!isOpen) return null;

  const currentDiary = diaries[currentIndex];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 transition-opacity duration-300"
        style={{ backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.8)' : 'rgba(93, 64, 55, 0.6)' }}
        onClick={onClose}
      />
      
      {/* 模态框内容 */}
      <div 
        className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden rounded-2xl border transition-all duration-300"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#F9F7F4',
          borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
          boxShadow: isDarkMode 
            ? '0 20px 40px rgba(0, 0, 0, 0.5)' 
            : '0 20px 40px rgba(93, 64, 55, 0.3)'
        }}
      >
        {/* 头部 */}
        <div 
          className="flex items-center justify-between p-6 border-b"
          style={{ borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8' }}
        >
          <div className="flex items-center gap-3">
            <div 
              className="flex items-center justify-center w-10 h-10 rounded-xl"
              style={{ backgroundColor: isDarkMode ? '#D2691E' : '#166534' }}
            >
              <Leaf className="w-5 h-5" style={{ color: '#FEFCF5' }} />
            </div>
            <div>
              <h2 
                className="text-2xl font-bold"
                style={{
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                Alex's Journal History
              </h2>
              <p 
                className="text-sm"
                style={{
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                {diaries.length > 0 ? `共 ${diaries.length} 篇日记` : '暂无日记记录'}
              </p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 rounded-lg transition-colors duration-200"
            style={{
              color: isDarkMode ? '#C4B59A' : '#8B4513',
              backgroundColor: 'transparent'
            }}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto custom-scrollbar p-6">
          {diaries.length === 0 ? (
            /* 空状态 */
            <div 
              className="text-center py-16"
              style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
            >
              <Leaf className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-xl mb-2">还没有日记记录</p>
              <p className="opacity-75">Alex还没有开始写日记，快去生成第一篇吧！</p>
            </div>
          ) : (
            /* 日记内容 */
            <div className="space-y-6">
              {/* 导航栏 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={handlePrev}
                    disabled={currentIndex === 0}
                    className="p-2 rounded-lg transition-all duration-200 disabled:opacity-50"
                    style={{
                      color: isDarkMode ? '#C4B59A' : '#8B4513',
                      backgroundColor: 'transparent'
                    }}
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  
                  <span 
                    className="font-medium"
                    style={{
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif'
                    }}
                  >
                    {currentIndex + 1} / {diaries.length}
                  </span>
                  
                  <button
                    onClick={handleNext}
                    disabled={currentIndex === diaries.length - 1}
                    className="p-2 rounded-lg transition-all duration-200 disabled:opacity-50"
                    style={{
                      color: isDarkMode ? '#C4B59A' : '#8B4513',
                      backgroundColor: 'transparent'
                    }}
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>

                {/* 删除按钮 */}
                <button
                  onClick={() => handleDelete(currentDiary.id)}
                  className="p-2 rounded-lg transition-colors duration-200"
                  style={{
                    color: isDarkMode ? '#F87171' : '#DC2626',
                    backgroundColor: 'transparent'
                  }}
                  title="删除这篇日记"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>

              {/* 当前日记 */}
              {currentDiary && (
                <div className="space-y-4">
                  {/* 日期 */}
                  <div className="flex items-center gap-3 pb-3 border-b" style={{
                    borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8'
                  }}>
                    <Calendar className="w-5 h-5" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
                    <span 
                      className="font-medium"
                      style={{
                        color: isDarkMode ? '#E8DCC6' : '#5D4037',
                        fontFamily: 'Georgia, "Noto Serif SC", serif'
                      }}
                    >
                      {formatDate(currentDiary.date)} • {currentDiary.dateDisplay || new Date(currentDiary.date).toLocaleDateString('zh-CN')}
                    </span>
                    {currentDiary.isFallback && (
                      <span 
                        className="text-xs px-2 py-1 rounded-full"
                        style={{
                          backgroundColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                          color: isDarkMode ? '#C4B59A' : '#8B4513'
                        }}
                      >
                        离线模式
                      </span>
                    )}
                  </div>

                  {/* 英文内容 */}
                  <div 
                    className="p-4 rounded-xl border"
                    style={{
                      backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
                      borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                      color: isDarkMode ? '#E8DCC6' : '#5D4037',
                      fontFamily: 'Georgia, "Noto Serif SC", serif',
                      lineHeight: '1.7',
                      fontSize: '16px'
                    }}
                  >
                    <div className="flex items-center gap-2 mb-3">
                      <span 
                        className="text-sm font-medium"
                        style={{ color: isDarkMode ? '#D2691E' : '#166534' }}
                      >
                        Today's Discovery
                      </span>
                      <VoicePlayButton
                        text={currentDiary.english}
                        isDarkMode={isDarkMode}
                        size="small"
                      />
                    </div>
                    <div>{currentDiary.english}</div>
                  </div>

                  {/* 中文翻译切换 */}
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => setShowTranslation(!showTranslation)}
                      className="flex items-center gap-2 text-sm transition-colors duration-200"
                      style={{
                        color: isDarkMode ? '#C4B59A' : '#8B4513'
                      }}
                    >
                      <span>{showTranslation ? '隐藏' : '显示'}中文翻译</span>
                      <ChevronRight 
                        className={`w-4 h-4 transition-transform duration-200 ${showTranslation ? 'rotate-90' : ''}`} 
                      />
                    </button>
                  </div>

                  {/* 中文翻译内容 */}
                  {showTranslation && (
                    <div 
                      className="p-4 rounded-xl border"
                      style={{
                        backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
                        borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
                        color: isDarkMode ? '#C4B59A' : '#8B4513',
                        fontFamily: 'Georgia, "Noto Serif SC", serif',
                        lineHeight: '1.7',
                        fontSize: '15px'
                      }}
                    >
                      <div className="flex items-center gap-2 mb-3">
                        <span 
                          className="text-sm font-medium"
                          style={{ color: isDarkMode ? '#D2691E' : '#166534' }}
                        >
                          中文翻译
                        </span>
                      </div>
                      <div>{currentDiary.chinese}</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 自定义确认对话框 */}
      <ConfirmDialog
        isOpen={dialogState.isOpen}
        onClose={hideConfirm}
        onConfirm={dialogState.onConfirm}
        title={dialogState.title}
        message={dialogState.message}
        confirmText={dialogState.confirmText}
        cancelText={dialogState.cancelText}
        type={dialogState.type}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

export default DiaryHistoryModal;