// ECDICT 修复验证工具
// 用于验证英文释义显示问题是否已修复

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试 "doing" 单词的完整显示
const testDoingWord = async () => {
  console.log('🧪 测试 "doing" 单词的完整显示...');
  console.log('='.repeat(50));
  
  try {
    const startTime = performance.now();
    const result = await getWordDetails('doing', 'ecdict');
    const endTime = performance.now();
    
    const queryTime = endTime - startTime;
    
    console.log(`⏱️ 查询时间: ${queryTime.toFixed(2)}ms`);
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log('📖 数据源:', result.source);
      console.log('🔤 单词:', result.word);
      
      if (result.phonetic) {
        console.log('🔊 音标:', result.phonetic);
      }
      
      // 检查配对释义
      if (result.definitionPairs && result.definitionPairs.length > 0) {
        console.log('🔗 配对释义:');
        result.definitionPairs.forEach((pair, index) => {
          console.log(`  ${pair.index}. 英文: ${pair.english}`);
          console.log(`    中文: ${pair.chinese}`);
        });
      } else {
        console.log('❌ 缺少配对释义数据');
        
        // 检查原始释义
        if (result.definition) {
          console.log('🇺🇸 原始英文释义:');
          console.log('  ', result.definition);
        }
        
        if (result.translation) {
          console.log('🇨🇳 原始中文释义:');
          console.log('  ', result.translation);
        }
      }
      
      // 检查其他信息
      if (result.tags && result.tags.length > 0) {
        console.log('🏷️ 考试标签:', result.tags.join(', '));
      }
      
      if (result.collins > 0) {
        console.log('⭐ 柯林斯星级:', result.collins);
      }
      
      if (result.oxford) {
        console.log('📚 牛津3000: 是');
      }
      
      if (result.bnc > 0) {
        console.log('📊 BNC词频:', result.bnc);
      }
      
      if (result.frq > 0) {
        console.log('📈 当代词频:', result.frq);
      }
      
      if (result.exchange && Object.keys(result.exchange).length > 0) {
        console.log('🔄 词形变化:', result.exchange);
      }
      
      // 检查UI布局数据
      console.log('\n🎨 UI布局数据:');
      console.log('  - 有英文释义:', !!result.definition);
      console.log('  - 有中文释义:', !!result.translation);
      console.log('  - 应该显示英汉字典布局:', result.source === 'ECDICT');
      console.log('  - 应该显示整合释义:', !!(result.definition || result.translation));
      
      return true;
    } else {
      console.log('❌ 查询失败');
      console.log('📝 错误信息:', result?.error || '未找到该单词');
      return false;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return false;
  }
};

// 测试其他单词
const testOtherWords = async () => {
  console.log('\n🧪 测试其他单词...');
  console.log('='.repeat(50));
  
  const testWords = ['school', 'nothing', 'beautiful', 'computer'];
  const results = {};
  
  for (const word of testWords) {
    try {
      const result = await getWordDetails(word, 'ecdict');
      const success = result && !result.notFound;
      
      console.log(`${success ? '✅' : '❌'} ${word}:`);
      if (success) {
        console.log(`  - 英文释义: ${result.definition ? '有' : '无'}`);
        console.log(`  - 中文释义: ${result.translation ? '有' : '无'}`);
        console.log(`  - 数据源: ${result.source}`);
      }
      
      results[word] = success;
    } catch (error) {
      console.log(`❌ ${word}: 查询出错 - ${error.message}`);
      results[word] = false;
    }
  }
  
  const successCount = Object.values(results).filter(r => r).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n📊 测试结果: ${successCount}/${totalCount} 成功`);
  
  return results;
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始 ECDICT 修复验证...');
  console.log('='.repeat(60));
  
  try {
    // 测试 "doing" 单词
    const doingResult = await testDoingWord();
    
    // 测试其他单词
    const otherResults = await testOtherWords();
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    if (doingResult) {
      console.log('✅ "doing" 单词显示修复成功！');
    } else {
      console.log('❌ "doing" 单词显示仍有问题');
    }
    
    return {
      doingResult,
      otherResults
    };
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testDoingWord,
  testOtherWords,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testEcdictFix = {
    testDoingWord,
    testOtherWords,
    runAllTests
  };
  
  console.log('🧪 ECDICT 修复验证工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testEcdictFix.runAllTests() // 运行所有测试');
  console.log('  - window.testEcdictFix.testDoingWord() // 测试 "doing" 单词');
  console.log('  - window.testEcdictFix.testOtherWords() // 测试其他单词');
}
