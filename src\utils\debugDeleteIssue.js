/**
 * 调试删除问题
 */

import { auth } from '../config/firebaseConfig';
import unifiedStorageService from '../services/storage/unifiedStorageService';

/**
 * 调试删除问题
 */
export const debugDeleteIssue = async () => {
  console.log('🔍 开始调试删除问题...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    console.log('👤 用户信息:', {
      uid: user.uid,
      email: user.email,
      emailVerified: user.emailVerified
    });
    
    console.log('🔧 统一存储服务状态:', {
      userId: unifiedStorageService.userId,
      isOnline: unifiedStorageService.isOnline,
      syncInProgress: unifiedStorageService.syncInProgress
    });
    
    // 检查用户ID是否匹配
    if (unifiedStorageService.userId !== user.uid) {
      console.log('⚠️ 用户ID不匹配！');
      console.log('  - 当前用户ID:', user.uid);
      console.log('  - 存储服务用户ID:', unifiedStorageService.userId);
      
      // 尝试重新初始化
      console.log('🔄 尝试重新初始化统一存储服务...');
      unifiedStorageService.init(user.uid);
      
      console.log('🔧 重新初始化后的状态:', {
        userId: unifiedStorageService.userId
      });
    } else {
      console.log('✅ 用户ID匹配正常');
    }
    
    // 测试Firebase权限
    console.log('🔍 测试Firebase权限...');
    const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
    
    try {
      const history = await aiAnalysisService.getAnalysisHistory(user.uid, 5);
      console.log('✅ 读取权限正常，找到', history.length, '条记录');
      
      if (history.length > 0) {
        const firstRecord = history[0];
        console.log('📝 第一条记录:', {
          id: firstRecord.id,
          text: firstRecord.text?.substring(0, 50) + '...',
          timestamp: firstRecord.timestamp
        });
      }
    } catch (error) {
      console.error('❌ 读取权限失败:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ 调试失败:', error);
    return false;
  }
};

/**
 * 测试Firebase权限
 */
export const testFirebasePermissions = async () => {
  console.log('🔍 测试Firebase权限...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    console.log('👤 用户信息:', {
      uid: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      displayName: user.displayName
    });
    
    // 测试读取权限
    console.log('🔍 测试读取权限...');
    try {
      const analysisRef = doc(db, 'ai_analysis', user.uid, 'analyses', 'test-doc');
      await getDoc(analysisRef);
      console.log('✅ 读取权限正常');
    } catch (error) {
      console.error('❌ 读取权限失败:', error);
      console.error('❌ 错误代码:', error.code);
    }
    
    // 测试写入权限
    console.log('🔍 测试写入权限...');
    try {
      const testData = {
        id: 'test-permission-' + Date.now(),
        text: '权限测试',
        timestamp: new Date().toISOString()
      };
      
      const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
      await aiAnalysisService.saveAnalysis(testData, user.uid);
      console.log('✅ 写入权限正常');
      
      // 立即删除测试数据
      await aiAnalysisService.deleteAnalysis(user.uid, testData.id);
      console.log('✅ 删除权限正常');
      
    } catch (error) {
      console.error('❌ 写入/删除权限失败:', error);
      console.error('❌ 错误代码:', error.code);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 权限测试失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.debugDelete = {
    debugDeleteIssue,
    testFirebasePermissions
  };
  
  console.log('🔧 删除问题调试工具已加载到 window.debugDelete');
  console.log('💡 使用方法:');
  console.log('  - await window.debugDelete.debugDeleteIssue() // 调试删除问题');
  console.log('  - await window.debugDelete.testFirebasePermissions() // 测试权限');
}