import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import { useEditorText } from './useEditorText';

// Mock writing service
vi.mock('../services/writing/writingTextService', () => ({
  getWritingText: vi.fn(),
  getWritingSuggestions: vi.fn(),
  autoSaveWritingText: vi.fn(),
  clearWritingText: vi.fn()
}));

import { getWritingText, getWritingSuggestions, autoSaveWritingText, clearWritingText } from '../services/writing/writingTextService';

describe('useEditorText', () => {
  const mockSetSuggestions = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    getWritingText.mockReturnValue('Initial text');
    getWritingSuggestions.mockReturnValue([]);
  });

  it('should initialize with text from localStorage', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));

    expect(result.current.text).toBe('Initial text');
    expect(result.current.hoveredSuggestion).toBe(null);
    expect(result.current.activeBubble).toBe(null);
    expect(result.current.bubblePosition).toEqual({ x: 0, y: 0 });
  });

  it('should restore saved suggestions on mount', () => {
    const mockSuggestions = [
      { id: '1', text: 'suggestion 1' },
      { id: '2', text: 'suggestion 2' }
    ];
    getWritingSuggestions.mockReturnValue(mockSuggestions);

    renderHook(() => useEditorText(mockSetSuggestions));

    expect(getWritingSuggestions).toHaveBeenCalled();
    expect(mockSetSuggestions).toHaveBeenCalledWith(mockSuggestions);
  });

  it('should not restore suggestions if none exist', () => {
    getWritingSuggestions.mockReturnValue([]);

    renderHook(() => useEditorText(mockSetSuggestions));

    expect(mockSetSuggestions).not.toHaveBeenCalled();
  });

  it('should handle text change and auto-save', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const newText = 'New text content';
    const suggestions = [{ id: '1', text: 'suggestion' }];

    act(() => {
      result.current.handleTextChange(newText, suggestions);
    });

    expect(result.current.text).toBe(newText);
    expect(autoSaveWritingText).toHaveBeenCalledWith(newText, suggestions);
  });

  it('should clear suggestions when text is too short', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const shortText = 'short';

    act(() => {
      result.current.handleTextChange(shortText, []);
    });

    expect(result.current.text).toBe(shortText);
    expect(mockSetSuggestions).toHaveBeenCalledWith([]);
  });

  it('should auto-save when text changes', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));

    act(() => {
      result.current.setText('New text');
    });

    expect(autoSaveWritingText).toHaveBeenCalledWith('New text', []);
  });

  it('should handle new document with confirmation when text exists', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const mockShowConfirm = vi.fn();

    act(() => {
      result.current.setText('Some text');
    });

    act(() => {
      result.current.handleNewDocument(mockShowConfirm);
    });

    expect(mockShowConfirm).toHaveBeenCalledWith({
      title: '创建新文档',
      message: '确定要创建新文档吗？当前内容将被清除。',
      confirmText: '创建新文档',
      cancelText: '取消',
      type: 'warning',
      onConfirm: expect.any(Function)
    });
  });

  it('should clear text immediately when no text exists', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const mockShowConfirm = vi.fn();

    // First clear the text
    act(() => {
      result.current.setText('');
    });

    act(() => {
      result.current.handleNewDocument(mockShowConfirm);
    });

    expect(result.current.text).toBe('');
    expect(mockSetSuggestions).toHaveBeenCalledWith([]);
    expect(clearWritingText).toHaveBeenCalled();
    expect(mockShowConfirm).not.toHaveBeenCalled();
  });

  it('should handle history selection', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const mockCloseModal = vi.fn();
    const historyRecord = {
      text: 'History text',
      suggestions: [{ id: '1', text: 'history suggestion' }]
    };

    act(() => {
      result.current.handleSelectHistory(historyRecord, mockCloseModal);
    });

    expect(result.current.text).toBe('History text');
    expect(mockSetSuggestions).toHaveBeenCalledWith(historyRecord.suggestions);
    expect(mockCloseModal).toHaveBeenCalled();
  });

  it('should handle history selection with no record', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const mockCloseModal = vi.fn();
    const originalText = result.current.text;

    act(() => {
      result.current.handleSelectHistory(null, mockCloseModal);
    });

    expect(result.current.text).toBe(originalText);
    expect(mockCloseModal).toHaveBeenCalled();
  });

  it('should handle history selection with no suggestions', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const mockCloseModal = vi.fn();
    const historyRecord = { text: 'History text' };

    act(() => {
      result.current.handleSelectHistory(historyRecord, mockCloseModal);
    });

    expect(result.current.text).toBe('History text');
    expect(mockSetSuggestions).toHaveBeenCalledWith([]);
    expect(mockCloseModal).toHaveBeenCalled();
  });

  it('should update bubble position', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const newPosition = { x: 100, y: 200 };

    act(() => {
      result.current.setBubblePosition(newPosition);
    });

    expect(result.current.bubblePosition).toEqual(newPosition);
  });

  it('should update active bubble', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const bubble = { id: '1', text: 'bubble' };

    act(() => {
      result.current.setActiveBubble(bubble);
    });

    expect(result.current.activeBubble).toEqual(bubble);
  });

  it('should update hovered suggestion', () => {
    const { result } = renderHook(() => useEditorText(mockSetSuggestions));
    const suggestion = { id: '1', text: 'suggestion' };

    act(() => {
      result.current.setHoveredSuggestion(suggestion);
    });

    expect(result.current.hoveredSuggestion).toEqual(suggestion);
  });
});
