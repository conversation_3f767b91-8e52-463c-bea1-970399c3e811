/**
 * 测试数据渲染的详细验证
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试数据渲染的详细验证
export const testDataRendering = async () => {
  console.log('🧪 开始测试数据渲染的详细验证...');
  
  // 模拟用户登录
  const testUserId = 'test_user_' + Date.now();
  simpleStorageService.init(testUserId);
  
  console.log('👤 测试用户ID:', testUserId);
  
  // 测试1: 创建符合前端组件期望的数据结构
  console.log('💬 测试1: 创建符合前端组件期望的数据结构...');
  
  const testMessages = [
    { 
      id: 'msg_1', 
      type: 'user', 
      content: 'Can you help me with English grammar?',
      timestamp: new Date()
    },
    { 
      id: 'msg_2', 
      type: 'ai', 
      content: 'Of course! I\'d be happy to help you with English grammar.',
      translation: '当然！我很乐意帮助你学习英语语法。',
      timestamp: new Date()
    }
  ];
  
  const chatSession = simpleStorageService.saveChatSession(testMessages, 'Grammar Help');
  console.log('✅ 聊天会话创建成功:', chatSession);
  
  // 测试2: 验证数据结构是否符合ChatHistoryModal的期望
  console.log('\n🔍 测试2: 验证ChatHistoryModal数据结构...');
  const chatHistory = simpleStorageService.getChatHistory();
  const session = chatHistory[0];
  
  console.log('📋 会话数据结构:');
  console.log('  - ID:', session.id);
  console.log('  - 标题:', session.title);
  console.log('  - 消息数量:', session.messageCount);
  console.log('  - 时间戳:', session.timestamp);
  console.log('  - 用户ID:', session.userId);
  console.log('  - 消息数组:', Array.isArray(session.messages) ? '是' : '否');
  console.log('  - 消息长度:', session.messages ? session.messages.length : 0);
  
  // 验证ChatHistoryModal需要的字段
  const chatModalFields = {
    id: session.id,
    title: session.title || session.sessionTitle || '未命名对话',
    messageCount: session.messageCount || (session.messages ? session.messages.length : 0),
    timestamp: session.timestamp
  };
  
  console.log('📋 ChatHistoryModal渲染数据:');
  Object.entries(chatModalFields).forEach(([key, value]) => {
    console.log(`  - ${key}:`, value);
  });
  
  // 测试3: 验证数据结构是否符合ChatPage的期望
  console.log('\n🔍 测试3: 验证ChatPage数据结构...');
  
  // 模拟ChatPage的handleSelectChatHistory验证
  const isValidForChatPage = (session) => {
    if (!session || !session.messages || !Array.isArray(session.messages)) {
      return false;
    }
    
    const validMessages = session.messages.filter(msg => 
      msg && 
      typeof msg.id === 'string' && 
      typeof msg.type === 'string' && 
      typeof msg.content === 'string'
    );
    
    return validMessages.length > 0;
  };
  
  const chatPageValid = isValidForChatPage(session);
  console.log('✅ ChatPage数据验证:', chatPageValid ? '通过' : '失败');
  
  if (!chatPageValid) {
    console.error('❌ ChatPage数据验证失败，详细信息:');
    console.log('  - session存在:', !!session);
    console.log('  - messages存在:', !!session.messages);
    console.log('  - messages是数组:', Array.isArray(session.messages));
    if (session.messages) {
      console.log('  - messages长度:', session.messages.length);
      session.messages.forEach((msg, index) => {
        console.log(`    - 消息${index + 1}:`, {
          id: typeof msg.id,
          type: typeof msg.type,
          content: typeof msg.content,
          hasId: !!msg.id,
          hasType: !!msg.type,
          hasContent: !!msg.content
        });
      });
    }
  }
  
  // 测试4: 创建AI分析历史数据
  console.log('\n📝 测试4: 创建AI分析历史数据...');
  const analysisRecord = simpleStorageService.saveAnalysis(
    'This is a test text for analysis',
    'Raw analysis result with detailed information',
    { 
      suggestions: ['suggestion1', 'suggestion2'], 
      score: 85,
      grammar: 'Good',
      vocabulary: 'Advanced'
    }
  );
  console.log('✅ AI分析记录创建成功:', analysisRecord);
  
  // 测试5: 验证AI分析历史数据结构
  console.log('\n🔍 测试5: 验证AI分析历史数据结构...');
  const analysisHistory = simpleStorageService.getAnalysisHistory();
  const record = analysisHistory[0];
  
  console.log('📋 分析记录数据结构:');
  console.log('  - ID:', record.id);
  console.log('  - 文本:', record.text);
  console.log('  - 原始分析:', record.rawAnalysis);
  console.log('  - 分析结果:', record.analysis);
  console.log('  - 时间戳:', record.timestamp);
  console.log('  - 用户ID:', record.userId);
  
  // 验证HistoryModal需要的字段
  const analysisModalFields = {
    id: record.id,
    text: record.text,
    rawAnalysis: record.rawAnalysis,
    analysis: record.analysis,
    timestamp: record.timestamp
  };
  
  console.log('📋 HistoryModal渲染数据:');
  Object.entries(analysisModalFields).forEach(([key, value]) => {
    console.log(`  - ${key}:`, typeof value === 'object' ? '对象' : value);
  });
  
  // 测试6: 模拟前端组件渲染过程
  console.log('\n🎨 测试6: 模拟前端组件渲染过程...');
  
  // 模拟ChatHistoryModal渲染
  console.log('💬 模拟ChatHistoryModal渲染:');
  chatHistory.forEach((session, index) => {
    const displayTitle = session.title || session.sessionTitle || '未命名对话';
    const displayMessageCount = session.messageCount || (session.messages ? session.messages.length : 0);
    const displayDate = new Date(session.timestamp).toLocaleDateString('zh-CN');
    
    console.log(`  ${index + 1}. ${displayTitle} (${displayMessageCount} 条消息) - ${displayDate}`);
    
    // 验证渲染数据
    if (!displayTitle || displayTitle === '未命名对话') {
      console.warn('⚠️ 标题可能有问题');
    }
    if (displayMessageCount === 0) {
      console.warn('⚠️ 消息数量为0');
    }
  });
  
  // 模拟HistoryModal渲染
  console.log('\n📝 模拟HistoryModal渲染:');
  analysisHistory.forEach((record, index) => {
    const displayText = record.text ? record.text.substring(0, 30) + '...' : '无文本';
    const displayDate = new Date(record.timestamp).toLocaleDateString('zh-CN');
    
    console.log(`  ${index + 1}. ${displayText} - ${displayDate}`);
    
    // 验证渲染数据
    if (!record.text) {
      console.warn('⚠️ 文本内容缺失');
    }
    if (!record.rawAnalysis) {
      console.warn('⚠️ 原始分析缺失');
    }
    if (!record.analysis) {
      console.warn('⚠️ 分析结果缺失');
    }
  });
  
  // 测试7: 测试数据更新和同步
  console.log('\n🔄 测试7: 测试数据更新和同步...');
  
  // 更新聊天会话
  const updatedMessages = [...testMessages, {
    id: 'msg_3',
    type: 'user',
    content: 'Thank you for your help!',
    timestamp: new Date()
  }];
  
  const updatedSession = simpleStorageService.saveChatSession(updatedMessages, 'Updated Grammar Help');
  console.log('✅ 聊天会话更新成功:', updatedSession);
  
  // 验证更新后的数据
  const updatedHistory = simpleStorageService.getChatHistory();
  const foundUpdated = updatedHistory.find(s => s.id === updatedSession.id);
  console.log('✅ 更新验证:', foundUpdated ? '成功' : '失败');
  
  // 最终统计
  const finalStats = simpleStorageService.getStats();
  console.log('\n📊 最终统计:', finalStats);
  
  console.log('🎉 数据渲染详细验证测试完成！');
  
  // 清理测试数据
  simpleStorageService.cleanup();
  console.log('🧹 测试数据已清理');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testDataRendering = testDataRendering;
}
