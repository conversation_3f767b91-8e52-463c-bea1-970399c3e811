.dict<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Header {
  .hw_txt {
    font-size: 1.5em;
    font-weight: bold;
  }
}

.dictWebster<PERSON>earner-Related {
  a {
    margin-left: 2em;
    color: #16a085;
  }
}

.dictWebsterLearner-Entry {
  & > .labels > .lb {
    font-style: italic;
  }

  & > .labels > .gram > .gram_internal {
    color: #757575;
  }

  & > .labels > .sl {
    font-style: italic;
  }

  /*view:  dros*/
  & > .dros {
    margin-bottom: 1em;
  }

  & > .dros > .dro {
    margin-bottom: 0.8em;
    padding-left: 0.8em;
  }

  & > .dros > .dro:last-child {
    margin-bottom: 0;
  }

  & > .dros > .dro > .dro_line {
    margin-left: -0.8em;
  }

  & > .dros > .dro > .dro_line > * {
    vertical-align: middle;
  }

  & > .dros > .dro > .dro_line > .dre {
    display: inline;
    font-weight: bold;
    padding: 0;
    margin: 0;
    font-size: inherit;
  }

  & > .dros > .dro > .dro_line > .gram > .gram_internal {
    color: #757575;
  }

  & > .dros > .dro > .dro_line > .sl {
    font-style: italic;
  }

  & > .dros > .dro > .dro_line > .rsl {
    font-style: italic;
  }

  & > .dros > .dro > .dxs {
    margin-top: 0.6em;
    display: block;
  }

  /*view:  uros*/
  & > .uros {
    margin-bottom: 1em;
  }

  & > .uros > .uro {
    margin-bottom: 0.8em;
    padding-left: 0.8em;
  }

  & > .uros > .uro:last-child {
    margin-bottom: 0;
  }

  & > .uros > .uro > .uro_line {
    margin-left: -0.8em;
  }

  & > .uros > .uro > .uro_line > * {
    vertical-align: middle;
  }

  & > .uros > .uro > .uro_line > .ure {
    display: inline;
    font-weight: bold;
    padding: 0;
    margin: 0;
    font-size: inherit;
    margin-right: 0.5em;
  }

  & > .uros > .uro > .uro_line > .gram > .gram_internal {
    color: #757575;
  }

  & > .uros > .uro > .uro_line > .lb {
    font-style: italic;
  }

  & > .uros > .uro > .uro_line > .sl {
    font-style: italic;
  }

  & > .uros > .uro > .uro_line > .fl {
    color: #717274;
    font-style: italic;
    font-weight: bold;
  }

  & > .uros > .uro > .uro_def {
    margin: 0.5em 0 0 0;
  }

  & > .uros > .uro > .uro_def:first-child {
    margin-top: 0;
  }

  & > .uros > .uro > .uro_def > *:first-child {
    margin-top: 0;
  }

  /*view:  cognate cross entries*/
  & > .cxs {
    margin-top: 1.2em;
    margin-bottom: 1.2em;
  }

  & > .cxs .cx_link {
    font-variant: small-caps;
    font-size: 1.1em;
    line-height: 1;
  }

  & > .cxs .cx_link sup {
    font-size: 50%;
  }

  & > .cxs .cl {
    font-style: italic;
  }
}

/*utils*/
.smark {
  font-size: 117%;
  font-weight: bold;
  padding-left: 1px;
}

.boxy {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.comma {
  margin: 0;
  padding: 0;
  font-weight: normal;
  color: #000;
  font-style: normal;
}

.semicolon {
  margin: 0;
  padding: 0;
  font-weight: normal;
  color: #000;
  font-style: normal;
}

.bc {
  font-weight: bold;
}

/*mw markup overrides*/
.mw_spm_aq {
  color: #000;
  margin: 0;
  padding: 0;
  padding: 0;
  border: none;
}

/*Dotted line*/
.dline {
  background: #ffffff
    url(data:image/gif;base64,R0lGODlhAwAOAJEAALKysv///////wAAACH5BAEHAAIALAAAAAADAA4AAAIGlI+poN0FADs=)
    repeat-x left center;
  margin-bottom: 1.3em;
}

.dline span {
  background-color: #fff;
  color: #3692a4;
  line-height: 1.1;
  font-weight: bold;
  display: inline-block;
  margin: 0;
  padding-right: 0.8em;
}

/*WOD only*/
.wod_img_tit {
  color: #757575;
  font-style: italic;
  margin-bottom: 15px;
}

.wod_img_act {
  padding: 0;
  line-height: 0;
}

/*non-HW prons*/
.pron_w {
  color: #717274;
  font-weight: normal;
  font-size: 109%;
}

.pron_i {
  color: #d40218;
  font-size: 140%;
}

.pron_i:hover {
  color: #ff0000;
  text-decoration: none;
}

/*non-HW variations*/
.v_label {
  font-style: italic;
  color: #757575;
}

.v_text {
  font-weight: bold;
}

/*non-HW inflections*/
.i_label {
  font-style: italic;
  color: #757575;
}

.i_text {
  font-weight: bold;
}

/*view:  phrasal verbs*/
.pva {
  font-weight: bold;
}

/*view:  synonym paragraphs*/
.synpar {
  border: 1px solid #ccc;
  padding: 4px 10px 6px 10px;
  margin-bottom: 1em;
}

.synpar > .synpar_part {
  margin-bottom: 10px;
}

.synpar > .synpar_part:last-child {
  margin-bottom: 0;
}

.synpar > .synpar_part > .synpar_w {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.synpar > .synpar_part > *:last-child {
  margin-bottom: 0;
}

/*view:  supplementary notes*/
.snotes {
  margin-bottom: 1em;
  overflow: hidden;
}

.snotes > * {
  margin-bottom: 0.8em;
}

.snotes > *:last-child {
  margin-bottom: 0;
}

.snotes > .snote_text > .snote_link {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.snotes > .snote_text > .snote_link sup {
  font-size: 50%;
}

/*view:  supplementary noteboxes*/
.snotebox {
  border: 1px solid #ccc;
  padding: 0.8em;
  margin-bottom: 1em;
  overflow: hidden;
}

.snotebox > .snotebox_text {
  text-align: justify;
}

.snotebox > .snotebox_text > .snote_link {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.snotebox > .snotebox_text > .snote_link sup {
  font-size: 50%;
}

/*view:  sense blocks*/
.sblocks {
  margin-bottom: 1em;
}

.sblocks .sense {
  margin-bottom: 0.5em;
}

.sblocks > .sblock {
  width: 100%;
  margin: 6px 0px 0px 0px;
}

.sblocks > .sblock > .sblock_c {
  margin-bottom: 10px;
}

.sblocks > .sblock > .sblock_c:last-child {
  margin-bottom: 0;
}

.sblocks > .sblock > .sblock_c > .sn_block_num {
  float: left;
  font-weight: bold;
}

.sblocks > .sblock > .sblock_c > .scnt > .sblock_labels > .slb {
  font-style: italic;
}

.sblocks > .sblock > .sblock_c > .scnt > .sblock_labels > .ssla {
  font-style: italic;
}

.sblocks
  > .sblock
  > .sblock_c
  > .scnt
  > .sblock_labels
  > .sgram
  > .sgram_internal {
  color: #757575;
}

.sblocks > .sblock > .sblock_c > .scnt > .sblock_labels > .bnote {
  font-weight: bold;
  font-style: italic;
}

.sblocks > .sblock > .sblock_c > .scnt {
  margin-bottom: 10px;
  overflow: auto;
}

.sblocks > .sblock > .sblock_c > .scnt:last-child {
  margin-bottom: 0;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > *:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .sn_letter {
  font-weight: bold;
  float: left;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .sd {
  font-style: italic;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .bnote {
  font-weight: bold;
  font-style: italic;
  color: #000;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .slb {
  font-style: italic;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .sgram > .sgram_internal {
  color: #757575;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .ssla {
  font-style: italic;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .def_text {
  margin-bottom: 6px;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .def_text > .bc {
  font-weight: bold;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .def_labels {
  margin-top: 10px;
  padding-left: 14px;
}

.sblocks
  > .sblock
  > .sblock_c
  > .scnt
  > .sense
  > .def_labels
  > .wsgram
  > .wsgram_internal {
  color: #757575;
}

.sblocks > .sblock > .sblock_c > .scnt > .sense > .def_labels > .sl {
  font-style: italic;
}

/*view:  verbal illustration*/
.vis_w {
  margin-bottom: 0.3em;
}

.vis_w > .vis {
  padding-left: 1.2em;
}

.vis_w > .vis > .vi {
  padding: 0.08em 0;
  list-style-type: square;
  color: #5fb68c;
}

.vis_w > .vis > .vi > .vi_content {
  color: var(--color-font-grey);
  margin-left: -2px;
  line-height: 1.3;
}

.vis_w > .vis > .vi:first-child {
  padding-top: 0;
}

.vis_w > .vis > .vi:last-child {
  padding-bottom: 0;
}

.vis_w > .vi_more {
  display: none;
}

/*view:  inline synonyms*/
.isyns > .bc {
  font-weight: bold;
}

.isyns > .isyn_link {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.isyns > .isyn_link sup {
  font-size: 50%;
}

/*view:  directional cross entries*/
.dxs.dxs_nl {
  margin-bottom: 1em;
}

.dxs .dx .dx_link {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.dxs .dx .dx_link sup {
  font-size: 50%;
}

.dxs .dx .dx_span {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.dxs .dx .dx_span sup {
  font-size: 50%;
}

.dxs .dx .dx_ab {
  font-style: italic;
}

.dxs .dx .dx_tag {
  font-style: italic;
}

/*view:  cas*/
.cas {
  margin-top: 14px;
}

.cas > .ca_prefix {
  font-style: italic;
}

.cas > .ca_text {
  font-style: italic;
}

/*view:  usage paragraphs*/
.usage_par {
  padding: 0.3em 0.7em;
  border: 1px solid #ccc;
  margin-bottom: 1em;
}

.usage_par > .usage_par_h {
  font-weight: bold;
}

.usage_par > .ud_text {
  text-align: justify;
}

.usage_par > * {
  margin-bottom: 0.5em;
}

.usage_par > *:last-child {
  margin-bottom: 0;
}

/*view:  synref*/
.synref_h1 {
  font-weight: bold;
}

.synref_link {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.synref_link sup {
  font-size: 50%;
}

/*view:  usageref*/
.usageref_block > .usageref_h1 {
  font-weight: bold;
}

.usageref_block > .usageref_link {
  font-variant: small-caps;
  font-size: 1.1em;
  line-height: 1;
}

.usageref_block > .usageref_link sup {
  font-size: 50%;
}
