import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { vi } from 'vitest';
import VoiceInputButton from './VoiceInputButton';

// Mock useSpeechRecognition hook
const mockUseSpeechRecognition = {
  transcript: '',
  isListening: false,
  isSupported: true,
  error: null,
  startListening: vi.fn(),
  stopListening: vi.fn(),
  clearTranscript: vi.fn(),
  getFinalTranscript: vi.fn(() => ''),
  checkSupport: vi.fn()
};

vi.mock('../hooks/useSpeechRecognition', () => ({
  useSpeechRecognition: () => mockUseSpeechRecognition
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  Mic: () => <div data-testid="mic-icon" />,
  MicOff: () => <div data-testid="mic-off-icon" />,
  Square: () => <div data-testid="square-icon" />
}));

describe('VoiceInputButton', () => {
  const defaultProps = {
    onTranscriptChange: vi.fn(),
    onFinalTranscript: vi.fn(),
    isDarkMode: false,
    disabled: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseSpeechRecognition.transcript = '';
    mockUseSpeechRecognition.isListening = false;
    mockUseSpeechRecognition.isSupported = true;
    mockUseSpeechRecognition.error = null;
    mockUseSpeechRecognition.getFinalTranscript.mockReturnValue('');
  });

  describe('基本渲染', () => {
    it('应该渲染语音输入按钮', () => {
      render(<VoiceInputButton {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('应该在不支持语音识别时不渲染按钮', () => {
      mockUseSpeechRecognition.isSupported = false;

      render(<VoiceInputButton {...defaultProps} />);

      const button = screen.queryByRole('button');
      expect(button).not.toBeInTheDocument();
    });

    it('应该显示正确的麦克风图标', () => {
      render(<VoiceInputButton {...defaultProps} />);

      expect(screen.getByTestId('mic-icon')).toBeInTheDocument();
    });
  });

  describe('语音识别功能', () => {
    it('应该能够开始语音识别', () => {
      render(<VoiceInputButton {...defaultProps} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockUseSpeechRecognition.startListening).toHaveBeenCalled();
    });

    it('应该能够停止语音识别', () => {
      mockUseSpeechRecognition.isListening = true;

      render(<VoiceInputButton {...defaultProps} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockUseSpeechRecognition.stopListening).toHaveBeenCalled();
    });

    it('应该在监听时显示停止图标', () => {
      mockUseSpeechRecognition.isListening = true;

      render(<VoiceInputButton {...defaultProps} />);

      expect(screen.getByTestId('square-icon')).toBeInTheDocument();
    });
  });

  describe('样式和主题', () => {
    it('应该在深色模式下应用正确样式', () => {
      render(<VoiceInputButton {...defaultProps} isDarkMode={true} />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('应该支持自定义className', () => {
      render(<VoiceInputButton {...defaultProps} className="custom-voice-btn" />);

      // className应用到外层容器div上
      const container = screen.getByRole('button').parentElement;
      expect(container).toHaveClass('custom-voice-btn');
    });
  });
});
