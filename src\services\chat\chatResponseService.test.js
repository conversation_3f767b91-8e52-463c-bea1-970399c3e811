import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getChatResponse } from './chatResponseService';

// Mock dependencies
vi.mock('../ai/promptLoader.js', () => ({
  loadPrompt: vi.fn(),
  replacePromptVariables: vi.fn()
}));

vi.mock('../user/userSettingsService.js', () => ({
  getDoubaoApiKey: vi.fn()
}));

vi.mock('../ai/translationService.js', () => ({
  translateToChinese: vi.fn()
}));

// Mock fetch
global.fetch = vi.fn();

describe('chatResponseService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getChatResponse', () => {
    it('应该成功获取聊天响应', async () => {
      const { loadPrompt, replacePromptVariables } = await import('../ai/promptLoader.js');
      const { getDoubaoApiKey } = await import('../user/userSettingsService.js');
      const { translateToChinese } = await import('../ai/translationService.js');

      loadPrompt.mockResolvedValue('Test system prompt');
      getDoubaoApiKey.mockResolvedValue('test-api-key');
      translateToChinese.mockResolvedValue('测试AI响应');

      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: 'Test AI response' } }]
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: '测试AI响应' } }]
          })
        });

      const result = await getChatResponse('Hello', []);

      expect(result).toBe('Test AI response\n---\n测试AI响应');
      expect(loadPrompt).toHaveBeenCalledWith('chatResponsePrompt');
      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith(
        'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key'
          }
        })
      );
      expect(translateToChinese).toHaveBeenCalledWith('Test AI response');
    });

    it('应该处理写作上下文', async () => {
      const { loadPrompt, replacePromptVariables } = await import('../ai/promptLoader.js');
      const { getDoubaoApiKey } = await import('../user/userSettingsService.js');

      loadPrompt.mockResolvedValue('Test system prompt');
      replacePromptVariables.mockReturnValue('Modified prompt with writing context');
      getDoubaoApiKey.mockResolvedValue('test-api-key');

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'Test AI response' } }]
        })
      });

      const writingContext = {
        title: 'Test Title',
        content: 'This is a test content that is longer than 200 characters to test the content preview functionality. '.repeat(5),
        wordCount: 100,
        sharedAt: '2024-01-01T00:00:00Z'
      };

      await getChatResponse('Hello', [], writingContext);

      expect(replacePromptVariables).toHaveBeenCalledWith(
        'Test system prompt',
        expect.objectContaining({
          writingContext: expect.objectContaining({
            title: 'Test Title',
            contentPreview: expect.stringContaining('...'),
            wordCount: 100,
            sharedDate: expect.any(String)
          })
        })
      );
    });

    it('应该过滤对话历史', async () => {
      const { loadPrompt } = await import('../ai/promptLoader.js');
      const { getDoubaoApiKey } = await import('../user/userSettingsService.js');

      loadPrompt.mockResolvedValue('Test system prompt');
      getDoubaoApiKey.mockResolvedValue('test-api-key');

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'Test AI response' } }]
        })
      });

      const conversationHistory = [
        { type: 'ai', content: 'Initial greeting' },
        { type: 'user', content: 'User message 1' },
        { type: 'ai', content: 'AI response 1' },
        { type: 'user', content: 'User message 2' }
      ];

      await getChatResponse('Hello', conversationHistory);

      const fetchCall = fetch.mock.calls[0];
      const requestData = JSON.parse(fetchCall[1].body);
      
      expect(requestData.messages).toHaveLength(5); // system + 4 messages (skipping initial AI greeting)
      expect(requestData.messages[1].role).toBe('user');
      expect(requestData.messages[1].content).toBe('User message 1');
    });

    it('应该抛出错误当没有API密钥', async () => {
      const { getDoubaoApiKey } = await import('../user/userSettingsService.js');
      getDoubaoApiKey.mockResolvedValue(null);

      await expect(getChatResponse('Hello')).rejects.toThrow('系统API密钥未配置，请联系管理员');
    });

    it('应该抛出错误当API请求失败', async () => {
      const { loadPrompt } = await import('../ai/promptLoader.js');
      const { getDoubaoApiKey } = await import('../user/userSettingsService.js');

      loadPrompt.mockResolvedValue('Test system prompt');
      getDoubaoApiKey.mockResolvedValue('test-api-key');

      global.fetch.mockResolvedValue({
        ok: false,
        status: 500,
        text: () => Promise.resolve('Internal Server Error')
      });

      await expect(getChatResponse('Hello')).rejects.toThrow('API request failed: 500 Internal Server Error');
    });

    it('应该使用正确的请求参数', async () => {
      const { loadPrompt } = await import('../ai/promptLoader.js');
      const { getDoubaoApiKey } = await import('../user/userSettingsService.js');

      loadPrompt.mockResolvedValue('Test system prompt');
      getDoubaoApiKey.mockResolvedValue('test-api-key');

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'Test AI response' } }]
        })
      });

      await getChatResponse('Hello', []);

      const fetchCall = fetch.mock.calls[0];
      const requestData = JSON.parse(fetchCall[1].body);
      
      expect(requestData.model).toBe('doubao-seed-1-6-flash-250615');
      expect(requestData.temperature).toBe(0.7);
      expect(requestData.max_tokens).toBe(800);
      expect(requestData.thinking).toEqual({ type: "disabled" });
      expect(requestData.messages).toHaveLength(2); // system + user
      expect(requestData.messages[0].role).toBe('system');
      expect(requestData.messages[1].role).toBe('user');
      expect(requestData.messages[1].content).toBe('Hello');
    });
  });
});
