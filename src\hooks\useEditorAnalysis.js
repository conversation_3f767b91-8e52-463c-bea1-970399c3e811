import useAIAnalysis from './useAIAnalysis';

/**
 * 编辑器AI分析Hook
 * 处理AI分析相关的逻辑和错误处理
 */
export const useEditorAnalysis = (user, setShowAuthModal) => {
  const { isAnalyzing, suggestions, setSuggestions, analyzeText, rawAIResponse } = useAIAnalysis();

  // 处理AI分析
  const handleAnalyze = async (text) => {
    if (!text.trim()) {
      alert('请输入一些文本再进行分析');
      return;
    }

    // 检查用户登录状态
    if (!user) {
      alert('请先登录以使用AI分析功能');
      setShowAuthModal(true);
      return;
    }

    try {
      await analyzeText(text, user.uid);
    } catch (error) {
      console.error('分析失败:', error);
      if (error.message.includes('使用量已达上限')) {
        alert(error.message);
      } else {
        alert('分析失败，请稍后重试');
      }
    }
  };

  return {
    isAnalyzing,
    suggestions,
    setSuggestions,
    rawAIResponse,
    handleAnalyze
  };
};
