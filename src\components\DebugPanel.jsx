import React, { useState } from 'react';
import { getSystemConfig, getUserSettings, checkApiUsageLimit } from '../services/user/userSettingsService';
import { onAuthChange } from '../services/auth/authService';

const DebugPanel = () => {
  const [debugInfo, setDebugInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState(null);

  React.useEffect(() => {
    const unsubscribe = onAuthChange((user) => {
      setUser(user);
    });
    return () => unsubscribe();
  }, []);

  const runDebug = async () => {
    setIsLoading(true);
    try {
      const systemConfig = await getSystemConfig();
      let userSettings = null;
      let usageCheck = null;

      if (user) {
        userSettings = await getUserSettings(user.uid);
        usageCheck = await checkApiUsageLimit(user.uid);
      }

      setDebugInfo({
        systemConfig,
        userSettings,
        usageCheck,
        user: user ? { uid: user.uid, email: user.email } : null,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      setDebugInfo({
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '400px',
      backgroundColor: '#fff',
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      zIndex: 1000,
      maxHeight: '60vh',
      overflow: 'auto'
    }}>
      <h3 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: 'bold' }}>
        🐛 调试面板
      </h3>
      
      <button
        onClick={runDebug}
        disabled={isLoading}
        style={{
          width: '100%',
          padding: '8px',
          backgroundColor: isLoading ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          fontSize: '14px',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          marginBottom: '12px'
        }}
      >
        {isLoading ? '检查中...' : '检查配置'}
      </button>

      {debugInfo && (
        <div style={{ fontSize: '12px' }}>
          <div style={{ marginBottom: '8px', color: '#666' }}>
            检查时间: {new Date(debugInfo.timestamp).toLocaleString()}
          </div>
          
          {debugInfo.error ? (
            <div style={{
              padding: '8px',
              backgroundColor: '#f8d7da',
              border: '1px solid #f5c6cb',
              borderRadius: '4px',
              color: '#721c24'
            }}>
              错误: {debugInfo.error}
            </div>
          ) : (
            <div>
              <details style={{ marginBottom: '8px' }}>
                <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                  系统配置
                </summary>
                <pre style={{
                  marginTop: '4px',
                  padding: '8px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                  overflow: 'auto',
                  fontSize: '11px'
                }}>
                  {JSON.stringify(debugInfo.systemConfig, null, 2)}
                </pre>
              </details>

              {debugInfo.user && (
                <>
                  <details style={{ marginBottom: '8px' }}>
                    <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                      用户信息
                    </summary>
                    <pre style={{
                      marginTop: '4px',
                      padding: '8px',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '11px'
                    }}>
                      {JSON.stringify(debugInfo.user, null, 2)}
                    </pre>
                  </details>

                  <details style={{ marginBottom: '8px' }}>
                    <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                      用户设置
                    </summary>
                    <pre style={{
                      marginTop: '4px',
                      padding: '8px',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '11px'
                    }}>
                      {JSON.stringify(debugInfo.userSettings, null, 2)}
                    </pre>
                  </details>

                  <details style={{ marginBottom: '8px' }}>
                    <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                      使用量检查
                    </summary>
                    <pre style={{
                      marginTop: '4px',
                      padding: '8px',
                      backgroundColor: debugInfo.usageCheck?.canUse ? '#d4edda' : '#f8d7da',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '11px'
                    }}>
                      {JSON.stringify(debugInfo.usageCheck, null, 2)}
                    </pre>
                  </details>
                </>
              )}

              {!debugInfo.user && (
                <div style={{
                  padding: '8px',
                  backgroundColor: '#fff3cd',
                  border: '1px solid #ffeaa7',
                  borderRadius: '4px',
                  color: '#856404'
                }}>
                  用户未登录
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DebugPanel;
