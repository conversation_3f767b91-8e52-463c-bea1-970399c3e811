// 隐藏字段测试工具
// 用于验证元数据字段是否已被正确隐藏

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试字段隐藏效果
const testHiddenFields = async () => {
  console.log('🧪 测试元数据字段隐藏效果...');
  console.log('='.repeat(50));
  
  const testWords = ['nothing', 'doing', 'school', 'beautiful'];
  const results = {};
  
  for (const word of testWords) {
    try {
      console.log(`\n📖 测试单词: ${word}`);
      console.log('-'.repeat(30));
      
      const result = await getWordDetails(word, 'ecdict');
      
      if (result && !result.notFound) {
        console.log(`✅ ${word} 查询成功`);
        console.log(`📊 数据源: ${result.source}`);
        
        // 检查是否还有元数据字段
        const hasTags = result.tags && result.tags.length > 0;
        const hasCollins = result.collins > 0;
        const hasOxford = result.oxford;
        const hasBnc = result.bnc > 0;
        const hasFrq = result.frq > 0;
        
        console.log('🔍 元数据字段检查:');
        console.log(`  - 考试标签: ${hasTags ? '❌ 仍有数据' : '✅ 已隐藏'}`);
        console.log(`  - 柯林斯星级: ${hasCollins ? '❌ 仍有数据' : '✅ 已隐藏'}`);
        console.log(`  - 牛津3000: ${hasOxford ? '❌ 仍有数据' : '✅ 已隐藏'}`);
        console.log(`  - BNC词频: ${hasBnc ? '❌ 仍有数据' : '✅ 已隐藏'}`);
        console.log(`  - 当代词频: ${hasFrq ? '❌ 仍有数据' : '✅ 已隐藏'}`);
        
        // 检查核心内容
        console.log('📝 核心内容:');
        if (result.definitionPairs && result.definitionPairs.length > 0) {
          console.log(`  - 配对释义: ${result.definitionPairs.length} 个`);
        } else {
          console.log(`  - 英文释义: ${result.definition ? '有' : '无'}`);
          console.log(`  - 中文释义: ${result.translation ? '有' : '无'}`);
        }
        
        if (result.exchange && Object.keys(result.exchange).length > 0) {
          console.log(`  - 词形变化: 有`);
        }
        
        const hasMetadata = hasTags || hasCollins || hasOxford || hasBnc || hasFrq;
        
        results[word] = {
          success: true,
          hasMetadata: hasMetadata,
          metadataFields: {
            tags: hasTags,
            collins: hasCollins,
            oxford: hasOxford,
            bnc: hasBnc,
            frq: hasFrq
          }
        };
      } else {
        console.log(`❌ ${word} 查询失败`);
        console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
        
        results[word] = {
          success: false,
          error: result?.error || '未找到该单词'
        };
      }
    } catch (error) {
      console.log(`❌ ${word} 查询出错: ${error.message}`);
      results[word] = {
        success: false,
        error: error.message
      };
    }
  }
  
  // 统计结果
  console.log('\n📊 隐藏效果统计:');
  console.log('='.repeat(50));
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const hiddenCount = Object.values(results).filter(r => r.success && !r.hasMetadata).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`✅ 成功查询: ${successCount}/${totalCount}`);
  console.log(`🎯 完全隐藏元数据: ${hiddenCount}/${successCount}`);
  
  Object.entries(results).forEach(([word, result]) => {
    if (result.success) {
      if (result.hasMetadata) {
        console.log(`  ⚠️  ${word}: 仍有元数据字段显示`);
        if (result.metadataFields.tags) console.log(`    - 考试标签`);
        if (result.metadataFields.collins) console.log(`    - 柯林斯星级`);
        if (result.metadataFields.oxford) console.log(`    - 牛津3000`);
        if (result.metadataFields.bnc) console.log(`    - BNC词频`);
        if (result.metadataFields.frq) console.log(`    - 当代词频`);
      } else {
        console.log(`  ✅ ${word}: 元数据字段已完全隐藏`);
      }
    } else {
      console.log(`  ❌ ${word}: ${result.error}`);
    }
  });
  
  return results;
};

// 测试特定单词的显示效果
const testSpecificWordDisplay = async (word) => {
  console.log(`🧪 测试单词 "${word}" 的显示效果...`);
  console.log('='.repeat(50));
  
  try {
    const result = await getWordDetails(word, 'ecdict');
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log(`📖 单词: ${result.word}`);
      console.log(`🔊 音标: ${result.phonetic || '无'}`);
      console.log(`📊 数据源: ${result.source}`);
      
      // 检查显示内容
      console.log('\n🎨 显示内容预览:');
      console.log('-'.repeat(40));
      console.log('单词标题和音标');
      console.log('释义区域');
      
      if (result.definitionPairs && result.definitionPairs.length > 0) {
        console.log('配对释义:');
        result.definitionPairs.forEach((pair, index) => {
          if (pair.english) {
            console.log(`  ${pair.index}. 英文释义: ${pair.english}`);
          }
          if (pair.chinese) {
            console.log(`  ${pair.index}. 中文释义: ${pair.chinese}`);
          }
        });
      }
      
      if (result.exchange && Object.keys(result.exchange).length > 0) {
        console.log('词形变化:');
        Object.entries(result.exchange).forEach(([key, value]) => {
          console.log(`  ${key}: ${value}`);
        });
      }
      
      console.log('\n❌ 已隐藏的字段:');
      console.log('  - 考试标签 (zk, gk, ielts 等)');
      console.log('  - 柯林斯星级');
      console.log('  - 牛津3000');
      console.log('  - BNC词频');
      console.log('  - 当代词频');
      
      return result;
    } else {
      console.log('❌ 查询失败');
      console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return null;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始元数据字段隐藏测试...');
  console.log('='.repeat(60));
  
  try {
    // 测试字段隐藏效果
    const results = await testHiddenFields();
    
    // 测试特定单词显示
    console.log('\n🔍 详细测试 "nothing" 单词显示:');
    await testSpecificWordDisplay('nothing');
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    return results;
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testHiddenFields,
  testSpecificWordDisplay,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testHiddenFields = {
    testHiddenFields,
    testSpecificWordDisplay,
    runAllTests
  };
  
  console.log('🧪 元数据字段隐藏测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testHiddenFields.runAllTests() // 运行所有测试');
  console.log('  - window.testHiddenFields.testHiddenFields() // 测试字段隐藏效果');
  console.log('  - window.testHiddenFields.testSpecificWordDisplay("nothing") // 测试特定单词显示');
}