const axios = require('axios');
const admin = require('firebase-admin');

exports.handler = async (event, context) => {
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type'
  };

  try {
    const now = new Date();
    const dateString = now.toISOString().split('T')[0];
    
    console.log('开始生成日记，日期:', dateString);
    
    const diaryContent = await generateAIDiary(now);
    await saveDiaryToFirebase(diaryContent);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: '日记生成成功',
        date: dateString,
        content: diaryContent
      })
    };
    
  } catch (error) {
    console.error('生成日记失败:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: '生成日记失败',
        details: error.message
      })
    };
  }
};

async function generateAIDiary(date) {
  try {
    const prompt = `Generate a natural diary entry for ${date.toLocaleDateString()}. Return as JSON: {"english": "content", "chinese": "内容", "mood": "happy", "weather": "sunny"}`;

    const response = await axios.post('https://api.openai.com/v1/chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.8
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const content = response.data.choices[0].message.content;
    
    try {
      return JSON.parse(content);
    } catch {
      return {
        english: content || "Today was wonderful...",
        chinese: "今天很美好...",
        mood: "happy",
        weather: "sunny"
      };
    }
    
  } catch (error) {
    return {
      english: "Today I reflected on life...",
      chinese: "今天我思考了人生...",
      mood: "thoughtful",
      weather: "partly_cloudy"
    };
  }
}

async function saveDiaryToFirebase(diaryContent) {
  try {
    // 初始化 Firebase Admin SDK
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n')
        }),
        databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com`
      });
    }

    const now = new Date();
    const dateString = now.toISOString().split('T')[0];
    
    // 准备保存的数据
    const diaryData = {
      id: `diary_${Date.now()}`,
      date: dateString,
      timestamp: admin.firestore.Timestamp.fromDate(now),
      content: diaryContent,
      createdAt: admin.firestore.Timestamp.fromDate(now),
      type: 'auto_generated'
    };
    
    // 保存到 Firestore
    const db = admin.firestore();
    const docRef = await db.collection('diaries').add(diaryData);
    
    console.log('日记已保存到Firestore，文档ID:', docRef.id);
    return { id: docRef.id, ...diaryData };
    
  } catch (error) {
    console.error('保存到Firestore失败:', error);
    // 即使保存失败也不要抛出错误，只记录日志
    console.log('日记内容（未保存）:', diaryContent);
  }
}
