/**
 * 测试AI写作分析保存功能
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试AI写作分析保存功能
export const testAIAnalysisSaving = async () => {
  console.log('🧪 开始测试AI写作分析保存功能...');
  
  // 模拟用户登录
  const testUserId = 'test_user_' + Date.now();
  simpleStorageService.init(testUserId);
  
  console.log('👤 测试用户ID:', testUserId);
  
  // 测试1: 模拟AI分析保存
  console.log('📝 测试1: 模拟AI分析保存...');
  const testText = 'This is a test text for AI analysis. It contains some grammar issues and style problems.';
  const testRawAnalysis = 'The text has several areas for improvement including grammar, style, and clarity.';
  const testSuggestions = [
    {
      category: 'grammar',
      original: 'some grammar issues',
      replacement: 'several grammatical errors',
      explanation: 'More precise language would improve clarity.'
    },
    {
      category: 'style',
      original: 'style problems',
      replacement: 'stylistic concerns',
      explanation: 'More formal language would be appropriate.'
    }
  ];
  
  try {
    const savedAnalysis = simpleStorageService.saveAnalysis(testText, testRawAnalysis, testSuggestions);
    console.log('✅ AI分析保存成功:', savedAnalysis.id);
  } catch (error) {
    console.error('❌ AI分析保存失败:', error);
  }
  
  // 测试2: 验证保存的数据结构
  console.log('\n🔍 测试2: 验证保存的数据结构...');
  const analysisHistory = simpleStorageService.getAnalysisHistory();
  console.log('📋 AI分析历史记录数量:', analysisHistory.length);
  
  if (analysisHistory.length > 0) {
    const latestRecord = analysisHistory[0];
    console.log('📄 最新分析记录:');
    console.log('  - ID:', latestRecord.id);
    console.log('  - 文本:', latestRecord.text ? latestRecord.text.substring(0, 50) + '...' : 'N/A');
    console.log('  - 原始分析:', latestRecord.rawAnalysis ? latestRecord.rawAnalysis.substring(0, 50) + '...' : 'N/A');
    console.log('  - 分析结果:', latestRecord.analysis ? '有' : '无');
    console.log('  - 时间戳:', latestRecord.timestamp);
    console.log('  - 用户ID:', latestRecord.userId);
    
    // 验证必要字段
    const requiredFields = ['id', 'text', 'rawAnalysis', 'analysis', 'timestamp', 'userId'];
    const missingFields = requiredFields.filter(field => !latestRecord[field]);
    if (missingFields.length > 0) {
      console.warn('⚠️ 缺少必要字段:', missingFields);
    } else {
      console.log('✅ 数据结构完整');
    }
  }
  
  // 测试3: 模拟多次分析保存
  console.log('\n🔄 测试3: 模拟多次分析保存...');
  const testCases = [
    {
      text: 'First analysis text',
      rawAnalysis: 'First analysis result',
      suggestions: [{ category: 'grammar', original: 'text', replacement: 'content', explanation: 'Better word choice' }]
    },
    {
      text: 'Second analysis text',
      rawAnalysis: 'Second analysis result',
      suggestions: [{ category: 'style', original: 'text', replacement: 'writing', explanation: 'More formal tone' }]
    },
    {
      text: 'Third analysis text',
      rawAnalysis: 'Third analysis result',
      suggestions: [{ category: 'clarity', original: 'text', replacement: 'message', explanation: 'Clearer communication' }]
    }
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    try {
      const saved = simpleStorageService.saveAnalysis(testCase.text, testCase.rawAnalysis, testCase.suggestions);
      console.log(`  ✅ 测试用例 ${i + 1} 保存成功:`, saved.id);
    } catch (error) {
      console.error(`  ❌ 测试用例 ${i + 1} 保存失败:`, error);
    }
  }
  
  // 测试4: 验证所有保存的记录
  console.log('\n📋 测试4: 验证所有保存的记录...');
  const allAnalysis = simpleStorageService.getAnalysisHistory();
  console.log('📊 总分析记录数量:', allAnalysis.length);
  
  allAnalysis.forEach((record, index) => {
    console.log(`\n  记录 ${index + 1}:`);
    console.log('    - ID:', record.id);
    console.log('    - 文本:', record.text ? record.text.substring(0, 30) + '...' : 'N/A');
    console.log('    - 原始分析:', record.rawAnalysis ? record.rawAnalysis.substring(0, 30) + '...' : 'N/A');
    console.log('    - 分析结果:', record.analysis ? '有' : '无');
    console.log('    - 时间戳:', record.timestamp);
  });
  
  // 测试5: 测试数据删除
  console.log('\n🗑️ 测试5: 测试数据删除...');
  if (allAnalysis.length > 0) {
    const firstRecord = allAnalysis[0];
    const deleteResult = simpleStorageService.deleteAnalysis(firstRecord.id);
    console.log('  - 删除结果:', deleteResult ? '成功' : '失败');
    
    const afterDelete = simpleStorageService.getAnalysisHistory();
    console.log('  - 删除后记录数量:', afterDelete.length);
  }
  
  // 测试6: 测试数据清空
  console.log('\n🧹 测试6: 测试数据清空...');
  const clearResult = simpleStorageService.clearAnalysisHistory();
  console.log('  - 清空结果:', clearResult ? '成功' : '失败');
  
  const afterClear = simpleStorageService.getAnalysisHistory();
  console.log('  - 清空后记录数量:', afterClear.length);
  
  // 最终统计
  const finalStats = simpleStorageService.getStats();
  console.log('\n📊 最终统计:', finalStats);
  
  console.log('🎉 AI写作分析保存功能测试完成！');
  
  // 清理测试数据
  simpleStorageService.cleanup();
  console.log('🧹 测试数据已清理');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testAIAnalysisSaving = testAIAnalysisSaving;
}
