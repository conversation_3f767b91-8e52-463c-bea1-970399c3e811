import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import VintageTextEditor from './VintageTextEditor';

// Mock child components to isolate the VintageTextEditor
vi.mock('./EditorActionBar', () => ({
  default: (props) => <div data-testid="editor-action-bar" {...props}></div>,
}));
vi.mock('./EditorBottomControls', () => ({
  default: (props) => <div data-testid="editor-bottom-controls" {...props}></div>,
}));
vi.mock('./TextHighlightOverlay', () => ({
  default: (props) => <div data-testid="text-highlight-overlay" {...props}></div>,
}));
vi.mock('../hooks/useBubbleManager', () => ({
  useBubbleManager: () => ({
    clearTimeout: vi.fn(),
    setTimeout: vi.fn(),
    setActiveSuggestion: vi.fn(),
    clearActiveSuggestion: vi.fn(),
  }),
}));

describe('VintageTextEditor', () => {
  const mockSetText = vi.fn();
  const mockOnAnalyze = vi.fn();
  const mockSetHoveredSuggestion = vi.fn();
  const mockSetBubblePosition = vi.fn();
  const mockSetActiveBubble = vi.fn();

  const defaultProps = {
    text: '',
    setText: mockSetText,
    suggestions: [],
    isAnalyzing: false,
    onAnalyze: mockOnAnalyze,
    hoveredSuggestion: null,
    setHoveredSuggestion: mockSetHoveredSuggestion,
    setBubblePosition: mockSetBubblePosition,
    setActiveBubble: mockSetActiveBubble,
    isImmersiveMode: false,
    onShowAIResponse: vi.fn(),
    rawAIResponse: '',
    onShowHistory: vi.fn(),
    onShowDictionary: vi.fn(),
    onNewDocument: vi.fn(),
    isDarkMode: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with initial props', () => {
    render(<VintageTextEditor {...defaultProps} />);

    // Check for the textarea
    expect(screen.getByPlaceholderText(/在这里开始写作/)).toBeInTheDocument();

    // Check for child components
    expect(screen.getByTestId('editor-action-bar')).toBeInTheDocument();
    expect(screen.getByTestId('editor-bottom-controls')).toBeInTheDocument();

    // Highlight overlay should not be present
    expect(screen.queryByTestId('text-highlight-overlay')).not.toBeInTheDocument();

    // Check for the analyze button
    expect(screen.getByTitle('AI分析 (Tab)')).toBeInTheDocument();
  });

  it('handles text input', () => {
    render(<VintageTextEditor {...defaultProps} />);
    const textarea = screen.getByPlaceholderText(/在这里开始写作/);

    fireEvent.change(textarea, { target: { value: 'Hello world' } });
    expect(mockSetText).toHaveBeenCalledWith('Hello world');
  });

  it('enables analyze button when text is long enough', () => {
    const props = { ...defaultProps, text: 'This is a sufficiently long text.' };
    render(<VintageTextEditor {...props} />);
    const analyzeButton = screen.getByTitle('AI分析 (Tab)');
    expect(analyzeButton).not.toBeDisabled();
  });

  it('disables analyze button when text is too short', () => {
    render(<VintageTextEditor {...defaultProps} text="short" />);
    const analyzeButton = screen.getByTitle('AI分析 (Tab)');
    expect(analyzeButton).toBeDisabled();
  });

  it('disables analyze button when analysis is in progress', () => {
    const props = { ...defaultProps, text: 'This is a long text.', isAnalyzing: true };
    render(<VintageTextEditor {...props} />);
    const analyzeButton = screen.getByTitle('分析中...');
    expect(analyzeButton).toBeDisabled();
  });

  it('calls onAnalyze when analyze button is clicked', () => {
    const props = { ...defaultProps, text: 'This is a long enough text.' };
    render(<VintageTextEditor {...props} />);
    const analyzeButton = screen.getByTitle('AI分析 (Tab)');
    fireEvent.click(analyzeButton);
    expect(mockOnAnalyze).toHaveBeenCalled();
  });

  it('calls onAnalyze when Tab key is pressed with enough text', () => {
    const props = { ...defaultProps, text: 'This is a long enough text.' };
    render(<VintageTextEditor {...props} />);
    const textarea = screen.getByPlaceholderText(/在这里开始写作/);
    fireEvent.keyDown(textarea, { key: 'Tab', code: 'Tab' });
    expect(mockOnAnalyze).toHaveBeenCalled();
  });

  it('does not call onAnalyze when Tab key is pressed with short text', () => {
    render(<VintageTextEditor {...defaultProps} text="short" />);
    const textarea = screen.getByPlaceholderText(/在这里开始写作/);
    fireEvent.keyDown(textarea, { key: 'Tab', code: 'Tab' });
    expect(mockOnAnalyze).not.toHaveBeenCalled();
  });

  it('renders TextHighlightOverlay when suggestions are present', () => {
    const suggestions = [{ id: '1', original: 'test', replacement: 'testing' }];
    const props = { ...defaultProps, suggestions };
    render(<VintageTextEditor {...props} />);

    expect(screen.getByTestId('text-highlight-overlay')).toBeInTheDocument();
  });
});
