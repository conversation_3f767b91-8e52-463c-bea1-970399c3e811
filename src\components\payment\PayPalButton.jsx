import { useEffect, useRef, useState } from 'react';
import { useAppContext } from '../../context/AppContext';
import { updateUserSettings } from '../../services/user/userSettingsService';

const PayPalButton = ({
  planType = 'basic',
  amount = '20.00',
  currency = 'USD',
  user = null,
  onSuccess = null,
  onError = null,
  onCancel = null,
  className = '',
  disabled = false
}) => {
  const paypalRef = useRef();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { dispatch } = useAppContext();

  // 使用传入的user参数或从context获取
  const currentUser = user || useAppContext().user;

  // PayPal客户端ID (测试环境)
  const PAYPAL_CLIENT_ID = import.meta.env.VITE_PAYPAL_CLIENT_ID || 'demo-client-id';

  console.log('PayPal Client ID:', PAYPAL_CLIENT_ID);
  console.log('Environment variables:', import.meta.env);

  useEffect(() => {
    console.log('PayPal Button useEffect triggered');
    console.log('Client ID:', PAYPAL_CLIENT_ID);
    console.log('Amount:', amount);
    console.log('Currency:', currency);
    console.log('Plan Type:', planType);

    // 演示模式：不加载真实的PayPal SDK
    const initializeDemoMode = () => {
      if (PAYPAL_CLIENT_ID === 'demo-client-id' || !PAYPAL_CLIENT_ID || PAYPAL_CLIENT_ID.startsWith('your_') || PAYPAL_CLIENT_ID.includes('demo')) {
        console.log('Running in demo mode');
        // 演示模式：显示模拟的PayPal按钮
        setIsLoading(false);
        return;
      }

      // 真实模式：动态加载PayPal SDK
      console.log('Loading PayPal SDK...');
      loadPayPalScript();
    };

    const loadPayPalScript = () => {
      if (window.paypal) {
        console.log('PayPal SDK already loaded');
        initializePayPal();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${PAYPAL_CLIENT_ID}&currency=${currency}&intent=capture`;
      script.async = true;
      script.onload = () => {
        console.log('PayPal SDK loaded successfully');
        setIsLoading(false);
        initializePayPal();
      };
      script.onerror = (error) => {
        console.error('Failed to load PayPal SDK:', error);
        console.error('Script src:', script.src);
        setError('PayPal SDK加载失败，请检查网络连接或客户端ID');
        setIsLoading(false);
      };
      document.head.appendChild(script);
    };

    const initializePayPal = () => {
      if (!window.paypal || !paypalRef.current) {
        console.error('PayPal SDK not loaded or ref not available');
        return;
      }

      // 清空之前的按钮
      paypalRef.current.innerHTML = '';

      try {
        window.paypal.Buttons({
          style: {
            layout: 'vertical',
            color: 'gold',
            shape: 'rect',
            label: 'paypal',
            height: 50
          },

          createOrder: function (data, actions) {
            return actions.order.create({
              purchase_units: [{
                amount: {
                  currency_code: currency,
                  value: amount.toString()
                },
                description: getOrderDescription(planType)
              }],
              application_context: {
                brand_name: '英语写作助手',
                locale: 'zh-CN',
                shipping_preference: 'NO_SHIPPING',
                user_action: 'PAY_NOW',
                return_url: window.location.origin + '/payment/success',
                cancel_url: window.location.origin + '/payment/cancel'
              }
            });
          },

          onApprove: function (data, actions) {
            return actions.order.capture().then(async function (details) {
              try {
                console.log('PayPal付款成功:', details);

                // 更新用户订阅状态
                if (currentUser?.uid) {
                  const subscriptionData = {
                    subscription: {
                      plan: planType,
                      status: 'active',
                      paypalOrderId: data.orderID,
                      paypalPayerId: details.payer.payer_id,
                      startDate: new Date().toISOString(),
                      expiresAt: getExpirationDate(planType),
                      paymentDetails: details
                    }
                  };

                  await updateUserSettings(currentUser.uid, subscriptionData);

                  if (onSuccess) {
                    onSuccess({
                      orderId: data.orderID,
                      payerId: details.payer.payer_id,
                      planType,
                      amount,
                      details
                    });
                  }
                }
              } catch (error) {
                console.error('付款处理失败:', error);
                if (onError) {
                  onError(error);
                }
              }
            });
          },

          onError: function (err) {
            console.error('PayPal错误:', err);
            setError('支付过程中发生错误');
            if (onError) {
              onError(err);
            }
          },

          onCancel: function (data) {
            console.log('用户取消支付:', data);
            if (onCancel) {
              onCancel(data);
            }
          }
        }).render(paypalRef.current);
      } catch (error) {
        console.error('Error initializing PayPal buttons:', error);
        setError('PayPal按钮初始化失败');
        setIsLoading(false);
      }
    };

    initializeDemoMode();

    // 清理函数
    return () => {
      const scripts = document.querySelectorAll('script[src*="paypal.com/sdk/js"]');
      scripts.forEach(script => script.remove());
    };
  }, [planType, amount, currency, user]);

  // 获取订单描述
  const getOrderDescription = (plan) => {
    const descriptions = {
      basic: '英语写作助手 - 基础月付计划',
      annual: '英语写作助手 - 年付计划'
    };
    return descriptions[plan] || descriptions.basic;
  };

  // 计算到期时间
  const getExpirationDate = (plan) => {
    const now = new Date();
    if (plan === 'annual') {
      now.setFullYear(now.getFullYear() + 1);
    } else {
      now.setMonth(now.getMonth() + 1);
    }
    return now.toISOString();
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
          <span style={{ color: '#8B4513' }}>加载支付按钮...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 rounded-lg ${className}`} style={{
        backgroundColor: '#FEF2F2',
        border: '1px solid #FECACA',
        color: '#B91C1C'
      }}>
        <p className="text-sm">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-xs underline hover:no-underline"
        >
          重新加载
        </button>
      </div>
    );
  }

  // 演示模式渲染
  if (PAYPAL_CLIENT_ID === 'demo-client-id') {
    return (
      <div className={className}>
        <button
          onClick={() => {
            if (disabled) return;
            // 模拟支付成功
            setTimeout(() => {
              if (onSuccess) {
                onSuccess({
                  subscriptionId: 'DEMO-SUBSCRIPTION-' + Date.now(),
                  planType,
                  amount
                });
              }
            }, 2000);
          }}
          disabled={disabled}
          className="w-full py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105"
          style={{
            background: disabled ? '#F3F4F6' : 'linear-gradient(135deg, #0070BA, #003087)',
            color: disabled ? '#9CA3AF' : '#FFFFFF',
            border: 'none',
            cursor: disabled ? 'not-allowed' : 'pointer',
            opacity: disabled ? 0.5 : 1
          }}
        >
          {disabled ? '请先登录' : '💳 PayPal支付 (演示)'}
        </button>
        {disabled && (
          <p className="text-xs mt-2 text-center" style={{ color: '#8B4513' }}>
            请先登录后再进行支付
          </p>
        )}
      </div>
    );
  }

  return (
    <div className={className}>
      <div
        ref={paypalRef}
        style={{
          opacity: disabled ? 0.5 : 1,
          pointerEvents: disabled ? 'none' : 'auto'
        }}
      />
      {disabled && (
        <p className="text-xs mt-2 text-center" style={{ color: '#8B4513' }}>
          请先登录后再进行支付
        </p>
      )}
    </div>
  );
};

export default PayPalButton;
