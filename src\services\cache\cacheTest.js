/**
 * 缓存功能测试脚本
 * 用于验证本地缓存同步功能是否正常工作
 */

import localCacheService from './localCacheService';

/**
 * 测试缓存基本功能
 */
export const testCacheBasic = async () => {
  console.log('🧪 开始测试缓存基本功能...');
  
  try {
    // 测试设置和获取缓存
    const testData = { message: 'Hello World', timestamp: Date.now() };
    localCacheService.setCache('test_cache', testData);
    
    const retrievedData = localCacheService.getCache('test_cache', 60000);
    if (JSON.stringify(retrievedData) === JSON.stringify(testData)) {
      console.log('✅ 缓存设置和获取测试通过');
    } else {
      console.log('❌ 缓存设置和获取测试失败');
      return false;
    }
    
    // 测试缓存过期
    localCacheService.setCache('test_expiry', testData);
    const expiredData = localCacheService.getCache('test_expiry', 1); // 1ms过期
    if (expiredData === null) {
      console.log('✅ 缓存过期测试通过');
    } else {
      console.log('❌ 缓存过期测试失败');
      return false;
    }
    
    // 清理测试数据
    localCacheService.clearCache('test_cache');
    localCacheService.clearCache('test_expiry');
    
    console.log('✅ 缓存基本功能测试全部通过');
    return true;
  } catch (error) {
    console.error('❌ 缓存基本功能测试失败:', error);
    return false;
  }
};

/**
 * 测试聊天历史缓存
 */
export const testChatHistoryCache = async () => {
  console.log('🧪 开始测试聊天历史缓存...');
  
  try {
    // 模拟聊天历史数据
    const mockChatHistory = [
      {
        id: '1',
        title: '测试对话1',
        messages: [
          { type: 'user', content: 'Hello' },
          { type: 'ai', content: 'Hi there!' }
        ],
        timestamp: new Date().toISOString()
      },
      {
        id: '2',
        title: '测试对话2',
        messages: [
          { type: 'user', content: 'How are you?' },
          { type: 'ai', content: 'I am fine, thank you!' }
        ],
        timestamp: new Date().toISOString()
      }
    ];
    
    // 设置缓存
    localCacheService.setCache('cache_chat_history', mockChatHistory);
    
    // 获取缓存
    const cachedHistory = localCacheService.getCache('cache_chat_history', 300000); // 5分钟
    
    if (cachedHistory && cachedHistory.length === 2) {
      console.log('✅ 聊天历史缓存测试通过');
      console.log(`📊 缓存了 ${cachedHistory.length} 条聊天记录`);
    } else {
      console.log('❌ 聊天历史缓存测试失败');
      return false;
    }
    
    // 清理测试数据
    localCacheService.clearCache('cache_chat_history');
    
    return true;
  } catch (error) {
    console.error('❌ 聊天历史缓存测试失败:', error);
    return false;
  }
};

/**
 * 测试缓存统计功能
 */
export const testCacheStats = () => {
  console.log('🧪 开始测试缓存统计功能...');
  
  try {
    // 设置一些测试缓存
    localCacheService.setCache('test_stats_1', { data: 'test1' });
    localCacheService.setCache('test_stats_2', { data: 'test2' });
    
    // 获取统计信息
    const stats = localCacheService.getCacheStats();
    
    if (stats && typeof stats.totalSize === 'number' && stats.itemCount >= 2) {
      console.log('✅ 缓存统计功能测试通过');
      console.log(`📊 缓存统计:`, {
        总大小: `${Math.round(stats.totalSize / 1024)}KB`,
        条目数: stats.itemCount,
        在线状态: stats.isOnline,
        同步状态: stats.syncInProgress
      });
    } else {
      console.log('❌ 缓存统计功能测试失败');
      return false;
    }
    
    // 清理测试数据
    localCacheService.clearCache('test_stats_1');
    localCacheService.clearCache('test_stats_2');
    
    return true;
  } catch (error) {
    console.error('❌ 缓存统计功能测试失败:', error);
    return false;
  }
};

/**
 * 测试缓存清理功能
 */
export const testCacheCleanup = () => {
  console.log('🧪 开始测试缓存清理功能...');
  
  try {
    // 设置一些测试缓存
    localCacheService.setCache('test_cleanup_1', { data: 'test1' });
    localCacheService.setCache('test_cleanup_2', { data: 'test2' });
    
    // 验证缓存存在
    const beforeStats = localCacheService.getCacheStats();
    console.log(`📊 清理前缓存条目数: ${beforeStats.itemCount}`);
    
    // 清理所有缓存
    localCacheService.clearAllCache();
    
    // 验证缓存已清理
    const afterStats = localCacheService.getCacheStats();
    console.log(`📊 清理后缓存条目数: ${afterStats.itemCount}`);
    
    if (afterStats.itemCount < beforeStats.itemCount) {
      console.log('✅ 缓存清理功能测试通过');
      return true;
    } else {
      console.log('❌ 缓存清理功能测试失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 缓存清理功能测试失败:', error);
    return false;
  }
};

/**
 * 测试同步状态监听
 */
export const testSyncListeners = () => {
  console.log('🧪 开始测试同步状态监听...');
  
  try {
    let listenerCalled = false;
    
    // 添加监听器
    const testListener = (status) => {
      console.log(`📡 同步状态变化: ${status}`);
      listenerCalled = true;
    };
    
    localCacheService.addSyncListener(testListener);
    
    // 模拟同步状态变化
    localCacheService.syncCallbacks.forEach(callback => callback('test'));
    
    // 移除监听器
    localCacheService.removeSyncListener(testListener);
    
    if (listenerCalled) {
      console.log('✅ 同步状态监听测试通过');
      return true;
    } else {
      console.log('❌ 同步状态监听测试失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 同步状态监听测试失败:', error);
    return false;
  }
};

/**
 * 运行所有缓存测试
 */
export const runAllCacheTests = async () => {
  console.log('🚀 开始运行所有缓存测试...');
  console.log('='.repeat(50));
  
  const tests = [
    { name: '基本功能', test: testCacheBasic },
    { name: '聊天历史缓存', test: testChatHistoryCache },
    { name: '缓存统计', test: testCacheStats },
    { name: '缓存清理', test: testCacheCleanup },
    { name: '同步监听', test: testSyncListeners }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    console.log(`\n📋 运行测试: ${name}`);
    try {
      const result = await test();
      if (result) {
        passedTests++;
        console.log(`✅ ${name} 测试通过`);
      } else {
        console.log(`❌ ${name} 测试失败`);
      }
    } catch (error) {
      console.error(`❌ ${name} 测试出错:`, error);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有缓存测试通过！');
  } else {
    console.log('⚠️ 部分测试失败，请检查缓存功能');
  }
  
  return passedTests === totalTests;
};

/**
 * 性能测试
 */
export const testCachePerformance = async () => {
  console.log('🧪 开始缓存性能测试...');
  
  try {
    const testData = Array.from({ length: 100 }, (_, i) => ({
      id: i,
      content: `测试消息 ${i}`,
      timestamp: Date.now()
    }));
    
    // 测试设置缓存性能
    const setStartTime = performance.now();
    localCacheService.setCache('performance_test', testData);
    const setEndTime = performance.now();
    const setTime = setEndTime - setStartTime;
    
    // 测试获取缓存性能
    const getStartTime = performance.now();
    const retrievedData = localCacheService.getCache('performance_test', 300000);
    const getEndTime = performance.now();
    const getTime = getEndTime - getStartTime;
    
    console.log('📊 性能测试结果:');
    console.log(`  设置缓存: ${setTime.toFixed(2)}ms`);
    console.log(`  获取缓存: ${getTime.toFixed(2)}ms`);
    console.log(`  数据大小: ${JSON.stringify(testData).length} 字符`);
    
    if (setTime < 10 && getTime < 5) {
      console.log('✅ 缓存性能测试通过');
      return true;
    } else {
      console.log('⚠️ 缓存性能可能需要优化');
      return false;
    }
  } catch (error) {
    console.error('❌ 缓存性能测试失败:', error);
    return false;
  } finally {
    // 清理测试数据
    localCacheService.clearCache('performance_test');
  }
};

// 导出测试函数供控制台使用
if (typeof window !== 'undefined') {
  window.cacheTests = {
    testCacheBasic,
    testChatHistoryCache,
    testCacheStats,
    testCacheCleanup,
    testSyncListeners,
    runAllCacheTests,
    testCachePerformance
  };
  
  console.log('🔧 缓存测试函数已加载到 window.cacheTests');
  console.log('💡 使用方法: await window.cacheTests.runAllCacheTests()');
}

