import { vi, describe, it, expect, beforeEach } from 'vitest';

// 1. Mock the dependencies
const mockFirebaseChatService = {
  saveChatSession: vi.fn(),
  getChatHistory: vi.fn(),
  deleteChatSession: vi.fn(),
  clearAllChatHistory: vi.fn(),
};
const mockFirebaseMigrationService = {
  migrateAllHistory: vi.fn().mockResolvedValue({}), // Ensure it returns a Promise
};
vi.mock('./firebaseHistoryService', () => ({
  chatHistoryService: mockFirebaseChatService,
  aiAnalysisService: { saveAnalysis: vi.fn(), getAnalysisHistory: vi.fn() },
  dictionarySearchService: { getSearchHistory: vi.fn() },
  writingHistoryService: { getWritingHistory: vi.fn() },
  diaryHistoryService: { getDiaryHistory: vi.fn() },
  migrationService: mockFirebaseMigrationService,
}));

const mockLocalChatService = {
  saveChatSession: vi.fn(),
  getChatHistory: vi.fn(),
  deleteChatSession: vi.fn(),
  clearAllChatHistory: vi.fn(),
};
vi.mock('../chat/chatHistoryService', () => ({ ...mockLocalChatService }));
vi.mock('../writing/historyService', async () => ({}));
vi.mock('../writing/diaryService', async () => ({}));

// 2. Dynamically import the module to be tested *after* mocks are set up
const {   setStorageMode, getStorageMode, hybridChatHistoryService, STORAGE_MODE } = await import('./hybridHistoryService.js');

describe('Hybrid History Service', () => {
  const mockUserId = 'user123';

  beforeEach(() => {
    vi.clearAllMocks();
    setStorageMode(STORAGE_MODE.LOCAL, null);
    // Mock localStorage for each test to ensure isolation
    const store = {};
    global.localStorage = {
      getItem: vi.fn(key => store[key] || null),
      setItem: vi.fn((key, value) => { store[key] = value; }),
      clear: vi.fn(() => { store = {}; })
    };
  });

  describe('Storage Mode Management', () => {
    it('should default to LOCAL mode', () => {
      expect(getStorageMode()).toBe(STORAGE_MODE.LOCAL);
    });

    it('should switch to FIREBASE mode with a userId', () => {
      setStorageMode(STORAGE_MODE.FIREBASE, mockUserId);
      expect(getStorageMode()).toBe(STORAGE_MODE.FIREBASE);
    });

    it('should trigger migration when switching to FIREBASE mode for the first time', () => {
      setStorageMode(STORAGE_MODE.FIREBASE, mockUserId);
      expect(mockFirebaseMigrationService.migrateAllHistory).toHaveBeenCalledWith(mockUserId);
    });

    it('should not trigger migration if it has run before', () => {
      localStorage.setItem(`migration_completed_${mockUserId}`, 'true');
      setStorageMode(STORAGE_MODE.FIREBASE, mockUserId);
      expect(mockFirebaseMigrationService.migrateAllHistory).not.toHaveBeenCalled();
    });
  });

  describe('Operations in FIREBASE mode', () => {
    beforeEach(() => {
      setStorageMode(STORAGE_MODE.FIREBASE, mockUserId);
    });

    it('saveChatSession should call firebase service', async () => {
      const sessionData = { id: '1', content: 'test' };
      await hybridChatHistoryService.saveChatSession(sessionData);
      expect(mockFirebaseChatService.saveChatSession).toHaveBeenCalledWith(sessionData, mockUserId);
      expect(mockLocalChatService.saveChatSession).not.toHaveBeenCalled();
    });

    it('getChatHistory should call firebase service', async () => {
      await hybridChatHistoryService.getChatHistory();
      expect(mockFirebaseChatService.getChatHistory).toHaveBeenCalledWith(mockUserId, 50);
      expect(mockLocalChatService.getChatHistory).not.toHaveBeenCalled();
    });

    it('deleteChatSession should call firebase service', async () => {
      await hybridChatHistoryService.deleteChatSession('1');
      expect(mockFirebaseChatService.deleteChatSession).toHaveBeenCalledWith(mockUserId, '1');
      expect(mockLocalChatService.deleteChatSession).not.toHaveBeenCalled();
    });

    it('clearAllChatHistory should call firebase service', async () => {
      await hybridChatHistoryService.clearAllChatHistory();
      expect(mockFirebaseChatService.clearAllChatHistory).toHaveBeenCalledWith(mockUserId);
      expect(mockLocalChatService.clearAllChatHistory).not.toHaveBeenCalled();
    });

    it('should fall back to local storage if firebase fails', async () => {
      const error = new Error('Firebase failed');
      mockFirebaseChatService.saveChatSession.mockRejectedValue(error);
      
      const sessionData = { id: '1', content: 'test' };
      await hybridChatHistoryService.saveChatSession(sessionData);

      // It tried firebase first
      expect(mockFirebaseChatService.saveChatSession).toHaveBeenCalledWith(sessionData, mockUserId);
      // Then fell back to local
      expect(mockLocalChatService.saveChatSession).toHaveBeenCalledWith(sessionData);
    });
  });

  describe('Operations in LOCAL mode', () => {
    beforeEach(() => {
      setStorageMode(STORAGE_MODE.LOCAL, null);
    });

    it('saveChatSession should call local service', async () => {
      const sessionData = { id: '1', content: 'test' };
      await hybridChatHistoryService.saveChatSession(sessionData);
      expect(mockLocalChatService.saveChatSession).toHaveBeenCalledWith(sessionData);
      expect(mockFirebaseChatService.saveChatSession).not.toHaveBeenCalled();
    });

    it('getChatHistory should call local service', async () => {
      await hybridChatHistoryService.getChatHistory();
      expect(mockLocalChatService.getChatHistory).toHaveBeenCalled();
      expect(mockFirebaseChatService.getChatHistory).not.toHaveBeenCalled();
    });

    it('deleteChatSession should call local service', async () => {
      await hybridChatHistoryService.deleteChatSession('1');
      expect(mockLocalChatService.deleteChatSession).toHaveBeenCalledWith('1');
      expect(mockFirebaseChatService.deleteChatSession).not.toHaveBeenCalled();
    });

    it('clearAllChatHistory should call local service', async () => {
      await hybridChatHistoryService.clearAllChatHistory();
      expect(mockLocalChatService.clearAllChatHistory).toHaveBeenCalled();
      expect(mockFirebaseChatService.clearAllChatHistory).not.toHaveBeenCalled();
    });
  });
});
