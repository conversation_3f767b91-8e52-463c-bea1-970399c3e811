import { 
  chatHistoryService, 
  aiAnalysisService, 
  dictionarySearchService, 
  writingHistoryService, 
  diaryHistoryService,
  migrationService 
} from './firebaseHistoryService';
import { generateUniqueId } from '../../utils/idGenerator';

/**
 * 混合存储服务
 * 自动在本地存储和Firebase存储之间切换
 * 支持离线模式和在线同步
 */

// 存储模式枚举
export const STORAGE_MODE = {
  LOCAL: 'local',
  FIREBASE: 'firebase',
  HYBRID: 'hybrid'
};

// 当前存储模式
let currentStorageMode = STORAGE_MODE.LOCAL;
let currentUserId = null;

/**
 * 设置存储模式
 * @param {string} mode - 存储模式
 * @param {string} userId - 用户ID（Firebase模式必需）
 */
export const setStorageMode = (mode, userId = null) => {
  currentStorageMode = mode;
  currentUserId = userId;
  
  console.log(`🔄 存储模式已切换到: ${mode}${userId ? ` (用户: ${userId})` : ''}`);
  
  // 如果切换到Firebase模式且有用户ID，检查是否需要迁移数据
  if (mode === STORAGE_MODE.FIREBASE && userId) {
    // 检查是否已经迁移过数据
    const migrationKey = `migration_completed_${userId}`;
    const hasMigrated = localStorage.getItem(migrationKey);
    
    if (!hasMigrated) {
      console.log('🔄 首次登录，开始数据迁移...');
      migrationService.migrateAllHistory(userId)
        .then(results => {
          console.log('📦 数据迁移完成:', results);
          // 标记迁移完成
          localStorage.setItem(migrationKey, 'true');
        })
        .catch(error => {
          console.error('数据迁移失败:', error);
        });
    } else {
      console.log('✅ 数据已迁移过，跳过迁移过程');
    }
  }
};

/**
 * 获取当前存储模式
 * @returns {string} 当前存储模式
 */
export const getStorageMode = () => currentStorageMode;

/**
 * 检查是否可以使用Firebase
 * @returns {boolean} 是否可以使用Firebase
 */
const canUseFirebase = () => {
  const result = currentStorageMode === STORAGE_MODE.FIREBASE && currentUserId;
  console.log('🔍 canUseFirebase检查:', {
    currentStorageMode,
    currentUserId,
    result
  });
  return result;
};

/**
 * 聊天历史记录混合服务
 */
export const hybridChatHistoryService = {
  /**
   * 保存聊天会话
   * @param {Object} sessionData - 会话数据
   * @returns {Promise<Object>} 保存的会话数据
   */
  async saveChatSession(sessionData) {
    try {
      if (canUseFirebase()) {
        // 使用Firebase存储
        return await chatHistoryService.saveChatSession(sessionData, currentUserId);
      } else {
        // 使用本地存储
        const { saveChatSession: saveLocal } = await import('../chat/chatHistoryService');
        return saveLocal(sessionData);
      }
    } catch (error) {
      console.error('保存聊天会话失败，回退到本地存储:', error);
      // 回退到本地存储
      const { saveChatSession: saveLocal } = await import('../chat/chatHistoryService');
      return saveLocal(sessionData);
    }
  },

  /**
   * 获取聊天历史
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 聊天历史数组
   */
  async getChatHistory(limit = 50) {
    try {
      if (canUseFirebase()) {
        // 从Firebase获取
        return await chatHistoryService.getChatHistory(currentUserId, limit);
      } else {
        // 从本地存储获取
        const { getChatHistory: getLocal } = await import('../chat/chatHistoryService');
        return getLocal();
      }
    } catch (error) {
      console.error('获取聊天历史失败，回退到本地存储:', error);
      // 回退到本地存储
      const { getChatHistory: getLocal } = await import('../chat/chatHistoryService');
      return getLocal();
    }
  },

  /**
   * 删除聊天会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<void>}
   */
  async deleteChatSession(sessionId) {
    try {
      if (canUseFirebase()) {
        await chatHistoryService.deleteChatSession(currentUserId, sessionId);
      } else {
        const { deleteChatSession: deleteLocal } = await import('../chat/chatHistoryService');
        deleteLocal(sessionId);
      }
    } catch (error) {
      console.error('删除聊天会话失败:', error);
      throw error;
    }
  },

  /**
   * 清空所有聊天历史
   * @returns {Promise<void>}
   */
  async clearAllChatHistory() {
    try {
      if (canUseFirebase()) {
        await chatHistoryService.clearAllChatHistory(currentUserId);
      } else {
        const { clearAllChatHistory: clearLocal } = await import('../chat/chatHistoryService');
        clearLocal();
      }
    } catch (error) {
      console.error('清空聊天历史失败:', error);
      throw error;
    }
  }
};

/**
 * AI分析历史记录混合服务
 */
export const hybridAnalysisService = {
  /**
   * 保存AI分析记录
   * @param {Object} analysisData - 分析数据
   * @returns {Promise<Object>} 保存的分析数据
   */
  async saveAnalysis(analysisData) {
    console.log('🔍 hybridAnalysisService.saveAnalysis 被调用:', analysisData);
    console.log('🔍 当前存储模式:', currentStorageMode);
    console.log('🔍 当前用户ID:', currentUserId);
    
    try {
      if (canUseFirebase()) {
        console.log('📱 使用Firebase存储AI分析记录');
        const result = await aiAnalysisService.saveAnalysis(analysisData, currentUserId);
        console.log('✅ Firebase存储成功:', result);
        return result;
      } else {
        console.log('💾 使用本地存储AI分析记录');
        const { saveAnalysisToHistory: saveLocal } = await import('../writing/historyService');
        // 确保数据结构兼容性
        const compatibleData = {
          text: analysisData.text,
          rawAnalysis: analysisData.rawAnalysis,
          analysis: analysisData.suggestions || analysisData.analysis
        };
        console.log('📊 兼容性数据:', compatibleData);
        saveLocal(compatibleData);
        console.log('✅ 本地存储成功');
        return compatibleData;
      }
    } catch (error) {
      console.error('❌ 保存AI分析记录失败，回退到本地存储:', error);
      const { saveAnalysisToHistory: saveLocal } = await import('../writing/historyService');
      // 确保数据结构兼容性
      const compatibleData = {
        text: analysisData.text,
        rawAnalysis: analysisData.rawAnalysis,
        analysis: analysisData.suggestions || analysisData.analysis
      };
      console.log('📊 回退到本地存储，兼容性数据:', compatibleData);
      saveLocal(compatibleData);
      console.log('✅ 本地存储回退成功');
      return compatibleData;
    }
  },

  /**
   * 获取AI分析历史
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 分析历史数组
   */
  async getAnalysisHistory(limit = 50) {
    try {
      if (canUseFirebase()) {
        return await aiAnalysisService.getAnalysisHistory(currentUserId, limit);
      } else {
        const { getAnalysisHistory: getLocal } = await import('../writing/historyService');
        return getLocal();
      }
    } catch (error) {
      console.error('获取AI分析历史失败，回退到本地存储:', error);
      const { getAnalysisHistory: getLocal } = await import('../writing/historyService');
      return getLocal();
    }
  },

  /**
   * 删除AI分析记录
   * @param {string} analysisId - 分析记录ID
   * @returns {Promise<void>}
   */
  async deleteAnalysis(analysisId) {
    try {
      if (canUseFirebase()) {
        await aiAnalysisService.deleteAnalysis(currentUserId, analysisId);
      } else {
        const { deleteHistoryRecord: deleteLocal } = await import('../writing/historyService');
        deleteLocal(analysisId);
      }
    } catch (error) {
      console.error('删除AI分析记录失败:', error);
      throw error;
    }
  },

  /**
   * 清空所有AI分析历史
   * @returns {Promise<void>}
   */
  async clearAllAnalysisHistory() {
    try {
      if (canUseFirebase()) {
        await aiAnalysisService.clearAllAnalysisHistory(currentUserId);
      } else {
        const { clearAllHistory: clearLocal } = await import('../writing/historyService');
        clearLocal();
      }
    } catch (error) {
      console.error('清空AI分析历史失败:', error);
      throw error;
    }
  }
};

/**
 * 字典搜索历史记录混合服务
 */
export const hybridDictionarySearchService = {
  /**
   * 保存搜索词
   * @param {string} term - 搜索词
   * @returns {Promise<void>}
   */
  async saveSearchTerm(term) {
    try {
      if (canUseFirebase()) {
        await dictionarySearchService.saveSearchTerm(term, currentUserId);
      } else {
        const { addSearchToHistory: saveLocal } = await import('../writing/historyService');
        saveLocal(term);
      }
    } catch (error) {
      console.error('保存搜索词失败，回退到本地存储:', error);
      const { addSearchToHistory: saveLocal } = await import('../writing/historyService');
      saveLocal(term);
    }
  },

  /**
   * 获取搜索历史
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 搜索历史数组
   */
  async getSearchHistory(limit = 20) {
    try {
      if (canUseFirebase()) {
        const history = await dictionarySearchService.getSearchHistory(currentUserId, limit);
        return history.map(item => item.term); // 转换为字符串数组以保持兼容性
      } else {
        const { getSearchHistory: getLocal } = await import('../writing/historyService');
        return getLocal();
      }
    } catch (error) {
      console.error('获取搜索历史失败，回退到本地存储:', error);
      const { getSearchHistory: getLocal } = await import('../writing/historyService');
      return getLocal();
    }
  },

  /**
   * 清空搜索历史
   * @returns {Promise<void>}
   */
  async clearSearchHistory() {
    try {
      if (canUseFirebase()) {
        await dictionarySearchService.clearSearchHistory(currentUserId);
      } else {
        const { clearSearchHistory: clearLocal } = await import('../writing/historyService');
        clearLocal();
      }
    } catch (error) {
      console.error('清空搜索历史失败:', error);
      throw error;
    }
  }
};

/**
 * 写作历史记录混合服务
 */
export const hybridWritingHistoryService = {
  /**
   * 保存写作记录
   * @param {Object} writingData - 写作数据
   * @returns {Promise<Object>} 保存的写作数据
   */
  async saveWriting(writingData) {
    try {
      if (canUseFirebase()) {
        return await writingHistoryService.saveWriting(writingData, currentUserId);
      } else {
        // 本地存储的写作历史保存逻辑
        const history = JSON.parse(localStorage.getItem('writing_history') || '[]');
        const newRecord = {
          id: generateUniqueId(),
          ...writingData,
          timestamp: new Date().toISOString()
        };
        
        const updatedHistory = [newRecord, ...history];
        if (updatedHistory.length > 50) {
          updatedHistory.splice(50);
        }
        
        localStorage.setItem('writing_history', JSON.stringify(updatedHistory));
        return newRecord;
      }
    } catch (error) {
      console.error('保存写作记录失败，回退到本地存储:', error);
      // 回退到本地存储
      const history = JSON.parse(localStorage.getItem('writing_history') || '[]');
      const newRecord = {
        id: generateUniqueId(),
        ...writingData,
        timestamp: new Date().toISOString()
      };
      
      const updatedHistory = [newRecord, ...history];
      if (updatedHistory.length > 50) {
        updatedHistory.splice(50);
      }
      
      localStorage.setItem('writing_history', JSON.stringify(updatedHistory));
      return newRecord;
    }
  },

  /**
   * 获取写作历史
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 写作历史数组
   */
  async getWritingHistory(limit = 50) {
    try {
      if (canUseFirebase()) {
        return await writingHistoryService.getWritingHistory(currentUserId, limit);
      } else {
        const history = localStorage.getItem('writing_history');
        const parsedHistory = history ? JSON.parse(history) : [];
        return parsedHistory.slice(0, limit);
      }
    } catch (error) {
      console.error('获取写作历史失败，回退到本地存储:', error);
      const history = localStorage.getItem('writing_history');
      const parsedHistory = history ? JSON.parse(history) : [];
      return parsedHistory.slice(0, limit);
    }
  }
};

/**
 * 日记历史记录混合服务
 */
export const hybridDiaryHistoryService = {
  /**
   * 保存日记
   * @param {Object} diaryData - 日记数据
   * @returns {Promise<Object>} 保存的日记数据
   */
  async saveDiary(diaryData) {
    try {
      if (canUseFirebase()) {
        return await diaryHistoryService.saveDiary(diaryData, currentUserId);
      } else {
        const { saveDiary: saveLocal } = await import('../writing/diaryService');
        return saveLocal(diaryData);
      }
    } catch (error) {
      console.error('保存日记失败，回退到本地存储:', error);
      const { saveDiary: saveLocal } = await import('../writing/diaryService');
      return saveLocal(diaryData);
    }
  },

  /**
   * 获取日记历史
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 日记历史数组
   */
  async getDiaryHistory(limit = 30) {
    try {
      if (canUseFirebase()) {
        return await diaryHistoryService.getDiaryHistory(currentUserId, limit);
      } else {
        const { getDiaries: getLocal } = await import('../writing/diaryService');
        return getLocal();
      }
    } catch (error) {
      console.error('获取日记历史失败，回退到本地存储:', error);
      const { getDiaries: getLocal } = await import('../writing/diaryService');
      return getLocal();
    }
  }
};

/**
 * 初始化混合存储服务
 * @param {string} mode - 初始存储模式
 * @param {string} userId - 用户ID
 */
export const initializeHybridStorage = (mode = STORAGE_MODE.LOCAL, userId = null) => {
  setStorageMode(mode, userId);
  
  console.log('🚀 混合存储服务初始化完成');
  console.log(`📱 当前模式: ${mode}`);
  console.log(`👤 用户ID: ${userId || '未登录'}`);
  
  return {
    mode: currentStorageMode,
    userId: currentUserId,
    canUseFirebase: canUseFirebase()
  };
};

/**
 * 获取存储状态信息
 * @returns {Object} 存储状态信息
 */
export const getStorageStatus = () => ({
  mode: currentStorageMode,
  userId: currentUserId,
  canUseFirebase: canUseFirebase(),
  isOnline: navigator.onLine
});
