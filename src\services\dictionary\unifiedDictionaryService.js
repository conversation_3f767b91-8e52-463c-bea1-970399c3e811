// 统一词典服务
// 整合多个词典数据源，提供统一的查询接口

import { getWordDetails as getFreeDictionaryDetails } from './freeDictionaryService';
import { getWordDetails as getEcdictDetails, initEcdict } from './ecdictService';

// 词典服务配置
const DICTIONARY_CONFIG = {
  // 服务优先级（数字越小优先级越高）
  FREE_DICTIONARY: 1,
  ECDICT: 2,
  // 可以添加更多词典服务
};

// 服务状态
let servicesInitialized = false;

// 初始化所有词典服务
const initializeServices = async () => {
  if (servicesInitialized) return true;
  
  try {
    console.log('初始化词典服务...');
    
    // 初始化 ECDICT
    const ecdictInitialized = await initEcdict();
    if (ecdictInitialized) {
      console.log('ECDICT 服务初始化成功');
    } else {
      console.warn('ECDICT 服务初始化失败');
    }
    
    servicesInitialized = true;
    console.log('词典服务初始化完成');
    return true;
  } catch (error) {
    console.error('初始化词典服务失败:', error);
    return false;
  }
};

// 查询单词（按优先级尝试不同服务）
const getWordDetails = async (word, preferredService = null) => {
  if (!servicesInitialized) {
    await initializeServices();
  }
  
  const normalizedWord = word.toLowerCase().trim();
  if (!normalizedWord) {
    return {
      word: word,
      notFound: true,
      error: '请输入有效的单词'
    };
  }
  
  // 根据用户偏好选择服务
  let services = [];
  
  if (preferredService === 'ecdict') {
    services = [
      { name: 'ECDICT', service: getEcdictDetails, priority: 1 }
    ];
  } else if (preferredService === 'free_dictionary') {
    services = [
      { name: 'Free Dictionary', service: getFreeDictionaryDetails, priority: 1 }
    ];
  } else {
    // 默认使用 ECDICT
    services = [
      { name: 'ECDICT', service: getEcdictDetails, priority: 1 }
    ];
  }
  
  // 按优先级排序
  services.sort((a, b) => a.priority - b.priority);
  
  let lastError = null;
  
  for (const { name, service } of services) {
    try {
      console.log(`尝试使用 ${name} 查询单词: ${normalizedWord}`);
      const result = await service(normalizedWord);
      
      if (result && !result.notFound) {
        console.log(`${name} 查询成功`);
        return {
          ...result,
          source: name,
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      console.warn(`${name} 查询失败:`, error);
      lastError = error;
    }
  }
  
  // 所有服务都失败
  console.log('所有词典服务查询失败');
  return {
    word: normalizedWord,
    notFound: true,
    error: lastError ? lastError.message : '未找到该单词',
    source: 'none',
    timestamp: new Date().toISOString()
  };
};

// 获取同义词
const getSynonyms = async (word) => {
  if (!servicesInitialized) {
    await initializeServices();
  }
  
  try {
    // 优先使用 Free Dictionary API 获取同义词
    const { getSynonyms: getFreeSynonyms } = await import('./freeDictionaryService');
    const synonyms = await getFreeSynonyms(word);
    
    if (synonyms && synonyms.length > 0) {
      return synonyms;
    }
  } catch (error) {
    console.warn('获取同义词失败:', error);
  }
  
  return [];
};

// 获取反义词
const getAntonyms = async (word) => {
  if (!servicesInitialized) {
    await initializeServices();
  }
  
  try {
    // 优先使用 Free Dictionary API 获取反义词
    const { getAntonyms: getFreeAntonyms } = await import('./freeDictionaryService');
    const antonyms = await getFreeAntonyms(word);
    
    if (antonyms && antonyms.length > 0) {
      return antonyms;
    }
  } catch (error) {
    console.warn('获取反义词失败:', error);
  }
  
  return [];
};

// 搜索单词（支持模糊匹配）
const searchWords = async (query, options = {}) => {
  if (!servicesInitialized) {
    await initializeServices();
  }
  
  const { limit = 20, fuzzy = true } = options;
  
  try {
    // 使用 ECDICT 进行搜索
    const { searchWords: ecdictSearch } = await import('./ecdictService');
    const results = await ecdictSearch(query, { limit, fuzzy });
    
    return results.map(result => ({
      ...result,
      source: 'ECDICT'
    }));
  } catch (error) {
    console.warn('搜索单词失败:', error);
    return [];
  }
};

// 获取词典统计信息
const getDictionaryStats = async () => {
  if (!servicesInitialized) {
    await initializeServices();
  }
  
  const stats = {
    services: [],
    totalWords: 0,
    isLoaded: servicesInitialized
  };
  
  try {
    // 获取 ECDICT 统计信息
    const { getDictionaryStats: getEcdictStats } = await import('./ecdictService');
    const ecdictStats = getEcdictStats();
    
    stats.services.push({
      name: 'ECDICT',
      words: ecdictStats.totalWords,
      loaded: ecdictStats.isLoaded
    });
    
    stats.totalWords += ecdictStats.totalWords;
  } catch (error) {
    console.warn('获取 ECDICT 统计信息失败:', error);
  }
  
  // 添加 Free Dictionary API 信息
  stats.services.push({
    name: 'Free Dictionary API',
    words: 'unlimited',
    loaded: true
  });
  
  return stats;
};

// 检查服务状态
const checkServiceStatus = async () => {
  const status = {
    initialized: servicesInitialized,
    services: {}
  };
  
  try {
    // 检查 ECDICT
    const { getDictionaryStats: getEcdictStats } = await import('./ecdictService');
    const ecdictStats = getEcdictStats();
    status.services.ecdict = {
      available: true,
      loaded: ecdictStats.isLoaded,
      words: ecdictStats.totalWords
    };
  } catch (error) {
    status.services.ecdict = {
      available: false,
      error: error.message
    };
  }
  
  // Free Dictionary API 总是可用的（在线服务）
  status.services.freeDictionary = {
    available: true,
    loaded: true,
    type: 'online'
  };
  
  return status;
};

// 预加载常用单词
const preloadCommonWords = async (words = []) => {
  if (!servicesInitialized) {
    await initializeServices();
  }
  
  const commonWords = words.length > 0 ? words : [
    'hello', 'world', 'good', 'bad', 'happy', 'sad', 'love', 'hate',
    'beautiful', 'ugly', 'big', 'small', 'fast', 'slow', 'hot', 'cold'
  ];
  
  console.log(`预加载 ${commonWords.length} 个常用单词...`);
  
  const results = await Promise.allSettled(
    commonWords.map(word => getWordDetails(word))
  );
  
  const successCount = results.filter(result => result.status === 'fulfilled' && !result.value.notFound).length;
  
  console.log(`预加载完成，成功加载 ${successCount}/${commonWords.length} 个单词`);
  
  return {
    total: commonWords.length,
    success: successCount,
    failed: commonWords.length - successCount
  };
};

export {
  initializeServices,
  getWordDetails,
  getSynonyms,
  getAntonyms,
  searchWords,
  getDictionaryStats,
  checkServiceStatus,
  preloadCommonWords
};
