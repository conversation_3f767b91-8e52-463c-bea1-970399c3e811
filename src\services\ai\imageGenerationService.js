/**
 * AI图像生成服务
 * 使用302.ai的gpt-4o-image-generation模型
 */

const API_URL = 'https://api.302.ai/v1/chat/completions';

/**
 * 生成图像
 * @param {string} text - 要转换为图像的文本内容
 * @returns {Promise<string>} - 返回生成的图像URL
 */
export const generateImageFromText = async (text) => {
  if (!text || text.trim() === '') {
    throw new Error('文本内容不能为空');
  }

  // 使用固定的API密钥
  const apiKey = 'sk-Zn4gmJLtpRc9grPTQDrkT0KWGS9wfhWR0lv8S8AxGJDvyp2F';

  try {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    };

    // 从日记中提取核心主题，并生成复古科学插图风格的提示词
    const getScientificIllustrationPrompt = (diaryText) => {
      const text = diaryText.toLowerCase();
      // 定义可能的动植物关键词
      const keywords = [
        'orchid', 'heliconia', 'bromeliad', 'fern', 'moss', 'vine', 'flower', 'leaf', 'tree', 'fungi', 'mushroom',
        'hummingbird', 'bird', 'butterfly', 'bee', 'insect', 'frog', 'lizard', 'monkey', 'sloth', 'toucan', 'parrot', 'spider', 'ant', 'beetle', 'moth', 'dragonfly', 'cricket', 'grasshopper', 'caterpillar', 'snake', 'gecko'
      ];

      let mainSubject = 'a fascinating jungle species'; // 默认主题
      let subjectLabel = 'Flora & Fauna'; // 默认标签

      // 查找第一个匹配的关键词作为主要主题
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          mainSubject = keyword;
          subjectLabel = keyword.charAt(0).toUpperCase() + keyword.slice(1);
          break;
        }
      }

      // 套用复古科学插图模板
      const prompt = `A vintage scientific illustration of ${mainSubject}, in a sepia tone, with detailed linework and shading, labeled "${subjectLabel}" and "Costa Rica", resembling an antique geology textbook plate, elegant minimalistic layout, 19th-century lithograph engraving style.`;

      return prompt;
    };

    const prompt = getScientificIllustrationPrompt(text);

    console.log('优化后的提示词:', prompt);

    const data = {
      model: 'gpt-4o-image-generation',
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt
            }
          ]
        }
      ],
      temperature: 2.0,
      stream: false
    };

    console.log('发送图像生成请求，优化提示词长度:', prompt.length);

    

    const response = await fetch(API_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => '未知错误');
      console.error('图像生成API请求失败:', response.status, errorText);
      throw new Error(`图像生成失败: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('图像生成API响应成功');

    // 从302.ai的markdown格式响应中提取图像URL
    if (result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {
      const content = result.choices[0].message.content;
      console.log('响应内容长度:', content.length);

      // 匹配markdown格式的图片链接 ![...](url)
      const markdownMatches = content.match(/!\[.*?\]\((https:\/\/file\.302ai\.cn\/gpt\/imgs\/[^)]+)\)/g);

      if (markdownMatches && markdownMatches.length > 0) {
        // 提取第一个图像URL
        const urlMatch = markdownMatches[0].match(/\((https:\/\/file\.302ai\.cn\/gpt\/imgs\/[^)]+)\)/);
        if (urlMatch) {
          let imageUrl = urlMatch[1];

          // 清理URL，只保留到.png为止
          const pngIndex = imageUrl.indexOf('.png');
          if (pngIndex !== -1) {
            imageUrl = imageUrl.substring(0, pngIndex + 4);
          }

          console.log('成功提取图像URL:', imageUrl);
          return imageUrl;
        }
      }

      // 如果markdown匹配失败，尝试直接匹配URL
      const directMatch = content.match(/https:\/\/file\.302ai\.cn\/gpt\/imgs\/[^\s)&]+\.png/);
      if (directMatch) {
        console.log('通过直接匹配找到图像URL:', directMatch[0]);
        return directMatch[0];
      }
    }

    console.error('无法从API响应中提取图像URL');
    throw new Error('API响应中未找到图像URL');

  } catch (error) {
    console.error('图像生成失败:', error);
    throw error;
  }
};

/**
 * 保存生成的图像到本地存储
 * @param {string} diaryId - 日记ID
 * @param {string} imageUrl - 图像URL
 */
export const saveGeneratedImage = (diaryId, imageUrl) => {
  try {
    const savedImages = JSON.parse(localStorage.getItem('generated_images') || '{}');
    savedImages[diaryId] = {
      url: imageUrl,
      timestamp: Date.now()
    };
    localStorage.setItem('generated_images', JSON.stringify(savedImages));
    console.log('图像URL已保存到localStorage:', diaryId);
  } catch (error) {
    console.error('保存生成图像失败:', error);
  }
};

/**
 * 获取已保存的图像
 * @param {string} diaryId - 日记ID
 * @returns {string|null} - 图像URL或null
 */
export const getSavedImage = (diaryId) => {
  try {
    const savedImages = JSON.parse(localStorage.getItem('generated_images') || '{}');
    const savedImage = savedImages[diaryId]?.url || null;
    if (savedImage) {
      console.log('从localStorage加载图像URL:', diaryId, savedImage);
    }
    return savedImage;
  } catch (error) {
    console.error('获取保存图像失败:', error);
    return null;
  }
};

/**
 * 删除保存的图像
 * @param {string} diaryId - 日记ID
 */
export const deleteSavedImage = (diaryId) => {
  try {
    const savedImages = JSON.parse(localStorage.getItem('generated_images') || '{}');
    delete savedImages[diaryId];
    localStorage.setItem('generated_images', JSON.stringify(savedImages));
    console.log('已删除保存的图像:', diaryId);
  } catch (error) {
    console.error('删除保存图像失败:', error);
  }
};