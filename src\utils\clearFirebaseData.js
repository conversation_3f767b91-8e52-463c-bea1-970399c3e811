/**
 * 清理Firebase数据工具
 * 用于清理Firebase中使用旧ID格式的数据
 */

import { auth } from '../config/firebaseConfig';

/**
 * 清理Firebase中的重复数据
 */
export const clearFirebaseDuplicateData = async () => {
  console.log('🧹 开始清理Firebase中的重复数据...');
  
  const user = auth.currentUser;
  if (!user) {
    console.log('❌ 没有登录用户，无法清理Firebase数据');
    return false;
  }
  
  const userId = user.uid;
  console.log('👤 当前用户ID:', userId);
  
  try {
    // 导入Firebase服务
    const { 
      chatHistoryService, 
      aiAnalysisService, 
      dictionarySearchService,
      writingHistoryService,
      diaryHistoryService 
    } = await import('../services/history/firebaseHistoryService');
    
    let totalRemoved = 0;
    
    // 1. 清理聊天历史中的重复数据
    console.log('🔍 检查聊天历史...');
    const chatHistory = await chatHistoryService.getChatHistory(userId, 100);
    const duplicateChatSessions = chatHistory.filter(session => 
      session.id && /^\d{13}$/.test(session.id.toString())
    );
    
    console.log(`📊 找到 ${duplicateChatSessions.length} 个使用旧ID格式的聊天会话`);
    
    for (const session of duplicateChatSessions) {
      try {
        await chatHistoryService.deleteChatSession(userId, session.id);
        console.log('🗑️ 删除重复聊天会话:', session.id);
        totalRemoved++;
      } catch (error) {
        console.error('❌ 删除聊天会话失败:', session.id, error);
      }
    }
    
    // 2. 清理AI分析历史中的重复数据
    console.log('🔍 检查AI分析历史...');
    const analysisHistory = await aiAnalysisService.getAnalysisHistory(userId, 100);
    const duplicateAnalysis = analysisHistory.filter(analysis => 
      analysis.id && /^\d{13}$/.test(analysis.id.toString())
    );
    
    console.log(`📊 找到 ${duplicateAnalysis.length} 个使用旧ID格式的AI分析记录`);
    
    for (const analysis of duplicateAnalysis) {
      try {
        await aiAnalysisService.deleteAnalysis(userId, analysis.id);
        console.log('🗑️ 删除重复AI分析记录:', analysis.id);
        totalRemoved++;
      } catch (error) {
        console.error('❌ 删除AI分析记录失败:', analysis.id, error);
      }
    }
    
    // 3. 清理字典搜索历史中的重复数据
    console.log('🔍 检查字典搜索历史...');
    const dictionaryHistory = await dictionarySearchService.getSearchHistory(userId, 100);
    const duplicateDictionary = dictionaryHistory.filter(search => 
      search.id && /^\d{13}$/.test(search.id.toString())
    );
    
    console.log(`📊 找到 ${duplicateDictionary.length} 个使用旧ID格式的字典搜索记录`);
    
    for (const search of duplicateDictionary) {
      try {
        await dictionarySearchService.deleteSearch(userId, search.id);
        console.log('🗑️ 删除重复字典搜索记录:', search.id);
        totalRemoved++;
      } catch (error) {
        console.error('❌ 删除字典搜索记录失败:', search.id, error);
      }
    }
    
    // 4. 清理写作历史中的重复数据
    console.log('🔍 检查写作历史...');
    const writingHistory = await writingHistoryService.getWritingHistory(userId, 100);
    const duplicateWriting = writingHistory.filter(writing => 
      writing.id && /^\d{13}$/.test(writing.id.toString())
    );
    
    console.log(`📊 找到 ${duplicateWriting.length} 个使用旧ID格式的写作记录`);
    
    for (const writing of duplicateWriting) {
      try {
        await writingHistoryService.deleteWriting(userId, writing.id);
        console.log('🗑️ 删除重复写作记录:', writing.id);
        totalRemoved++;
      } catch (error) {
        console.error('❌ 删除写作记录失败:', writing.id, error);
      }
    }
    
    // 5. 清理日记历史中的重复数据
    console.log('🔍 检查日记历史...');
    const diaryHistory = await diaryHistoryService.getDiaryHistory(userId, 100);
    const duplicateDiary = diaryHistory.filter(diary => 
      diary.id && /^\d{13}$/.test(diary.id.toString())
    );
    
    console.log(`📊 找到 ${duplicateDiary.length} 个使用旧ID格式的日记记录`);
    
    for (const diary of duplicateDiary) {
      try {
        await diaryHistoryService.deleteDiary(userId, diary.id);
        console.log('🗑️ 删除重复日记记录:', diary.id);
        totalRemoved++;
      } catch (error) {
        console.error('❌ 删除日记记录失败:', diary.id, error);
      }
    }
    
    console.log(`✅ Firebase数据清理完成，共删除 ${totalRemoved} 条重复记录`);
    return totalRemoved;
    
  } catch (error) {
    console.error('❌ 清理Firebase数据失败:', error);
    return false;
  }
};

/**
 * 检查Firebase中的重复数据
 */
export const checkFirebaseDuplicateData = async () => {
  console.log('🔍 检查Firebase中的重复数据...');
  
  const user = auth.currentUser;
  if (!user) {
    console.log('❌ 没有登录用户，无法检查Firebase数据');
    return false;
  }
  
  const userId = user.uid;
  console.log('👤 当前用户ID:', userId);
  
  try {
    // 导入Firebase服务
    const { 
      chatHistoryService, 
      aiAnalysisService, 
      dictionarySearchService,
      writingHistoryService,
      diaryHistoryService 
    } = await import('../services/history/firebaseHistoryService');
    
    const results = {};
    
    // 检查各种历史记录
    const [chatHistory, analysisHistory, dictionaryHistory, writingHistory, diaryHistory] = await Promise.all([
      chatHistoryService.getChatHistory(userId, 100),
      aiAnalysisService.getAnalysisHistory(userId, 100),
      dictionarySearchService.getSearchHistory(userId, 100),
      writingHistoryService.getWritingHistory(userId, 100),
      diaryHistoryService.getDiaryHistory(userId, 100)
    ]);
    
    // 分析重复数据
    results.chat = {
      total: chatHistory.length,
      duplicates: chatHistory.filter(session => session.id && /^\d{13}$/.test(session.id.toString())).length,
      sampleIds: chatHistory.slice(0, 3).map(s => s.id)
    };
    
    results.analysis = {
      total: analysisHistory.length,
      duplicates: analysisHistory.filter(analysis => analysis.id && /^\d{13}$/.test(analysis.id.toString())).length,
      sampleIds: analysisHistory.slice(0, 3).map(s => s.id)
    };
    
    results.dictionary = {
      total: dictionaryHistory.length,
      duplicates: dictionaryHistory.filter(search => search.id && /^\d{13}$/.test(search.id.toString())).length,
      sampleIds: dictionaryHistory.slice(0, 3).map(s => s.id)
    };
    
    results.writing = {
      total: writingHistory.length,
      duplicates: writingHistory.filter(writing => writing.id && /^\d{13}$/.test(writing.id.toString())).length,
      sampleIds: writingHistory.slice(0, 3).map(s => s.id)
    };
    
    results.diary = {
      total: diaryHistory.length,
      duplicates: diaryHistory.filter(diary => diary.id && /^\d{13}$/.test(diary.id.toString())).length,
      sampleIds: diaryHistory.slice(0, 3).map(s => s.id)
    };
    
    console.log('📊 Firebase重复数据检查结果:', results);
    return results;
    
  } catch (error) {
    console.error('❌ 检查Firebase数据失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.firebaseUtils = {
    clearFirebaseDuplicateData,
    checkFirebaseDuplicateData
  };
  
  console.log('🔧 Firebase数据清理工具已加载到 window.firebaseUtils');
  console.log('💡 使用方法:');
  console.log('  - await window.firebaseUtils.checkFirebaseDuplicateData() // 检查重复数据');
  console.log('  - await window.firebaseUtils.clearFirebaseDuplicateData() // 清理重复数据');
}
