# Netlify本地开发环境启动脚本
Write-Host "启动Netlify本地开发环境..." -ForegroundColor Green
Write-Host ""

# 检查是否安装了netlify-cli
try {
    $version = netlify --version 2>$null
    Write-Host "Netlify CLI 已安装: $version" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Netlify CLI，请先运行: npm install -g netlify-cli" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""

# 启动Netlify开发服务器
Write-Host "启动Netlify开发服务器..." -ForegroundColor Yellow
Write-Host "服务器将在 http://localhost:8888 启动" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

try {
    netlify dev --offline
} catch {
    Write-Host "启动失败，请检查网络连接和配置" -ForegroundColor Red
    Read-Host "按回车键退出"
}
