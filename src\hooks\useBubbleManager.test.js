import { renderHook, act } from '@testing-library/react';
import { useBubbleManager } from './useBubbleManager';

import { vi } from 'vitest';

describe('useBubbleManager', () => {
  beforeEach(() => {
    vi.clearAllTimers();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it('should initialize with null timeout and active suggestion', () => {
    const { result } = renderHook(() => useBubbleManager());
    
    expect(result.current.getActiveSuggestion()).toBeNull();
  });

  it('should set and get active suggestion', () => {
    const { result } = renderHook(() => useBubbleManager());
    
    act(() => {
      result.current.setActiveSuggestion('test-id');
    });
    
    expect(result.current.getActiveSuggestion()).toBe('test-id');
  });

  it('should clear active suggestion', () => {
    const { result } = renderHook(() => useBubbleManager());
    
    act(() => {
      result.current.setActiveSuggestion('test-id');
      result.current.clearActiveSuggestion();
    });
    
    expect(result.current.getActiveSuggestion()).toBeNull();
  });

  it('should set timeout and clear it', () => {
    const { result } = renderHook(() => useBubbleManager());
    const callback = vi.fn();
    
    act(() => {
      result.current.setTimeout(callback, 1000);
    });
    
    // 时间还没到，回调不应该被调用
    act(() => {
      vi.advanceTimersByTime(500);
    });
    expect(callback).not.toHaveBeenCalled();
    
    // 清除超时
    act(() => {
      result.current.clearTimeout();
    });
    
    // 即使时间到了，回调也不应该被调用
    act(() => {
      vi.advanceTimersByTime(1000);
    });
    expect(callback).not.toHaveBeenCalled();
  });

  it('should call callback after timeout', () => {
    const { result } = renderHook(() => useBubbleManager());
    const callback = vi.fn();
    
    act(() => {
      result.current.setTimeout(callback, 1000);
    });
    
    act(() => {
      vi.advanceTimersByTime(1000);
    });
    
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should clear previous timeout when setting new one', () => {
    const { result } = renderHook(() => useBubbleManager());
    const callback1 = vi.fn();
    const callback2 = vi.fn();
    
    act(() => {
      result.current.setTimeout(callback1, 1000);
      result.current.setTimeout(callback2, 500);
    });
    
    act(() => {
      vi.advanceTimersByTime(1000);
    });
    
    expect(callback1).not.toHaveBeenCalled();
    expect(callback2).toHaveBeenCalledTimes(1);
  });
});
