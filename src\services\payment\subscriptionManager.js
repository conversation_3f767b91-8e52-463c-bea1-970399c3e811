// 订阅状态管理服务
// 管理用户的订阅状态、权限检查、使用量统计等

import { doc, getDoc, setDoc, updateDoc, onSnapshot } from 'firebase/firestore';
import { db } from '../../config/firebaseConfig.js';
import { getUserSubscriptionPlan, checkFeatureAccess, getFeatureLimit } from '../../config/creemConfig.js';
import { CreemSubscriptionService } from './creemService.js';

// 用户订阅数据结构
const DEFAULT_SUBSCRIPTION_DATA = {
  plan: 'free',
  status: 'inactive',
  subscriptionId: null,
  customerId: null,
  currentPeriodStart: null,
  currentPeriodEnd: null,
  canceledAt: null,
  usage: {
    aiAnalysisCount: 0,
    dictionaryLookupCount: 0,
    lastResetDate: new Date().toISOString().split('T')[0] // YYYY-MM-DD
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

export class SubscriptionManager {
  constructor(userId) {
    this.userId = userId;
    this.subscriptionRef = doc(db, 'userSubscriptions', userId);
    this.unsubscribe = null;
  }
  
  // 初始化用户订阅数据
  async initializeSubscription() {
    try {
      const docSnap = await getDoc(this.subscriptionRef);
      
      if (!docSnap.exists()) {
        // 创建默认订阅数据
        await setDoc(this.subscriptionRef, DEFAULT_SUBSCRIPTION_DATA);
        console.log('✅ 初始化用户订阅数据成功');
        return DEFAULT_SUBSCRIPTION_DATA;
      }
      
      return docSnap.data();
    } catch (error) {
      console.error('❌ 初始化用户订阅数据失败:', error);
      throw error;
    }
  }
  
  // 获取用户订阅状态
  async getSubscriptionStatus() {
    try {
      const docSnap = await getDoc(this.subscriptionRef);
      
      if (!docSnap.exists()) {
        return await this.initializeSubscription();
      }
      
      const data = docSnap.data();
      
      // 检查是否需要重置月度使用量
      await this.checkAndResetMonthlyUsage(data);
      
      return data;
    } catch (error) {
      console.error('❌ 获取用户订阅状态失败:', error);
      return DEFAULT_SUBSCRIPTION_DATA;
    }
  }
  
  // 更新订阅状态（从Creem webhook或手动同步）
  async updateSubscriptionStatus(subscriptionData) {
    try {
      const plan = getUserSubscriptionPlan(subscriptionData);
      
      const updateData = {
        plan,
        status: subscriptionData.status || 'inactive',
        subscriptionId: subscriptionData.id,
        customerId: subscriptionData.customer?.id || subscriptionData.customer,
        currentPeriodStart: subscriptionData.current_period_start_date,
        currentPeriodEnd: subscriptionData.current_period_end_date,
        canceledAt: subscriptionData.canceled_at,
        updatedAt: new Date().toISOString()
      };
      
      await updateDoc(this.subscriptionRef, updateData);
      console.log('✅ 更新用户订阅状态成功:', updateData);
      
      return updateData;
    } catch (error) {
      console.error('❌ 更新用户订阅状态失败:', error);
      throw error;
    }
  }
  
  // 同步Creem订阅状态
  async syncWithCreem(subscriptionId) {
    try {
      const subscription = await CreemSubscriptionService.getSubscription(subscriptionId);
      return await this.updateSubscriptionStatus(subscription);
    } catch (error) {
      console.error('❌ 同步Creem订阅状态失败:', error);
      throw error;
    }
  }
  
  // 检查功能访问权限
  async checkFeatureAccess(feature) {
    try {
      const subscriptionData = await this.getSubscriptionStatus();
      return checkFeatureAccess(subscriptionData.plan, feature);
    } catch (error) {
      console.error('❌ 检查功能访问权限失败:', error);
      return false; // 默认拒绝访问
    }
  }
  
  // 检查使用量限制
  async checkUsageLimit(feature) {
    try {
      const subscriptionData = await this.getSubscriptionStatus();
      const limit = getFeatureLimit(subscriptionData.plan, feature);
      
      if (limit === -1) {
        return { allowed: true, remaining: -1, limit: -1 }; // 无限制
      }
      
      const currentUsage = subscriptionData.usage[`${feature}Count`] || 0;
      const remaining = Math.max(0, limit - currentUsage);
      
      return {
        allowed: remaining > 0,
        remaining,
        limit,
        current: currentUsage
      };
    } catch (error) {
      console.error('❌ 检查使用量限制失败:', error);
      return { allowed: false, remaining: 0, limit: 0 };
    }
  }
  
  // 增加功能使用量
  async incrementUsage(feature) {
    try {
      const usageField = `usage.${feature}Count`;
      const currentData = await this.getSubscriptionStatus();
      const currentUsage = currentData.usage[`${feature}Count`] || 0;
      
      await updateDoc(this.subscriptionRef, {
        [usageField]: currentUsage + 1,
        'usage.lastUpdated': new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      console.log(`✅ 增加${feature}使用量: ${currentUsage + 1}`);
      return currentUsage + 1;
    } catch (error) {
      console.error('❌ 增加功能使用量失败:', error);
      throw error;
    }
  }
  
  // 检查并重置月度使用量
  async checkAndResetMonthlyUsage(subscriptionData) {
    try {
      const today = new Date().toISOString().split('T')[0];
      const lastResetDate = subscriptionData.usage?.lastResetDate;
      
      if (!lastResetDate) {
        // 首次设置重置日期
        await updateDoc(this.subscriptionRef, {
          'usage.lastResetDate': today
        });
        return;
      }
      
      const lastReset = new Date(lastResetDate);
      const now = new Date();
      
      // 检查是否需要重置（每月1号重置）
      if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
        await updateDoc(this.subscriptionRef, {
          'usage.aiAnalysisCount': 0,
          'usage.dictionaryLookupCount': 0,
          'usage.lastResetDate': today,
          updatedAt: new Date().toISOString()
        });
        
        console.log('✅ 重置月度使用量');
      }
    } catch (error) {
      console.error('❌ 重置月度使用量失败:', error);
    }
  }
  
  // 监听订阅状态变化
  subscribeToChanges(callback) {
    this.unsubscribe = onSnapshot(this.subscriptionRef, (doc) => {
      if (doc.exists()) {
        callback(doc.data());
      } else {
        callback(DEFAULT_SUBSCRIPTION_DATA);
      }
    }, (error) => {
      console.error('❌ 监听订阅状态变化失败:', error);
      callback(DEFAULT_SUBSCRIPTION_DATA);
    });
    
    return this.unsubscribe;
  }
  
  // 停止监听
  unsubscribeFromChanges() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
  }
  
  // 获取订阅统计信息
  async getSubscriptionStats() {
    try {
      const subscriptionData = await this.getSubscriptionStatus();
      const plan = subscriptionData.plan;
      
      const aiAnalysisLimit = getFeatureLimit(plan, 'aiAnalysis');
      const dictionaryLimit = getFeatureLimit(plan, 'dictionaryLookup');
      
      return {
        plan,
        status: subscriptionData.status,
        aiAnalysis: {
          used: subscriptionData.usage?.aiAnalysisCount || 0,
          limit: aiAnalysisLimit,
          unlimited: aiAnalysisLimit === -1
        },
        dictionaryLookup: {
          used: subscriptionData.usage?.dictionaryLookupCount || 0,
          limit: dictionaryLimit,
          unlimited: dictionaryLimit === -1
        },
        currentPeriodEnd: subscriptionData.currentPeriodEnd,
        canceledAt: subscriptionData.canceledAt
      };
    } catch (error) {
      console.error('❌ 获取订阅统计信息失败:', error);
      return null;
    }
  }
}

// 创建全局订阅管理器实例的工厂函数
export const createSubscriptionManager = (userId) => {
  return new SubscriptionManager(userId);
};

export default SubscriptionManager;
