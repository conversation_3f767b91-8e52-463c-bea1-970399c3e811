"use strict";function _createForOfIteratorHelper(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function eudic_onCambridgeDictClick(e){var t=e.target;t&&t.classList&&(t.classList.contains("js-accord")&&(t.classList.toggle("open"),e.stopPropagation(),e.preventDefault()),t.classList.contains("daccord_h")&&(t.parentElement.classList.toggle("open"),e.stopPropagation(),e.preventDefault()))}function eudic_onlineDictPlugin_getParameterByName(e,t){e=e.replace(/[\[\]]/g,"\\$&");var r=new RegExp("[?&]"+e+"(=([^&#]*)|&|#|$)").exec(t);return r?r[2]?decodeURIComponent(r[2].replace(/\+/g," ")):"":null}function eudic_onlineDictPlugin_onloadFinish(){var e=eudic_onlineDictPlugin_getParameterByName("id",document.currentScript.src),t=document.getElementById("eudic-onlinedict-section-"+e);if(t){var r=t.querySelectorAll("[eudic-onlinedict-custom-onclick]");if(r&&r.length>0){var n,o=_createForOfIteratorHelper(r);try{for(o.s();!(n=o.n()).done;){var i=n.value,a=i.getAttribute("eudic-onlinedict-custom-onclick"),c=window[a];"function"==typeof c&&(i.ontouchend=c)}}catch(e){o.e(e)}finally{o.f()}}}}eudic_onlineDictPlugin_onloadFinish();