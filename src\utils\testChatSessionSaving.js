/**
 * 测试聊天会话保存逻辑
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试聊天会话保存逻辑
export const testChatSessionSaving = async () => {
  console.log('🧪 开始测试聊天会话保存逻辑...');
  
  // 模拟用户登录
  const testUserId = 'test_user_' + Date.now();
  simpleStorageService.init(testUserId);
  
  console.log('👤 测试用户ID:', testUserId);
  
  // 测试1: 模拟只有AI消息的会话（不应该保存）
  console.log('🤖 测试1: 模拟只有AI消息的会话...');
  const aiOnlyMessages = [
    { id: 'ai_1', type: 'ai', content: 'Hello! How can I help you today?', timestamp: new Date() }
  ];
  
  const hasUserMessages1 = aiOnlyMessages.some(msg => msg.type === 'user');
  console.log('  - 有用户消息:', hasUserMessages1);
  console.log('  - 应该保存:', hasUserMessages1 ? '是' : '否');
  
  if (hasUserMessages1) {
    simpleStorageService.saveChatSession(aiOnlyMessages);
    console.log('  - 实际保存了（不应该）');
  } else {
    console.log('  - 正确跳过保存');
  }
  
  // 测试2: 模拟有用户消息的会话（应该保存）
  console.log('\n💬 测试2: 模拟有用户消息的会话...');
  const userMessages = [
    { id: 'ai_1', type: 'ai', content: 'Hello! How can I help you today?', timestamp: new Date() },
    { id: 'user_1', type: 'user', content: 'Can you help me with English?', timestamp: new Date() },
    { id: 'ai_2', type: 'ai', content: 'Of course! I\'d be happy to help.', timestamp: new Date() }
  ];
  
  const hasUserMessages2 = userMessages.some(msg => msg.type === 'user');
  console.log('  - 有用户消息:', hasUserMessages2);
  console.log('  - 应该保存:', hasUserMessages2 ? '是' : '否');
  
  if (hasUserMessages2) {
    const savedSession = simpleStorageService.saveChatSession(userMessages);
    console.log('  - 实际保存了，会话ID:', savedSession.id);
  } else {
    console.log('  - 错误跳过保存');
  }
  
  // 测试3: 模拟多次保存同一会话（检查是否重复）
  console.log('\n🔄 测试3: 模拟多次保存同一会话...');
  const initialCount = simpleStorageService.getChatHistory().length;
  console.log('  - 初始会话数量:', initialCount);
  
  // 第一次保存
  const session1 = simpleStorageService.saveChatSession(userMessages, 'Test Session 1');
  const count1 = simpleStorageService.getChatHistory().length;
  console.log('  - 第一次保存后数量:', count1);
  
  // 第二次保存相同内容
  const session2 = simpleStorageService.saveChatSession(userMessages, 'Test Session 2');
  const count2 = simpleStorageService.getChatHistory().length;
  console.log('  - 第二次保存后数量:', count2);
  
  // 第三次保存相同内容
  const session3 = simpleStorageService.saveChatSession(userMessages, 'Test Session 3');
  const count3 = simpleStorageService.getChatHistory().length;
  console.log('  - 第三次保存后数量:', count3);
  
  console.log('  - 是否产生重复:', count3 > count1 + 2 ? '是' : '否');
  
  // 测试4: 检查保存的会话内容
  console.log('\n📋 测试4: 检查保存的会话内容...');
  const allSessions = simpleStorageService.getChatHistory();
  console.log('  - 总会话数量:', allSessions.length);
  
  allSessions.forEach((session, index) => {
    console.log(`\n  会话 ${index + 1}:`);
    console.log('    - ID:', session.id);
    console.log('    - 标题:', session.title);
    console.log('    - 消息数量:', session.messageCount);
    console.log('    - 用户消息数量:', session.messages ? session.messages.filter(msg => msg.type === 'user').length : 0);
    console.log('    - AI消息数量:', session.messages ? session.messages.filter(msg => msg.type === 'ai').length : 0);
  });
  
  // 测试5: 模拟useChat.js的保存逻辑
  console.log('\n🔧 测试5: 模拟useChat.js的保存逻辑...');
  
  // 模拟handleSendMessage的保存逻辑
  const simulateHandleSendMessage = (messages) => {
    const hasUserMessages = messages.some(msg => msg.type === 'user');
    if (hasUserMessages) {
      const saved = simpleStorageService.saveChatSession(messages);
      console.log('    - 保存了会话，ID:', saved.id);
      return saved;
    } else {
      console.log('    - 跳过保存（无用户消息）');
      return null;
    }
  };
  
  // 模拟handleNewConversation的保存逻辑
  const simulateHandleNewConversation = (messages) => {
    if (messages.length > 1) {
      const hasUserMessages = messages.some(msg => msg.type === 'user');
      if (hasUserMessages) {
        const saved = simpleStorageService.saveChatSession(messages);
        console.log('    - 保存了会话，ID:', saved.id);
        return saved;
      } else {
        console.log('    - 跳过保存（无用户消息）');
        return null;
      }
    } else {
      console.log('    - 跳过保存（消息太少）');
      return null;
    }
  };
  
  // 测试不同的消息组合
  const testCases = [
    { name: '只有AI消息', messages: [{ id: 'ai_1', type: 'ai', content: 'Hello!', timestamp: new Date() }] },
    { name: '有用户消息', messages: [
      { id: 'ai_1', type: 'ai', content: 'Hello!', timestamp: new Date() },
      { id: 'user_1', type: 'user', content: 'Hi!', timestamp: new Date() }
    ]},
    { name: '完整对话', messages: [
      { id: 'ai_1', type: 'ai', content: 'Hello!', timestamp: new Date() },
      { id: 'user_1', type: 'user', content: 'Hi!', timestamp: new Date() },
      { id: 'ai_2', type: 'ai', content: 'How can I help?', timestamp: new Date() }
    ]}
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n  测试用例 ${index + 1}: ${testCase.name}`);
    const result = simulateHandleSendMessage(testCase.messages);
    if (result) {
      console.log('    - 结果: 保存成功');
    } else {
      console.log('    - 结果: 跳过保存');
    }
  });
  
  // 最终统计
  const finalStats = simpleStorageService.getStats();
  console.log('\n📊 最终统计:', finalStats);
  
  console.log('🎉 聊天会话保存逻辑测试完成！');
  
  // 清理测试数据
  simpleStorageService.cleanup();
  console.log('🧹 测试数据已清理');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testChatSessionSaving = testChatSessionSaving;
}
