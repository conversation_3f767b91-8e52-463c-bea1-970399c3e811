import { vi, describe, it, expect, beforeEach } from 'vitest';
import { auth } from '../../config/firebaseConfig';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
} from 'firebase/auth';
import { signUp, signIn, logout, onAuthChange } from './authService';

// Mock the entire firebase/auth module
vi.mock('firebase/auth', () => ({
  createUserWithEmailAndPassword: vi.fn(),
  signInWithEmailAndPassword: vi.fn(),
  signOut: vi.fn(),
  onAuthStateChanged: vi.fn(),
  getAuth: vi.fn(),
}));

// Mock the auth object from firebaseConfig
vi.mock('../../config/firebaseConfig', () => ({
  auth: {},
}));

describe('Auth Service', () => {
  const email = '<EMAIL>';
  const password = 'password123';
  const mockUser = { uid: '123', email };
  const mockUserCredential = { user: mockUser };
  const mockError = new Error('Auth failed');

  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
  });

  // Test suite for signUp
  describe('signUp', () => {
    it('should create a new user successfully', async () => {
      createUserWithEmailAndPassword.mockResolvedValue(mockUserCredential);

      const result = await signUp(email, password);

      expect(createUserWithEmailAndPassword).toHaveBeenCalledWith(auth, email, password);
      expect(result).toEqual(mockUserCredential);
    });

    it('should throw an error if sign up fails', async () => {
      createUserWithEmailAndPassword.mockRejectedValue(mockError);

      await expect(signUp(email, password)).rejects.toThrow('Auth failed');
    });
  });

  // Test suite for signIn
  describe('signIn', () => {
    it('should sign in an existing user successfully', async () => {
      signInWithEmailAndPassword.mockResolvedValue(mockUserCredential);

      const result = await signIn(email, password);

      expect(signInWithEmailAndPassword).toHaveBeenCalledWith(auth, email, password);
      expect(result).toEqual(mockUserCredential);
    });

    it('should throw an error if sign in fails', async () => {
      signInWithEmailAndPassword.mockRejectedValue(mockError);

      await expect(signIn(email, password)).rejects.toThrow('Auth failed');
    });
  });

  // Test suite for logout
  describe('logout', () => {
    it('should sign out the current user successfully', async () => {
      signOut.mockResolvedValue(undefined);

      await logout();

      expect(signOut).toHaveBeenCalledWith(auth);
    });

    it('should throw an error if sign out fails', async () => {
      signOut.mockRejectedValue(mockError);

      await expect(logout()).rejects.toThrow('Auth failed');
    });
  });

  // Test suite for onAuthChange
  describe('onAuthChange', () => {
    it('should call the callback with the user object when auth state changes', () => {
      const callback = vi.fn();
      // Mock the behavior of onAuthStateChanged
      onAuthStateChanged.mockImplementation((auth, cb) => {
        // Immediately invoke the callback with a mock user
        cb(mockUser);
        // Return a mock unsubscribe function
        return vi.fn();
      });

      onAuthChange(callback);

      expect(onAuthStateChanged).toHaveBeenCalledWith(auth, callback);
      expect(callback).toHaveBeenCalledWith(mockUser);
    });

    it('should call the callback with null when the user is signed out', () => {
      const callback = vi.fn();
      onAuthStateChanged.mockImplementation((auth, cb) => {
        cb(null);
        return vi.fn();
      });

      onAuthChange(callback);

      expect(callback).toHaveBeenCalledWith(null);
    });

    it('should return an unsubscribe function', () => {
      const unsubscribeMock = vi.fn();
      onAuthStateChanged.mockReturnValue(unsubscribeMock);

      const unsubscribe = onAuthChange(vi.fn());

      expect(unsubscribe).toBe(unsubscribeMock);
      unsubscribe();
      expect(unsubscribeMock).toHaveBeenCalled();
    });
  });
});
