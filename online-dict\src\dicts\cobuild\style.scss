body.black .dictCOBUILD-ColEntry .dc .rend-b{
  color:#39f;
}
body.night .dictCOBUILD-ColEntry .dc .rend-b{
  color:#1e5994;
}

.dictCOBUILD-Title {
  font-size: 1.5em;
  font-weight: bold;
}

.dictCOBUILD-Pron {
  display: flex;
  margin-bottom: 5px;
}

.dictCOBUILD-PronItem {
  margin-right: 1em;

  .icon-Speaker {
    margin-left: 2px;
  }
}

.dictCOBUILD-Rate {
  display: flex;
  margin-bottom: 5px;
}

.dictCOBUILD-Level {
  margin-left: 10px;
  color: #aaa;
}

.dictCOBUILD-Defs {
  margin: 0;
  padding-left: 15px;
}

.dictCOBUILD-Def {
  p {
    margin: 0;
  }

  b {
    color: var(--color-theme);
  }

  .prep-en {
    display: block;
    margin-bottom: 5px;
  }

  .text-sentence {
    padding-left: 10px;
    padding-bottom: 5px;
    color: var(--color-font-grey);
    border-left: var(--color-font-grey) solid 1px;

    &:last-child {
      padding-bottom: 0;
      margin-bottom: 10px;
    }
  }
}

.dictCOBUILD-ColEntry {
  * {
    word-wrap: break-word;
    margin: 0;
    padding: 0;
    border: 0;
    box-sizing: border-box;
  }

  a {
    color: var(--color-font) !important;
  }

  .padLeft {
    padding-left: 20px;
  }

  .textCenter {
    text-align: center;
  }

  ol,
  ul {
    list-style-type: none;
  }

  q {
    quotes: none;
  }

  input,
  button,
  select {
    font-size: inherit;
    color: inherit;
    padding: 0.5em;
    border: solid 1px #c3c3c3;
  }

  label {
    display: block;
  }

  label.inline {
    display: inline;
  }

  button {
    cursor: pointer;
  }

  h1 {
    font-size: 2em;
    line-height: 1.4em;
  }

  h2 {
    font-size: 1.2em;
  }

  .center {
    display: table;
    margin: 0 auto;
  }

  .clear:before,
  .clear:after {
    content: ' ';
    display: block;
    height: 0;
    overflow: hidden;
  }

  .clear:after {
    clear: both;
  }

  .floatRight {
    float: right;
  }

  .discrete {
    font-size: 0.85em;
    color: #888;
  }

  .text-right {
    text-align: right;
  }

  .columns-block {
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
    display: table;
    width: 100%;
  }

  .white .topslot_container {
    margin-left: auto;
    margin-right: auto;
  }

  .topslot_container,
  #ad_rightslot,
  #ad_rightslot2 {
    margin-bottom: 20px;
  }

  .ac_leftslot_a {
    min-height: 600px;
  }

  .btmslot_a-container {
    width: 100%;
  }

  .content-box {
    padding: 0;
    margin-bottom: 1em;
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .content-box h2,
  .content-box .h2 {
    margin-bottom: 0.5em;
  }

  .content-box .view_more a {
    opacity: 0.7;
    text-decoration: none;
    margin-top: 1em;
    display: block;
    font-size: 0.8em;
  }

  .lsw {
    font-weight: bold;
  }

  .lsw a {
    text-decoration: none;
  }

  .lsw[data-type='trends'] a {
    margin-left: 0.5em;
  }

  .lsw[data-type='trends'] .percentVariation .icon-fiber_new {
    font-size: 2em;
  }

  .lsw[data-type='trends'] .percentVariation {
    display: inline-block;
    min-width: 3em;
    text-align: right;
  }

  .lsw i {
    font-size: 1.3em;
    vertical-align: sub;
    margin-right: 5px;
  }

  .lsw .lsw_title {
    font-size: 1.5em;
    margin-bottom: 1em;
  }

  .lsw[data-type='trends'] .lsw_title {
    margin-bottom: 0.3em;
  }

  .lsw .lsw_title i {
    font-size: 3em;
    color: #7e7b87;
    margin-right: 10px;
  }

  .lsw .view_more {
    border: solid 1px #1c4b8b;
    padding: 20px;
    display: inline-block;
    background: #e8e8e8;
    margin: 1em 20px 0;
    font-size: 1.1em;
    padding: 15px 25px;
  }

  .lsw .lsw_list {
    margin-left: 1em;
  }

  .lsw[data-type='trends'] .lsw_list {
    line-height: 1.2em;
  }

  .lsw .lsw_list li {
    margin-bottom: 5px;
  }

  .lsw .lsw_list span {
    font-size: 0.7em;
    color: grey;
  }

  .lsw i.green {
    color: #008000;
  }

  .lsw i.red {
    color: #e05555;
  }

  .lgg {
    background: 0;
    font-weight: bold;
    border: 0;
    box-shadow: none;
    font-size: 1.2em;
    padding: 0;
  }

  .lgg h2,
  .lgg .margin {
    color: var(--color-font-grey);
    text-align: center;
    padding: 21px 5px;
    margin-bottom: 5px;
  }

  .lgg a {
    text-decoration: none;
    margin-bottom: 5px;
    text-align: center;
    padding: 10px;
    border: 1px solid rgba(216, 216, 216, 0.7);
    border-radius: 5px;
    background-color: #e5ebf3;
    display: block;
  }

  .lgg a:hover {
    border: 1px solid rgba(144, 144, 144, 0.7);
    background-color: rgba(187, 183, 183, 0.37);
  }

  .def-dict {
    text-decoration: none;
    display: block;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
    background: #e5ebf3;
    color: #194885;
  }

  .def-dict-title {
    font-size: 1.6em;
    margin: 0.5em 0 1em 0;
    font-weight: bold;
  }

  .def-dict-footer {
    margin-top: 1em;
    font-weight: bold;
    color: #1c4b8b;
  }

  .def-dict-footer i {
    font-size: 1.5em;
    vertical-align: text-top;
    margin-right: 10px;
  }

  .toc {
    padding: 0 10px;
    text-align: center;
    margin-bottom: 0.5em;
  }

  .toc-group {
    display: inline-block;
    margin-right: 1em;
  }

  .related {
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .related-title {
    font-weight: bold;
    text-decoration: none;
    border-bottom: 1px dotted;
  }

  .related-definition {
    font-style: italic;
  }

  .promoBoxGrammar {
    overflow: hidden;
  }

  .promoBoxGrammar .entry_container ul {
    overflow: hidden;
  }

  .promoBoxGrammar .entry_container {
    box-shadow: none;
    background-color: white;
    overflow-y: auto;
    max-height: 400px;
  }

  .browse-block {
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .dc .cobuild-logo {
    display: none;
    width: 150px;
    height: 20px;
    // background: url(https://www.collinsdictionary.com/external/images/cobuild-logo.png?version=3.1.211);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    float: right;
    text-decoration: none;
    border: 0;
    margin-top: 5px;
  }

  .dc .cobuild-logo.partner {
    width: 200px;
    background-image: url(https://www.collinsdictionary.com/external/images/cobuild-word-partner.png?version=3.1.211);
    margin-right: 0.5em;
    margin-top: 10px;
  }

  .beta {
    background-image: url(https://www.collinsdictionary.com/external/images/beta.png?version=3.1.211);
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: 75%;
    display: inline-block;
    width: 80px;
  }

  .dc .cobuild-partner {
    float: right;
  }

  .dc .expandable-list {
    padding: 0.1em;
  }

  .dc .content-box {
    padding-top: 0;
  }

  .dc .content-box-thesaurus {
    margin-top: 20px;
  }

  .dc .content-box-header:after {
    content: ' ';
    display: block;
    clear: both;
  }

  .dc .content-box-header {
    background: #e5ebf3;
    padding: 0.5em 15px;
  }

  .cdet .dc .content-box .content-box-header,
  .dc .content .content-box-header {
    background: 0;
    padding: 0;
    margin: 0;
  }

  .dc .content-box-definition.cobuild .content-box-header,
  .dc .content-box-collocation.cobuild .content-box-header,
  .dc .content-box-learners .content-box-header {
    background: #ccedf0;
  }

  .dc .content-box-examples .content-box-header {
    background: #deefe3;
  }

  .dc .content-box-translations .content-box-header {
    background: #fff6dd;
  }

  .dc .content-box-nearby-words .content-box-header {
    background: #ffefeb;
  }

  .dc .content-box-origin .content-box-header {
    background: #eff7ff;
  }

  .dc .content-box-comments .content-box-header {
    background: #dbf7dc;
  }

  .dc .content-box-usage .content-box-header {
    background: #e9ffe3;
  }

  .dc .content-box-images .content-box-header {
    background: #f4ffd4;
  }

  .dc .content-box-videos .content-box-header {
    background: #ffebeb;
  }

  .dc .content-box-wordlists .content-box-header {
    background: #fff1e6;
  }

  .dc .content-box-quotations .content-box-header {
    background: #fff1e6;
  }

  .dc .content-box-related-terms .content-box-header {
    background: #fffeec;
  }

  .dc .content-box-header .h2_entry {
    margin-bottom: 0;
  }

  .dc .content-box-videos .entryVideo {
    width: 100%;
    max-width: 640px;
    height: 320px;
  }

  .dc .h2_entry {
    display: none;
    font-size: 1.4em;
    margin-bottom: 0.5em;
  }

  .dc .content-box-definition h2.h2_entry {
    font-size: 1.8em;
    line-height: 1;
  }

  .dc .content-box-examples h2.h2_entry {
    display: inline-block;
  }

  .dc .definitions .thes {
    margin-top: 0.8em;
  }

  .dc .example_box,
  .dc ol li,
  .dc .thesaurus_synonyms,
  .verbtable_content .headword_link,
  .dc .link-right.verbtable {
    margin-bottom: 1em;
  }

  .dc .h3_entry {
    font-size: 1.3em;
    margin: 0.5em 0;
  }

  .dc .sense_list .scbold {
    display: block;
    font-style: italic;
    font-family: 'Times New Roman', Times, serif;
    border-bottom: 1px dotted #c5c5c5;
  }

  .dc strong,
  .dc .content-box-synonyms .firstSyn,
  .dc .content-box-nearby-words .current,
  .dc .cit-type-example .orth,
  .dc .mini_h2 .orth,
  .dc .example_box .author,
  .dc .thesaurus_synonyms .synonym:first-of-type,
  .dc .content-box-translation .phr,
  .dc .blackbold {
    font-weight: bold;
  }

  .dc .blackbold {
    color: var(--color-font);
  }

  .dc .school .def {
    display: inline;
  }

  .dc .def {
    font-size: 1.15em;
    margin-bottom: 0.5em;
    line-height: 1.5em;
  }

  .dc .sensenumdef .def {
    font-size: 1.1em;
    margin: 0.5em 0;
  }

  .dc .hom ol ol {
    list-style-type: lower-alpha;
  }

  .dc #synonyms_content:first-of-type {
    border: 0;
  }

  .dc #synonyms_content {
    border-top: 1px dotted #c5c5c5;
    padding-top: 12px;
  }

  .dc .lbl.type-register,
  .dc .lbl.misc,
  .dc .colloc,
  .dc #synonyms_content .thesaurus_synonyms .lbl,
  .dc #synonyms_content .thesaurus_synonyms .misc {
    font-style: italic;
  }

  .dc .lbl.type-curric {
    font-variant: small-caps;
  }

  .school .form.type-drv .orth,
  .school .form.type-phrasalverb .orth {
    font-weight: bold;
    font-size: 1.1em;
  }

  .school .fraction {
    display: inline-block;
    position: relative;
    vertical-align: middle;
    letter-spacing: 0.001em;
    text-align: center;
    padding-left: 3px;
    padding-right: 3px;
    font-size: 0.7em;
    line-height: 1.1em;
  }

  .school .fraction > span {
    display: none;
    padding: 0 0.2em;
  }

  .school .fraction > .denominator {
    border-top: thin solid var(--color-font);
  }

  .school .fraction > .numerator,
  .school .fraction > .denominator {
    display: block;
  }

  .dc .lbl.type-syntax {
    font-size: 0.8em;
    color: var(--color-font-grey);
  }

  .dc ol {
    list-style-position: outside;
    list-style-type: decimal;
    margin-left: 2em;
  }

  .dc ol.single,
  .dc ol ol.single {
    list-style-type: none;
  }

  .dc .content-box-synonyms .h2_entry {
    display: inline-block;
  }

  .dc .content-box-synonyms .extra-link,
  .dc .content-box-collocation .explore-collocation {
    display: inline-block;
    margin-left: 1em;
  }

  .dc .thesaurus_synonyms .firstSyn {
    font-weight: bold;
    display: inline-block;
    border-width: 1px;
    border-style: solid;
    border-color: initial;
    border-image: initial;
    padding: 5px 11px 2px;
    margin: 0 5px 0 0;
  }

  .dc .thesaurus_synonyms .firstSyn > a {
    text-decoration: none;
  }

  //禁止所有外部跳转链接
  .dc .ref.type-thesaurus,
  .dc .content-box-synonyms .thesaurus-link-plus,
  .dc .link-right.verbtable,
  .dc .extra-link,
  .dc .content-box-examples .button,
  .verbtable_content .headword_link {
    background: rgba(218, 218, 218, 0.5);
    padding: 2px 10px;
    border: 0;
    text-decoration: none;
    display: none;
    font-weight: bold;
    font-size: 0.9em;
    margin-top: 2px;
  }

  .dc .h2_entry .dictname,
  .dc .h2_entry .lbl.type-misc {
    font-size: 16px;
  }

  .dc .translation .lang_EN-GB {
    margin-bottom: 10px;
  }

  .dc .translation .def {
    font-weight: bold;
    font-size: inherit;
  }

  .dc .translation .example {
    display: block;
    font-style: italic;
    margin-bottom: 1em;
  }

  .dc .translation_list {
    margin: 1em 0;
  }

  .dc .translation_list .gramGrp {
    text-transform: lowercase;
  }

  .dc .audio_play_button,
  .audio_play_button {
    color: #ec2615;
    vertical-align: middle;
    -webkit-transition: transform 0.2s, text-shadow 0.2s;
    transition: transform 0.2s, text-shadow 0.2s;
    border: 0;
  }

  .dc .h1_entry {
    font-size: 1.8em;
    line-height: 1.75em;
  }

  .dc .entry_title {
    font-size: 1.5em;
    line-height: 1.4em;
    text-align: left;
    color: #4d4e51;
    font-weight: bold;
  }

  .dc .gotodict {
    float: right;
    line-height: 3.9em;
  }

  .dc .h2_entry .homnum {
    background-color: #1c4b8b;
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    padding: 2px 5px;
    vertical-align: super;
  }

  .context-english-thesaurus .quote {
    display: block;
    margin-top: 10px;
  }

  .dc .dictionary .quote {
    color: var(--color-font-grey);
  }

  .dc .dictionary .cit.type-translation .quote {
    font-weight: bold;
  }

  .dc .dictionary .cit.type-example .quote .emph {
    color: var(--color-brand);
  }

  .context-english-thesaurus .scbold br {
    display: none;
  }

  .context-english-thesaurus .dc .sense_list .scbold {
    border-bottom: 0;
  }

  .context-english-thesaurus .scbold {
    display: block;
    font-style: italic;
    font-family: 'Times New Roman', Times, serif;
    border-bottom: 0;
    margin-top: 15px;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .dc .sup {
    vertical-align: super;
    font-size: smaller;
  }

  .dc .content-box:after,
  .dc .dictionary .content:after {
    content: '';
    clear: both;
    display: table;
  }

  .dc .cit.type-quotation .quote,
  .dc .content-box-quotations .quote,
  .dc .content-box-examples .quote,
  .dc .content-box-thesaurus .quote {
    display: block;
    margin-top: 1em;
  }

  .dc .cit.type-quotation .author,
  .dc .content-box-quotations .author,
  .dc .content-box-examples .author,
  .dc .content-box-thesaurus .author {
    font-weight: bold;
    font-style: italic;
    font-size: 0.8em;
  }

  .dc .cit.type-quotation .title,
  .dc .content-box-quotations .title,
  .dc .content-box-examples .title,
  .dc .content-box-thesaurus .title {
    display: inline;
    font-variant: small-caps;
    font-style: italic;
    font-size: 0.8em;
  }

  .dc .cit.type-quotation .year,
  .dc .content-box-quotations .year,
  .dc .content-box-examples .year,
  .dc .content-box-thesaurus .year {
    font-size: 0.8em;
    font-style: italic;
  }

  .dc .content-box-syn-of-syns div.type-syn_of_syn_head .orth,
  .dc .thesbase .key,
  .context-dataset-english-thesaurus .author,
  .dc .rend-b,
  .headwordSense {
    color: var(--color-brand);
  }

  .dc .minimalunit {
    font-weight: bold;
  }

  .dc .image {
    background: #fafafa;
    border: solid 1px #eee;
    display: inline-block;
  }

  .dc .image .imageImg {
    max-width: 100%;
    max-height: 250px;
    vertical-align: middle;
  }

  .dc .image .imageDescription {
    font-style: italic;
    font-size: 0.8em;
    padding: 0 5px;
  }

  .dc .example-info i {
    color: red;
    font-size: 21px;
    vertical-align: text-top;
    border-bottom: 0;
  }

  .dc .example-info {
    font-style: italic;
    font-size: 0.9em;
  }

  .page {
    font-size: 1em;
  }

  .page .dictname {
    font-size: 0.7em;
  }

  .page .dictionary .copyright .i {
    color: gray;
  }

  .page .copyright {
    text-align: center;
    color: gray;
    font-size: small;
    margin-top: 10px;
  }

  .page .metadata {
    display: none;
  }

  .page .infls,
  .page .description,
  .page .title,
  .page .url,
  .page .summary,
  .page .og,
  .page .infls,
  .page .description,
  .page .title,
  .page .url,
  .page .summary,
  .page .og {
    display: block;
  }

  .page .assetref {
    display: block;
  }

  .page .assettype {
    font-weight: bold;
    color: blue;
  }

  .page .dictentry,
  .page .colloList {
    margin-bottom: 20px;
  }

  .page .assets_intro,
  .page .asset_intro {
    color: green;
    display: block;
    font-weight: bold;
    font-variant: small-caps;
  }

  .page .dictionary .re .hom {
    display: inline;
  }

  .page .dictionary .re {
    display: block;
  }

  .page .cobuild .hom {
    display: block;
    margin-left: 1.5em;
    margin-bottom: 1em;
  }

  .page .cobuild .sense {
    margin-left: 0;
    margin-bottom: 0;
    margin-top: 0.25em;
  }

  .page .dictionary .sense {
    display: block;
    margin-left: 1.5em;
    margin-bottom: 0.5em;
    margin-top: 0.5em;
  }

  .page .dictionary .sense.inline {
    display: inline;
    margin-left: 0;
  }

  .page .dictionary .inline {
    display: inline;
  }

  .page .dictionary .newline {
    display: block;
  }

  .page .cobuild br {
    display: none;
  }

  .page .dictionary .subc,
  .page .dictionary .colloc {
    font-style: italic;
    font-weight: normal;
  }

  .page .dictionary .re .pos {
    font-style: italic;
    color: var(--color-font);
  }

  .page .dictionary .b,
  .page .dictionary .form.type-infl .orth,
  .page .dictionary .form.type-drv .orth,
  .page .dictionary .hi.rend-b,
  .page .dictionary .emph {
    font-weight: bold;
  }

  .page .dictionary .form.type-inflected {
    display: none;
  }

  .page .dictionary .hi.rend-b,
  .page .dictionary .emph {
    font-weight: bold;
  }

  .page .dictionary .hi.rend-sc {
    font-variant: small-caps;
  }

  .page .dictionary .hi.rend-u {
    text-decoration: underline;
    font-size: inherit;
  }

  .page .dictionary .hi.rend-r {
    font-weight: normal;
    font-style: normal;
  }

  .page .dictionary .sup,
  .page .dictionary .hi.rend-sup {
    vertical-align: super;
    font-size: smaller;
  }

  .page .dictionary .sub,
  .page .dictionary .hi.rend-sub {
    vertical-align: sub;
    font-size: smaller;
  }

  .page .dictionary .hi.rend-i {
    font-style: italic;
  }

  .page .dictionary .i {
    font-weight: normal;
    font-style: italic;
    color: var(--color-font);
  }

  .page .dictionary .note {
    color: var(--color-font);
    line-height: 1.4em;
    font-style: normal;
    background-color: var(--color-divider);
    margin: 6px 0;
    padding: 6px 4px 6px 18px;
    font-weight: normal;
    display: block;
  }

  .page .dictionary .posp {
    font-size: 80%;
    text-transform: uppercase;
  }

  .page .dictionary .r {
    font-style: normal;
  }

  .page .dictionary .u {
    text-decoration: underline;
  }

  .page .dictionary .block {
    display: block;
    margin-top: 3px;
  }

  .page .hin .block {
    display: block;
    margin-top: 15px;
    margin-bottom: 7.5px;
  }

  .page .dictionary .bolditalic {
    font-weight: bold;
    font-style: italic;
  }

  .page span.bluebold {
    font-weight: bold;
    color: #1c4b8b;
  }

  .page span.italics,
  .page span.ital {
    font-style: italic;
  }

  .page span.sensenum {
    margin-left: -1.3em;
    float: left;
    font-weight: bold;
    font-size: 1.1em;
  }

  .page .dictionary .cit.type-translation .quote {
    font-style: normal;
    color: var(--color-brand);
  }

  .page span.bold,
  .page .dictionary .cit.type-translation .pos,
  .page .dictionary .var {
    font-style: bold;
  }

  .dc a,
  .openerBig {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
  }

  label.openerBig {
    display: inline;
  }

  .page .dictionary a:hover {
    color: var(--color-theme);
  }

  .page .dictionary .power {
    float: right;
  }

  .page .dictionary .power .i {
    color: #1c4b8b;
    font-size: inherit;
  }

  .page .dictionary .hom_subsec {
    display: block;
  }

  .page .dictionary .definitions,
  .page .dictionary .derivs,
  .page .dictionary .etyms {
    margin-bottom: 1em;
  }

  .page .dictionary .inflected_forms {
    display: block;
    padding-bottom: 1.25em;
  }

  .page .dictionary .scbold {
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.8em;
  }

  .page .dictionary .note .scbold {
    display: block;
  }

  .page .dictionary .pron .ptr {
    color: red;
  }

  .page .dictionary .list,
  .page .dictionary .relwordgrp {
    display: block;
    margin-left: 20px;
  }

  .page .dictionary .listitem,
  .page .dictionary .relwordunit {
    display: list-item;
  }

  .page .dictionary .type-syngrp,
  .page .dictionary .type-antgrp {
    display: block;
  }

  .page .asset.Corpus_Examples_EN .quote {
    font-style: italic;
  }

  .page .cobuild .sense {
    margin-left: 0;
  }

  .page .cit.type-example .content {
    background-color: white;
    margin-bottom: 20px;
    padding: 20px;
  }

  .page .cit.type-example .author {
    font-weight: bold;
    font-style: italic;
  }

  .page .cit.type-example .title {
    display: inline;
    font-variant: small-caps;
    font-style: italic;
  }

  .page .cit.type-example .ref.type-def {
    text-decoration: none;
    color: inherit;
  }

  .page .biling .lbl {
    font-style: italic;
    font-variant: initial;
    font-weight: initial;
  }

  .page .biling .lbl.type-subj {
    font-variant: small-caps;
  }

  .page .biling .lbl.type-tm {
    font-style: normal;
  }

  .page .biling .lbl.type-tm_hw {
    font-size: 0.78em;
  }

  .page .biling .lbl.type-infl span,
  .page .biling .lbl.type-infl {
    font-style: normal;
    color: #1c4b8b;
    font-weight: normal;
  }

  .page .biling br {
    display: none;
  }

  .page .biling .phrasals .re .orth {
    font-size: 1.25em;
  }

  .page .biling .sense .re {
    font-size: 100%;
    margin-left: 0;
  }

  .page .hin .form.type-syn .orth,
  .page .hin .form.type-ant .orth,
  .page .hin .form.type-phr .orth {
    font-weight: normal;
    font-size: 100%;
  }

  .page .biling .re {
    display: block;
    margin-left: 1em;
  }

  .page .thesbase .synunit .cit {
    display: inline;
  }

  .page .thesbase .xr.type-theslink {
    display: inline-block;
    margin-left: 20px;
  }

  .page .thesbase .relwgrp {
    display: block;
    margin-left: 1em;
  }

  .page .thesbase .caption {
    display: block;
    font-weight: bold;
    margin-top: 10px;
    font-size: larger;
  }

  .page .thesbase .table,
  .page .thesbase .bibl,
  .page .thesbase .cit.type-proverb {
    display: block;
  }

  .page .thesbase .bibl .title {
    display: inline;
  }

  .page .thesbase .tr {
    display: table-row;
  }

  .page .thesbase .td {
    display: table-cell;
    padding: 3px;
  }

  .page .thesbase .th {
    display: table-cell;
    font-weight: bold;
  }

  .page .thesbase .cit.type-example .quote {
    padding-left: 10px;
  }

  .page .thesbase .note {
    background-color: transparent;
    padding: 0;
    margin-top: 10px;
    overflow: hidden;
  }

  .page .thesbase .note .tr {
    display: block;
    margin-bottom: 20px;
  }

  .page .thesbase .tr .td:first-child {
    background-color: var(--color-divider);
    font-weight: bold;
    color: #1c4b8b;
    padding: 5px 15px;
  }

  .page .thesbase .note .td {
    padding: 8px 15px;
    display: block;
  }

  .page .thesbase .note .th {
    display: none;
  }

  .page .thesbase .link {
    text-decoration: underline;
    font-family: 'Open Sans', sans-serif;
    background: #e5ebf3;
    color: #1c4b8b;
    padding: 0.3em 0.8em;
    margin: 5px 0;
    display: inline-block;
  }

  .page .thesbase .sense {
    margin-bottom: 2em;
  }

  .page .thesbase .author {
    font-weight: bold;
    font-style: italic;
  }

  .cdet .content-box-origin {
    padding-top: 0;
    padding-bottom: 0;
  }

  .page .thesbase .sensehead > .sensenum {
    float: none;
  }

  .page .thesbase .scbold {
    background: #efefef;
    padding: 0.5em 22px;
    margin: 2em 0 1em 0;
    font-weight: bold;
    font-size: 80%;
    text-transform: uppercase;
    display: block;
  }

  .page .content-box-syn-of-syns div.type-syn_of_syn_head {
    display: inline-block;
  }

  .page .content-box-syn-of-syns div.type-syn_of_syn_head .orth,
  .page .thesbase .key {
    font-weight: bold;
    margin-right: 0;
    display: inline-block;
    margin-left: 0;
    padding: 0.3em 0.3em;
    border: 0;
    font-size: 1.1em;
    padding-left: 0;
    padding-bottom: 0;
  }

  .headwordSense {
    font-weight: bold;
    display: inline-block;
    font-size: 1.1em;
    cursor: pointer;
  }

  .page .thesbase .key {
    padding-right: 0;
  }

  .page .thesbase h2.first-sense {
    display: inline;
    font-size: inherit;
  }

  .thesbase ol.square {
    margin-left: 1.6em;
  }

  .page .thesbase .firstSyn {
    color: var(--color-font);
    font-size: 0.9em;
  }

  .page .content-box-syn-of-syns .syns_head {
    margin-top: 2.2em;
  }

  .page .content-box-syn-of-syns .syns_example {
    line-height: 2.5em;
  }

  .page .type-ant.columns3,
  .page .content-box-syn-of-syns .columns3 {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
  }

  .page .content-box-syn-of-syns .syns_items {
    display: block;
  }

  .pagination {
    text-align: center;
    margin: 1em;
  }

  .pagination a.prev,
  .pagination a.next,
  .pagination a.page,
  .pagination span.page,
  .pagination p,
  .pagination p a {
    padding: 0.3em 0.8em;
    display: inline-block;
    text-decoration: none;
    font-weight: bold;
    border: 0;
  }

  .pagination a.prev,
  .pagination a.next,
  .pagination a.page {
    background: #e5ebf3;
    color: #194885;
  }

  .pagination span.page,
  .pagination p,
  .pagination p a {
    background: #194885;
    color: #e5ebf3;
  }

  .page .content-box-syn-of-syns .lbl,
  .page .content-box-syn-of-syns .lbl span,
  .page .dictionary.thesbase .lbl,
  .page .dictionary.thesbase .lbl span {
    font-style: italic;
    color: green;
  }

  .page .dictionary.thesbase .sensebody {
    display: block;
    margin: 0.5em 0 0.5em 6px;
  }

  .page .thesbase span.bold {
    font-weight: bold;
  }

  .page .thesbase span.kerntouch {
    letter-spacing: -0.18em;
  }

  .page .thesbase span.kern60 {
    letter-spacing: -0.6em;
  }

  .page .thesbase span.manualdiacritic {
    vertical-align: 25%;
    letter-spacing: -1em;
  }

  .page .thesbase span.numerator {
    vertical-align: 35%;
    font-size: smaller;
  }

  .page .thesbase span.numerator_back {
    position: absolute;
    vertical-align: 35%;
    letter-spacing: -1em;
    font-size: smaller;
  }

  .page .thesbase span.denominator {
    vertical-align: -35%;
    font-size: smaller;
  }

  .page .thesbase span.italics {
    font-weight: normal;
    font-style: italic;
    color: var(--color-font);
  }

  .page .thesbase span.homnum {
    font-weight: bold;
    color: #fff;
    vertical-align: super;
    font-size: 50%;
  }

  .page .thesbase span.sensenum {
    font-weight: bold;
    display: inline-block;
    min-width: 1em;
  }

  .cdet .toc-group a {
    color: var(--color-brand);
    border-bottom: dashed 1px var(--color-brand);
  }

  .cdet .toc {
    text-align: left;
    padding: 0 1em;
    margin-top: 1em;
  }

  .cdet .toc-group .pos {
    font-style: italic;
  }

  .cdet .toc-group .orth {
    color: var(--color-brand);
  }

  .page .thesbase span.QA {
    font-style: italic;
    color: red;
    font-size: 90%;
  }

  .page .thesbase hr {
    width: 50%;
    text-align: left;
    border: 3px inset #555;
    height: 6px;
    margin: 10px auto 5px 0;
  }

  .page .thesbase .cit.type-quotation {
    display: block;
  }

  .page .thesbase .cit.type-quotation > .quote,
  .page .thesbase .cit.type-proverb > .quote,
  .page .thesbase .cit.type-quotation > .bibl {
    display: block;
    margin-left: 1em;
    padding-left: 0;
  }

  .page .thesbase > .re.type-phr .xr {
    font-weight: bold;
  }

  .page .thesbase .div .xr {
    display: block;
    margin-left: 1em;
  }

  .cdet .content-box {
    padding: 0;
    border-left: none;
  }

  .cdet .toggleExample {
    position: absolute;
    right: 0;
    top: -1em;
    padding: 0.2em;
    padding-right: 1em;
    background-color: rgba(164, 189, 212, 0.53);
  }

  .cdet .blockSyn {
    position: relative;
  }

  .cdet div[data-type-block] .sense .sensenum {
    margin-left: 0;
  }

  .cdet .page .dictionary .sense,
  .cdet .sense.moreSyn {
    margin-left: 0;
    margin-bottom: 0.5em;
    padding-bottom: 0.5em;
    position: relative;
  }

  .cdet .sense .sensehead .xr {
    display: none;
  }

  .cdet .h1Word {
    color: #e9573f;
  }

  .cdet .content-box-syn-of-syns .syns_container {
    padding-left: 1.9em;
  }

  .cdet .dictionary.thesbase .sensebody,
  .cdet div[data-type-block] .sense .sensebody {
    margin: 0;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    padding-right: 1.9em;
    padding-left: 1.9em;
    line-height: 1.5em;
    font-size: 0.9em;
  }

  .cdet .content-box-syn-of-syns div[data-type-block] .sense .def,
  .cdet .content-box-syn-of-syns div[data-type-block] .sense .syns_example {
    margin: 0;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    line-height: 1.5em;
    font-size: 0.9em;
    padding-right: 0;
    padding-left: 0;
  }

  .cdet .sensehead > .sensenum {
    min-width: 1.9em;
    display: inline-block;
    text-align: center;
    font-size: 0.9em;
    color: #4d4e51;
  }

  .cdet .dictionary .sense.opened,
  .cdet div[data-type-block] .sense.opened {
    margin-left: 0;
    margin-bottom: 1em;
    padding-bottom: 0.5em;
    cursor: auto;
    position: relative;
  }

  .cdet .dictionary.thesbase .sense.opened .sensebody,
  .cdet div[data-type-block] .sense.opened .sensebody {
    overflow: auto;
    text-overflow: inherit;
    white-space: inherit;
  }

  .cdet .toggleExample,
  .cdet .iconContainer {
    display: none;
  }

  .cdet .iconContainer {
    position: absolute;
    right: 0;
  }

  .cdet .sense.opened .iconContainer {
    display: block;
  }

  .cdet .syns_container {
    padding-left: 0;
  }

  .cdet .content-box-syn-of-syns .sense.moreSyn {
    margin-bottom: 0.5em;
  }

  .cdet .form.type-syn .orth,
  .cdet .type-ant .orth,
  .cdet .syns_container .form.type-syn .orth {
    background-color: transparent;
    border: 0;
    font-size: 0.97em;
    font-weight: bold;
    text-decoration: none;
    color: #4d4e51;
    margin: 0;
    display: inline-block;
  }

  .cdet .dictionary .content,
  .cdet .form.type-syn {
    position: relative;
  }

  .cdet .titleTypeContainer {
    font-size: 0.9em;
  }

  .cdet .titleTypeContainer .titleType {
    font-size: 1.1em;
    color: #4d4e51;
    display: inline-block;
    margin-top: 0.5em;
    font-weight: bold;
  }

  body.context-language-THESAURUS {
    background-color: white;
  }

  .cdet .sense .form *[class*='type'] {
    font-size: 0.9em;
  }

  .cdet .titleTypeSubContainer {
    margin-top: 0.5em;
    font-weight: bold;
    font-size: 0.9em;
  }

  .cdet .form.type-syn .titleTypeSubContainer {
    display: block;
  }

  .cdet .sense.moreAnt .type-ant div,
  .cdet .form.type-syn .titleTypeSubContainer,
  .cdet .form.type-ant .titleTypeSubContainer,
  .cdet .form.type-ant,
  .cdet .blockAnt,
  .cdet .blockSyn,
  .cdet .blockAnt div {
    display: inline;
  }

  .entry.dictionary.thesbase.content-box.content-box-thesbase {
    position: relative;
  }

  .cdet .form.type-syn .titleTypeSubContainer:after,
  .cdet .form.type-ant .titleTypeSubContainer:after {
    content: ': ';
    display: inline;
  }

  .cdet .titleTypeSubContainer .titleType {
    display: inline;
    padding: 0;
    font-size: 1.3em;
    font-variant: all-small-caps;
  }

  .cdet .blockSyn {
    margin-bottom: 1em;
  }

  .headerSensePos {
    font-size: 0.9em;
    color: #888;
    font-style: italic;
  }

  .blockAnt .type-ant > div:before {
    content: ', ';
    display: inline;
  }

  .cdet .blockAnt .type-ant > div:first-child:before {
    content: '';
    display: inline;
  }

  .cdet .type-ant .orth {
    font-weight: normal;
  }

  .cdet .type-ant,
  .cdet .content-box-syn-of-syns .syns_container {
    padding-left: 0;
    margin-left: 0;
  }

  .cdet .content-box-comments {
    background-color: white;
    margin-bottom: 20px;
    padding: 20px;
    position: relative;
  }

  .cdet .content-box-comments,
  .cdet .content-box-origin,
  .cdet .content-box-nearby-words,
  .cdet .dictionary .content,
  .cdet .content-box-syn-of-syns {
    border-left: none;
    box-shadow: none;
  }

  .cdet .re.type-phr .xr,
  .cdet .content-box-nearby-words li {
    margin-left: 0;
    padding-left: 0.85em;
    margin-bottom: 0.3em;
    padding-bottom: 0.3em;
    display: block;
  }

  .cdet .content-box-syn-of-syns div.type-syn_of_syn_head {
    display: block;
  }

  .cdet .content-box-syn-of-syns .sense .def {
    padding-left: 1em;
  }

  .cdet .content-box-syn-of-syns .sense .def,
  .cdet .content-box-syn-of-syns .sense .syns_example {
    margin: 0;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    line-height: 1.5em;
    font-size: 0.9em;
    color: #4d4e51;
  }

  .cdet .content-box-syn-of-syns .quote {
    font-style: italic;
  }

  .cdet div[data-type-block] .sense.opened .sensebody,
  .cdet .content-box-syn-of-syns .sense.opened .def,
  .cdet .content-box-syn-of-syns .sense.opened .syns_example {
    overflow: auto;
    white-space: normal;
  }

  .cdet .content-box-syn-of-syns .syns_head {
    margin-top: 0;
  }

  .cdet .cit.type-quotation > .bibl {
    font-size: 0.85em;
  }

  .cdet .cit.type-quotation {
    margin-left: 0;
    margin-bottom: 0.3em;
    padding-bottom: 0.3em;
  }

  .cdet .re.type-phr > .titleTypeContainer,
  .cdet .cit.type-quotation > .titleTypeContainer {
    margin-bottom: 1em;
  }

  .cdet .cit.type-quotation > .quote {
    line-height: 1.3em;
    margin-bottom: 0.3em;
  }

  .cdet .syns_example .cit.type-example {
    overflow: inherit;
    text-overflow: inherit;
    white-space: inherit;
  }

  .cdet .cit.type-quotation .title,
  .cdet .cit.type-quotation .author {
    font-size: inherit;
  }

  .cdet span.sensenum {
    margin-left: 0;
  }

  .cdet .dictionary .quote {
    color: #4d4e51;
    font-size: 0.9em;
  }

  .cdet span.quote:before {
    content: '';
    margin: 2px 6px;
    display: inline-block;
    background-color: var(--color-font);;
    padding: 3px;
  }

  .cdet .headerSense .sensenum {
    margin-left: -20px;
  }

  .cdet .synonymBlock,
  .cdet .containerBlock {
    margin-left: 20px;
  }

  .cdet .headerThes .entry_title + div a {
    color: var(--color-brand);
    font-size: 0.9em;
    margin-bottom: 1em;
    display: inline-block;
  }

  .cdet .form {
    display: inline;
  }

  .cdet .cit.type-example,
  .selectorOpernerBig:checked + .sense.opened .sep,
  .selectorOpernerBig:checked + .sense.opened .openerBig,
  .selectorOpernerBig:checked ~ label[for='default'],
  .selectorOpernerBig {
    display: none;
  }

  .selectorOpernerBig + .shadow_layer {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(128, 128, 128, 0.78);
    z-index: 2;
  }

  .selectorOpernerBig:checked + .sense.opened .cit.type-example,
  .selectorOpernerBig:checked + .sense.opened div > .form {
    display: block;
  }

  .selectorOpernerBig:checked + .sense.opened:before {
    content: '';
    display: block;
    height: 2em;
    position: fixed;
    top: 0;
    z-index: 1;
    background-color: white;
    width: 70%;
    left: 15%;
  }

  .selectorOpernerBig:checked + .sense.sense.opened {
    position: fixed;
    top: 0;
    left: 15%;
    height: calc(100% - 2em);
    width: 70%;
    background-color: white;
    z-index: 3;
    margin: 0;
    padding: 1em;
    overflow-y: auto;
    margin-top: 2em;
  }

  .selectorOpernerBig ~ label.menuPanelCloseButton {
    position: fixed;
    display: block;
    top: 0;
    border-bottom: 0;
    z-index: 4;
    right: calc(15% - -10px);
  }

  .he .grammar .page {
    display: block;
    border: solid 1px;
    font-family: arial, helvetica, sans-serif;
    margin-bottom: 20px;
    padding: 15px;
    padding-bottom: 40px;
  }

  .he .grammar a.previous,
  .he .grammar a.next {
    background: #e5ebf3;
    color: #194885;
    padding: 0.5em 1em;
    font-weight: bolder;
    border-bottom: 0;
    float: left;
    margin-top: 1em;
  }

  .he .grammar a.next {
    float: right;
  }

  .he .grammar a.previous:hover,
  .he .grammar a.next:hover {
    color: #194885;
  }

  .he .grammar a.previous i,
  .he .grammar a.next i {
    font-size: 1.3em;
    vertical-align: middle;
    padding-right: 8px;
    padding-left: 8px;
    display: inline-block;
  }

  .he .grammar .exmplblk ul {
    padding-left: 0;
  }

  .he .grammar .exmplblk {
    padding: 0.5em;
  }

  .he .grammar .exmplgrp ul {
    padding-left: 20px;
    padding-bottom: 10px;
  }

  .he .grammar .intro.suppressed {
    display: none;
  }

  .he .grammar h2 {
    font-size: 16pt;
    line-height: 2em;
    text-decoration: underline;
  }

  .he .grammar h3 {
    font-size: 14pt;
  }

  .he .grammar h4 {
    font-size: 12pt;
    font-weight: bold;
    margin-bottom: 1em;
  }

  .he .grammar u {
    text-decoration: underline;
  }

  .he .grammar .lemma {
    font-weight: bold;
  }

  .he .grammar .caption {
    font-weight: bold;
    margin-top: 1.5em;
  }

  .he .grammar .p {
    display: block;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .he .grammar .group {
    display: block;
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .he .grammar .exmpl {
    font-weight: normal;
    font-style: italic;
  }

  .he .grammar .i,
  .he .grammar .post {
    font-style: italic;
  }

  .he .grammar .posp {
    font-weight: bold;
    font-style: normal;
  }

  .he .grammar .pattern {
    font-family: sans-serif;
  }

  .he .grammar .ul {
    margin-top: 5px;
    list-style-type: none;
    padding-left: 15px;
  }

  .he .grammar ul.arrow {
    list-style-type: square;
  }

  .he .grammar ul.star {
    list-style-type: disc;
  }

  .he .grammar ul.alpha {
    list-style-type: lower-alpha;
  }

  .he .grammar ol {
    margin-top: 5px;
    list-style-type: decimal;
  }

  .he .grammar .li.exmpl {
    font-style: italic;
  }

  .he .grammar .lemmalist .li {
    margin-top: 10px;
  }

  .he .grammar .lemmalist {
    border-color: #ccc;
    border-style: solid;
    border-width: 1px;
    margin: 4px;
    margin-top: 2em;
    padding: 1em;
    -webkit-column-count: 4;
    -moz-column-count: 4;
    column-count: 4;
  }

  .he .grammar div.greyborder2 {
    border-color: #ccc;
    border-style: solid;
    border-width: 1px;
    margin: 4px;
    -webkit-column-count: 4;
    -moz-column-count: 4;
    column-count: 4;
  }

  .he .grammar th,
  .he .grammar td {
    border-color: #000;
    border-style: solid;
    border-width: 1px;
    padding: 0.5em 1.4em;
  }

  .he .grammar th {
    background-color: #ddd;
    font-weight: bold;
    font-size: 0.9em;
  }

  .he .grammar table {
    border-collapse: collapse;
    border-color: #000;
    border-style: solid;
    border-width: 1px;
    margin-top: 1.5em;
    margin-bottom: 1em;
  }

  .linksTool + .linksTool {
    margin-top: 0.5em;
  }

  .linksTool {
    margin: 1.5em 1em 1.5em 1em;
    font-style: italic;
    font-size: 0.9em;
  }

  .he .grammar a.block {
    display: block;
  }

  .he .grammar i.icon-chevron-thin-right {
    display: inline-block;
    font-weight: bold;
    width: 2em;
    text-align: center;
    font-size: 0.6em;
  }

  .he .grammar .group a:before,
  .he .grammar .section a:before,
  .he .grammar .posGr a:before,
  .he .grammar .subpattern a:before,
  .he .grammar .pattern a:before,
  .he .grammar .chapter a:before {
    display: block;
    content: '';
  }

  .entry_container {
    color: inherit;
    display: block;
    background: #fff;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    margin-bottom: 20px;
    padding: 20px;
  }

  .he .grammar .breadcrumb {
    margin-bottom: 2em;
  }

  .synonymBlock {
    border: 1px solid transparent;
  }

  .synonymBlock:after {
    content: '';
    display: block;
    clear: both;
  }

  .grammar .bold {
    display: inline;
    font-weight: bold;
  }

  .grammar .italic {
    display: inline;
    font-style: italic;
  }

  .grammar .bolditalic {
    display: inline;
    font-weight: bold;
    font-style: italic;
  }

  .grammar .roman {
    font-style: normal;
  }

  .grammar .color {
    color: #0058a9;
  }

  .grammar .color1 {
    color: #0058a9;
    font-style: normal;
  }

  .grammar .chaptitle {
    font-size: x-large;
    font-weight: bold;
    color: #0058a9;
  }

  .grammar .parttitle {
    font-size: xx-large;
    font-weight: bold;
    color: #0058a9;
  }

  .grammar .head1 {
    font-weight: bold;
  }

  .grammar .head {
    font-size: medium;
    font-weight: bold;
    color: #0058a9;
    margin-top: 2em;
  }

  .grammar .p {
    font-size: medium;
    font-style: normal;
    text-indent: 0;
  }

  .grammar .ind {
    font-size: medium;
    font-weight: normal;
    font-style: normal;
    text-indent: 0;
    margin-top: 0.3em;
    margin-bottom: 0.3em;
    margin-left: 0.8em;
    text-indent: -0.8em;
  }

  .grammar .ul.cll0 {
    padding-left: 0;
    list-style-type: disc;
  }

  .grammar .ul.cll0 .li {
    margin-left: 1em;
  }

  .grammar .ul.cll1 {
    padding-left: 0;
    list-style-type: none;
  }

  .grammar .ul.cll1 > .li:before {
    content: '– ';
  }

  .grammar .ul.cll1 .li,
  .grammar .ul.cll2 .li,
  .grammar .ol.cll4 .li {
    margin-left: 0.8em;
    text-indent: -0.8em;
  }

  .grammar .ul.cll2a,
  .grammar .ul.cll2 {
    list-style-type: none;
    margin-top: 1em;
    margin-bottom: 1em;
  }

  .grammar .ul.cll2a {
    padding-left: 0.75em;
  }

  .grammar .ul.cll2a .li {
    margin-left: 0;
    text-indent: -0.8em;
  }

  .grammar .ul.cll3 {
    padding-left: 1.2em;
    color: #0058a9;
  }

  .grammar .ul.cll3 > .li > span {
    color: var(--color-font);
  }

  .grammar .ol.cll4 {
    list-style-type: none;
    margin-top: 1em;
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  .grammar span.label {
    width: 0.8em;
    display: inline-block;
    color: #0058a9;
    font-weight: bold;
  }

  .grammar span.label1 {
    width: 0.8em;
    display: inline-block;
  }

  .grammar .block {
    font-size: 0.83em;
    font-style: italic;
    font-weight: normal;
    text-align: justify;
    text-indent: 0;
    margin: 0.3em 1.3em;
  }

  .grammar div.box {
    border: 1px solid #0058a9;
    margin-top: 2em;
    margin-bottom: 2em;
    padding: 0 2.5em;
    background-color: #e1e4ee;
    border-radius: 15px;
  }

  .grammar .toc {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  .grammar .center {
    text-align: center;
  }

  .grammar .right {
    text-align: right;
  }

  .grammar .small {
    font-size: 78%;
  }

  .grammar td {
    vertical-align: top;
  }

  .grammar td.filet_b,
  .grammar td.filet_t,
  .grammar td.filet_l,
  .grammar td.filet_r {
    border-right: 1px solid var(--color-font);
  }

  .grammar .tab1 {
    margin-left: 5em;
  }

  .grammar .strike {
    text-decoration: line-through;
  }

  .navigation .tab.current .ref > .pos {
    color: white;
  }

  .cdet .ref > .pos {
    font-style: italic;
    font-size: 0.9em;
    color: #777772;
  }

  .openTootip,
  .openTootip ~ .def {
    display: none;
  }

  .openTootip:checked ~ .def {
    display: block;
  }

  .linkDef {
    display: inline;
  }

  .selectorOpernerBig:checked + .sense.sense.opened .cit .quote:before {
    display: none;
  }

  @media screen and (min-width: 321px) {
    .cdet .page .dictionary .sense,
    .cdet .sense.moreSyn,
    .cdet .dictionary .hom,
    .cdet .dictionary .syn_of_syns {
      overflow: hidden;
    }
  }

  @media screen and (max-width: 761px) {
    .cdet .navigation .nav {
      display: none;
    }

    .cdet .more {
      margin: 10px auto 10px auto;
      width: 50%;
    }

    .selectorOpernerBig:checked + .sense.sense.opened:before,
    .selectorOpernerBig:checked + .sense.sense.opened {
      width: 100%;
      left: 0;
    }

    .selectorOpernerBig ~ label.menuPanelCloseButton {
      right: 0;
    }
  }

  .navigation {
    position: relative;
    width: 100%;
  }

  .navigation:before {
    content: '\a0';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    background-color: inherit;
  }

  .navigation a.tab,
  .navigation span.tab > a {
    font-size: 0.9em;
    display: inline-block;
    line-height: 26px;
    padding: 4px 8px;
    color: #333;
    background-color: #e5ebf3;
    margin: 6px 2px 10px 2px;
    border: 0;
    border-radius: 2px;
  }

  .navigation .expo {
    position: relative;
    top: -4px;
    font-size: 0.8em;
    margin-left: 2px;
  }

  .navigation[data-position='fixed'] {
    position: fixed;
    top: 50px;
    z-index: 2;
  }

  .navigation a.nav:hover {
    background-color: #1c4b8b;
    color: white;
  }

  .navigation .tab .ref:active,
  .navigation a.tab.current,
  .navigation span.tab.current > a {
    background-color: #1c4b8b;
    color: white;
    position: relative;
  }

  .cdet .ref:active > .pos {
    color: white;
  }

  .navigation .tabsNavigation {
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    word-wrap: normal;
  }

  .navigation .tabsNavigation i {
    font-size: 0.85em;
  }

  .navigation .tab.current::before {
    content: '';
    display: inline-block;
    position: absolute;
    left: 50%;
    left: 1.2em;
    bottom: -5px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 5px 5px 5px;
    border-color: transparent transparent #1c4b8b transparent;
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
  }

  .navigation a.nav {
    background: #cdd4de;
    position: absolute;
    top: 0;
    font-size: 20px;
    overflow: hidden;
    display: inline-block;
    color: #4d4e51;
    margin-left: 0;
    margin-right: 0;
    padding-left: 15px;
    padding-right: 15px;
  }

  .navigation .left {
    left: 0;
  }

  .navigation .right {
    right: 0;
  }

  .cdet .navigation {
    background-color: white;
  }

  .navigation .tab a {
    border-bottom: 0;
  }

  .cdet .tab span + span {
    display: inline-block;
    padding-left: 0.3em;
  }

  .cdet .tab a {
    display: inline-block;
  }

  .dc .frenquency-title {
    float: right;
  }

  .dc .word-frequency-img {
    margin-left: 0.5em;
  }

  .dc .word-frequency-container .level {
    border-radius: 50%;
    display: inline-block;
  }

  .dc .word-frequency-container .level.roundRed {
    background-color: var(--color-theme);
  }

  .dc .word-frequency-container .level1 {
    width: 14px;
    height: 14px;
  }

  .dc .word-frequency-container .level2 {
    width: 15px;
    height: 15px;
  }

  .dc .word-frequency-container .level3 {
    width: 16px;
    height: 16px;
  }

  .dc .word-frequency-container .level4 {
    width: 17px;
    height: 17px;
  }

  .dc .word-frequency-container .level5 {
    width: 18px;
    height: 18px;
  }

  .dc .word-frequency-container .round {
    width: 100%;
    height: 100%;
  }

  .dc .word-frequency-container.relevance .level {
    border-radius: 0;
    width: 15px;
    vertical-align: bottom;
  }

  .dc .word-frequency-container.relevance .level1 {
    background: #f6b26b;
    height: 10px;
  }

  .dc .word-frequency-container.relevance .level2 {
    background: #f6b26b;
    height: 13px;
  }

  .dc .word-frequency-container.relevance .level3 {
    background: #ffd966;
    height: 16px;
  }

  .dc .word-frequency-container.relevance .level4 {
    background: #ffd966;
    height: 19px;
  }

  .dc .word-frequency-container.relevance .level5 {
    background: #b6d7a8;
    height: 22px;
  }

  .dc .word-frequency-container.relevance .level6 {
    background: #b6d7a8;
    height: 25px;
  }

  .lightboxLink {
    cursor: pointer;
  }

  .lightboxOverlay {
    background: rgba(0, 0, 0, 0.8);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .lightboxContainer {
    display: inline-block;
    position: relative;
    padding-bottom: 30px;
  }

  .lightboxImage {
    min-height: 200px;
    min-width: 200px;
    vertical-align: middle;
    border: solid 5px #fff;
    border-radius: 3px;
    background: #fff;
  }

  .lightboxClose {
    position: absolute;
    right: 0;
    bottom: 0;
    color: #fff;
    font-size: 2em;
  }

  .lightboxCopyright {
    position: absolute;
    bottom: 0;
    color: #fff;
    left: 0;
  }

  .tagline {
    font-size: 15px;
    margin-left: 4px;
  }

  .home_menu {
    margin: 1em 0;
    text-align: center;
  }

  .home_logo_link {
    display: inline-block;
  }

  .home_logo {
    width: 100%;
    max-width: 320px;
  }

  .home_menu li {
    display: inline-block;
  }

  .home_menu a.current,
  .home_menu a:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .home_menu a {
    padding: 10px 25px;
    color: inherit;
    display: inline-block;
    -webkit-transition: background-color 0.4s ease, color 0.4s ease;
    -moz-transition: background-color 0.4s ease, color 0.4s ease;
    -o-transition: background-color 0.4s ease, color 0.4s ease;
    transition: background-color 0.4s ease, color 0.4s ease;
  }

  .home_search_container {
    margin: 1em auto 0 auto;
  }

  .dataset-description {
    font-size: 1em;
    opacity: 0.8;
    margin-top: 1em;
    text-align: center;
  }

  h1 {
    padding-top: 0.5em;
    font-size: 2em;
    text-align: center;
  }

  .search-desktop {
    width: 900px;
  }

  .word-content li {
    display: inline-block;
  }

  .blue {
    background: #e8e8e8;
    color: #000;
  }

  .blue .main-content {
    padding: 0;
  }

  .main-content {
    padding: 20px 0;
    padding-top: 0;
    margin: 0 auto;
  }

  .word-content {
    text-align: center;
  }

  .word-content .home-link.current,
  .word-content .home-link:hover {
    background: #ddd;
  }

  .word-content .home-link.current {
    font-weight: bold;
  }

  .word-content .home-link {
    display: inline-block;
    color: inherit;
    padding: 1em 1.15em;
    position: relative;
    text-decoration: none;
  }

  .word-content .home-link.current::before {
    content: '';
    display: inline-block;
    position: absolute;
    left: 50%;
    margin-left: -10px;
    top: -10px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 10px 10px 10px;
    border-color: transparent transparent #e5ebf3 transparent;
  }

  .white {
    padding-top: 20px;
  }

  @media screen and (max-width: 761px) {
    header .main-content {
      padding-top: 10px;
    }

    header .main-content {
      padding-top: 0;
      background-position: calc(100% - 10px) calc(10px);
      background-size: 20%;
      padding-bottom: 25px;
    }

    .search-desktop {
      width: 100%;
    }

    .home_search_container {
      margin: 0 10px;
      display: block;
    }

    .word-content .home-link.current::before {
      display: none;
    }

    .word-content .home-link {
      padding: 0.5em;
    }

    .home_logo_link {
      display: block;
      text-align: center;
      margin-top: 1em;
      margin-bottom: 2em;
    }

    .home_logo {
      max-width: none;
      width: 80%;
    }

    .white {
      padding-top: 10px;
    }
  }

  @media screen and (min-width: 762px) and (max-width: 948px) {
    .tagline {
      font-size: 10px;
      margin-left: 4px;
    }

    header .main-content {
      background-position: calc(100% - 5px) calc(5px);
      background-size: 25%;
    }

    .search-desktop {
      width: 700px;
      margin: 0 auto;
    }

    .home_logo {
      max-width: 210px;
    }
  }

  .columns .columns_item {
    display: inline-block;
    width: 24.5%;
    padding-right: 20px;
    vertical-align: top;
    min-height: 360px;
  }

  @media screen and (max-width: 761px) {
    .columns .columns_item {
      display: block;
      width: auto;
      padding-right: 0;
      min-height: 0;
    }
  }

  @media screen and (min-width: 762px) {
    .columns .columns_item {
      width: 49.5%;
    }
  }

  [class^='icon-'],
  [class*=' icon-'] {
    font-family: 'icomoon';
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .icon-fw {
    display: inline-block;
    text-align: center;
    width: 1.55em;
  }

  .icon-2x {
    font-size: 2em;
  }

  .icon-chat:before {
    content: '\e900';
  }

  .icon-community:before {
    content: '\e901';
  }

  .icon-keyboard:before {
    content: '\e955';
  }

  .icon-search:before {
    content: '\f002';
  }

  .icon-user:before {
    content: '\f007';
  }

  .icon-times:before {
    content: '\f00d';
  }

  .icon-volume-up:before {
    content: '\f028';
  }

  .icon-twitter-square:before {
    content: '\f081';
  }

  .icon-twitter:before {
    content: '\f099';
  }

  .icon-facebook:before {
    content: '\f09a';
  }

  .icon-globe:before {
    content: '\f0ac';
  }

  .icon-bars:before {
    content: '\f0c9';
  }

  .icon-caret-down:before {
    content: '\f0d7';
  }

  .icon-caret-up:before {
    content: '\f0d8';
  }

  .icon-caret-left:before {
    content: '\f0d9';
  }

  .icon-caret-right:before {
    content: '\f0da';
  }

  .icon-copyright:before {
    content: '\f1f9';
  }

  .icon-facebook-official:before {
    content: '\f230';
  }

  .icon-warning:before {
    content: '\e907';
  }

  .icon-fiber_new:before {
    content: '\e05e';
  }

  .icon-trending_down:before {
    content: '\e8e3';
  }

  .icon-trending_flat:before {
    content: '\e8e4';
  }

  .icon-trending_up:before {
    content: '\e8e5';
  }

  .icon-chevron-thin-left:before {
    content: '\e905';
  }

  .icon-chevron-thin-right:before {
    content: '\e906';
  }

  .icon-books:before {
    content: '\e920';
  }

  .icon-eye-plus:before {
    content: '\e9cf';
  }

  .icon-eye-minus:before {
    content: '\e9d0';
  }

  .icon-share2:before {
    content: '\ea82';
  }

  .icon-read:before {
    content: '\e902';
  }

  .icon-copy:before {
    content: '\e908';
  }

  .icon-exchange:before {
    content: '\e909';
  }

  .icon-sort:before {
    content: '\f0dc';
  }

  [class*='res_cell'] {
    float: left;
    display: block;
    width: 100%;
  }

  .res_cell_left {
    width: 160px;
    min-height: 1px;
  }

  .res_cell_right {
    width: 300px;
  }

  .res_cell_2_3 {
    width: 66%;
  }

  .res_cell_2_3_content {
    padding: 0 20px 0 0;
  }

  .res_cell_1_3 {
    width: 34%;
  }

  .mpuslot_b-container {
    min-width: 320px;
    text-align: center;
    padding: 0;
  }

  @media screen and (max-width: 320px) {
    .mpuslot_b-container {
      margin: 0 0 0 -38px;
    }

    .cdet .mpuslot_b-container {
      margin: 0 0 0 -10px;
    }

    .content-box-examples .mpuslot_b-container {
      margin: 0 0 0 -14px;
    }

    .mpuslot_b-container-amp {
      margin: 0 0 0 -26px;
    }

    .thesbase .mpuslot_b-container-amp {
      margin: 0;
    }

    .content-box-examples .mpuslot_b-container-amp {
      margin: 0 0 0 -4px;
    }
  }

  @media screen and (min-width: 321px) and (max-width: 761px) {
    .mpuslot_b-container {
      margin: 0 0 0 -24px;
    }

    .cdet .mpuslot_b-container {
      margin: 0;
    }

    .content-box-examples .mpuslot_b-container {
      margin: 0 0 0 -14px;
    }
  }

  @media screen and (max-width: 761px) {
    main > .dictionary,
    main > .browse_wrapper,
    main > .spellcheck_wrapper,
    main > .content_wrapper,
    main > .submit_new_word_wrapper,
    main > .word_submitted_wrapper,
    main > .suggested_word_wrapper {
      width: 100%;
      float: none;
    }

    [class*='res_cell'] {
      clear: both;
      width: 100%;
      margin: 0 0 10px 0;
    }

    .res_cell_center_content,
    .res_cell_2_3_content {
      padding: 0;
    }

    .res_hos {
      display: none;
    }

    .homepage header .left {
      float: none;
    }

    main,
    .main-content {
      width: 100%;
    }

    .page .type-ant.columns3,
    .page .content-box-syn-of-syns .columns3 {
      -webkit-column-count: 1;
      -moz-column-count: 1;
      column-count: 1;
    }

    .dc .entry_title {
      font-size: 1.5em;
      padding-left: 0.5em;
    }

    .topslot_container {
      margin-bottom: 10px;
    }

    .mpuslot_b {
      width: 320px;
      margin: 0 auto;
    }

    .columns .columns_item {
      display: block;
      width: auto;
      padding-right: 0;
      min-height: 0;
    }

    .page .dictionary .power {
      display: none;
    }

    .content-box,
    .page .Corpus_Examples_EN .content,
    .dc .content-box,
    .wotd-txt-block,
    .promoBox-content {
      padding: 10px;
    }

    .dc .content-box {
      padding-top: 0;
    }

    .promoBox,
    .promoBox {
      min-height: 0;
    }

    .search-desktop {
      display: none;
    }

    .search-desktop {
      display: block;
    }

    #searchPanelButton:checked ~ .search-desktop {
      display: block;
      width: auto;
      margin: 4px;
      clear: both;
    }

    .search-desktop .custom-select {
      display: block;
      position: absolute;
      left: 0;
      opacity: 0.001;
      width: 50px;
      height: 100%;
      font-size: medium;
    }

    .search-desktop .custom-select-menu {
      -webkit-column-count: 1;
      -moz-column-count: 1;
      column-count: 1;
    }

    .dc .h1_entry {
      line-height: 1.2em;
    }

    .cdet .titleTypeContainer .titleType {
      margin: 0;
    }

    .dc .content-box.cobuild.br .content-box-header::after {
      content: none;
    }

    .dc .content-box-videos .entryVideo {
      max-width: 320px;
      height: 160px;
    }

    .cdet .dc .entry_title,
    .cdet .headerThes .entry_title + div {
      padding-left: 10px;
    }
  }

  @media screen and (min-width: 762px) and (max-width: 948px) {
    main > .dictionary,
    main > .browse_wrapper,
    main > .spellcheck_wrapper,
    main > .content_wrapper,
    main > .submit_new_word_wrapper,
    main > .word_submitted_wrapper,
    main > .suggested_word_wrapper {
      width: 100%;
      float: none;
    }

    .res_cell_right {
      width: 230px;
    }

    .res_cell_center_content {
      padding-left: 0;
    }
  }

  i[class^='icon'],
  .extra-link,
  .socialButtons {
    display: none !important;
  }
}

.share-overlay,
.popup-overlay,
.share-button {
  display: none !important;
}
