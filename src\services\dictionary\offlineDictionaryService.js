// 离线词典服务
// 注意：实际使用时需要下载词典数据文件

// 词典数据将被异步加载
let dictionaryData = null;
let synonymsData = null;
let antonymsData = null;

// 加载词典数据
const loadDictionaryData = async () => {
  try {
    // 在实际应用中，这些数据应该从本地JSON文件或IndexedDB加载
    // 这里使用示例URL，实际使用时需要替换为真实路径
    const response = await fetch('/data/dictionary.json');
    dictionaryData = await response.json();
    console.log('词典数据加载成功');
    return true;
  } catch (error) {
    console.error('加载词典数据失败:', error);
    return false;
  }
};

// 加载同义词数据
const loadSynonymsData = async () => {
  try {
    const response = await fetch('/data/synonyms.json');
    synonymsData = await response.json();
    console.log('同义词数据加载成功');
    return true;
  } catch (error) {
    console.error('加载同义词数据失败:', error);
    return false;
  }
};

// 加载反义词数据
const loadAntonymsData = async () => {
  try {
    const response = await fetch('/data/antonyms.json');
    antonymsData = await response.json();
    console.log('反义词数据加载成功');
    return true;
  } catch (error) {
    console.error('加载反义词数据失败:', error);
    return false;
  }
};

// 初始化词典
const initDictionary = async () => {
  const results = await Promise.all([
    loadDictionaryData(),
    loadSynonymsData(),
    loadAntonymsData()
  ]);
  
  return results.every(result => result === true);
};

// 查询单词定义
const lookupDefinition = (word) => {
  if (!dictionaryData) {
    console.warn('词典数据尚未加载');
    return [];
  }
  
  const normalizedWord = word.toLowerCase().trim();
  return dictionaryData[normalizedWord] || [];
};

// 查询同义词
const lookupSynonyms = (word) => {
  if (!synonymsData) {
    console.warn('同义词数据尚未加载');
    return [];
  }
  
  const normalizedWord = word.toLowerCase().trim();
  return synonymsData[normalizedWord] || [];
};

// 查询反义词
const lookupAntonyms = (word) => {
  if (!antonymsData) {
    console.warn('反义词数据尚未加载');
    return [];
  }
  
  const normalizedWord = word.toLowerCase().trim();
  return antonymsData[normalizedWord] || [];
};

// 获取单词的详细信息
const getWordDetails = (word) => {
  const normalizedWord = word.toLowerCase().trim();
  
  return {
    word: normalizedWord,
    definitions: lookupDefinition(normalizedWord),
    synonyms: lookupSynonyms(normalizedWord),
    antonyms: lookupAntonyms(normalizedWord)
  };
};

export {
  initDictionary,
  lookupDefinition,
  lookupSynonyms,
  lookupAntonyms,
  getWordDetails
};