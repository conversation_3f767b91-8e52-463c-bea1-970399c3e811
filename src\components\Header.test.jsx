import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { vi } from 'vitest';
import Header from './Header';

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  BookOpen: () => <div data-testid="book-open-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Maximize: () => <div data-testid="maximize-icon" />
}));

describe('Header', () => {
  const defaultProps = {
    onShowApiConfig: vi.fn(),
    onShowAuth: vi.fn(),
    onLogout: vi.fn(),
    onToggleImmersiveMode: vi.fn(),
    isDarkMode: false,
    user: null
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基本渲染', () => {
    it('应该渲染应用标题', () => {
      render(<Header {...defaultProps} />);

      expect(screen.getByText('對白')).toBeInTheDocument();
    });

    it('应该渲染logo', () => {
      render(<Header {...defaultProps} />);

      const logo = screen.getByAltText('對白');
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute('src', '/logo.svg');
    });

    it('应该在深色模式下使用深色logo', () => {
      render(<Header {...defaultProps} isDarkMode={true} />);

      const logo = screen.getByAltText('對白');
      expect(logo).toHaveAttribute('src', '/logo-dark.svg');
    });

    it('应该渲染沉浸写作模式按钮', () => {
      render(<Header {...defaultProps} />);

      const immersiveButton = screen.getByTitle('沉浸写作模式');
      expect(immersiveButton).toBeInTheDocument();
      expect(screen.getByTestId('maximize-icon')).toBeInTheDocument();
    });

    it('应该渲染设置按钮', () => {
      render(<Header {...defaultProps} />);

      const settingsButton = screen.getByTitle('设置');
      expect(settingsButton).toBeInTheDocument();
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
    });
  });

  describe('按钮交互', () => {
    it('应该在点击沉浸写作模式按钮时调用回调', () => {
      render(<Header {...defaultProps} />);

      const immersiveButton = screen.getByTitle('沉浸写作模式');
      fireEvent.click(immersiveButton);

      expect(defaultProps.onToggleImmersiveMode).toHaveBeenCalled();
    });

    it('应该在用户未登录时点击设置按钮调用onShowAuth', () => {
      render(<Header {...defaultProps} user={null} />);

      const settingsButton = screen.getByTitle('设置');
      fireEvent.click(settingsButton);

      expect(defaultProps.onShowAuth).toHaveBeenCalled();
      expect(defaultProps.onShowApiConfig).not.toHaveBeenCalled();
    });

    it('应该在用户已登录时点击设置按钮调用onShowApiConfig', () => {
      const mockUser = { uid: 'test-user', displayName: 'Test User' };
      render(<Header {...defaultProps} user={mockUser} />);

      const settingsButton = screen.getByTitle('设置');
      fireEvent.click(settingsButton);

      expect(defaultProps.onShowApiConfig).toHaveBeenCalled();
      expect(defaultProps.onShowAuth).not.toHaveBeenCalled();
    });
  });

  describe('样式和主题', () => {
    it('应该在浅色模式下应用正确的背景色', () => {
      render(<Header {...defaultProps} isDarkMode={false} />);

      // 查找具有背景色样式的容器
      const headerContainer = screen.getByText('對白').closest('.transition-colors');
      expect(headerContainer).toBeInTheDocument();
    });

    it('应该在深色模式下应用正确的背景色', () => {
      render(<Header {...defaultProps} isDarkMode={true} />);

      // 查找具有背景色样式的容器
      const headerContainer = screen.getByText('對白').closest('.transition-colors');
      expect(headerContainer).toBeInTheDocument();
    });

    it('应该在浅色模式下应用正确的文字颜色', () => {
      render(<Header {...defaultProps} isDarkMode={false} />);

      const title = screen.getByText('對白');
      expect(title).toHaveStyle('color: rgb(93, 64, 55)');
    });

    it('应该在深色模式下应用正确的文字颜色', () => {
      render(<Header {...defaultProps} isDarkMode={true} />);

      const title = screen.getByText('對白');
      expect(title).toHaveStyle('color: rgb(232, 220, 198)');
    });
  });

  describe('响应式布局', () => {
    it('应该应用正确的padding样式', () => {
      render(<Header {...defaultProps} />);

      const contentContainer = screen.getByText('對白').closest('.flex.items-center.justify-between');
      expect(contentContainer).toHaveStyle('padding-left: 80px');
      expect(contentContainer).toHaveStyle('padding-right: 80px');
    });

    it('应该正确排列标题和logo', () => {
      render(<Header {...defaultProps} />);

      const titleContainer = screen.getByText('對白').closest('.flex.items-center.gap-8');
      expect(titleContainer).toBeInTheDocument();

      const logo = screen.getByAltText('對白');
      expect(titleContainer).toContainElement(logo);
    });

    it('应该正确排列按钮组', () => {
      render(<Header {...defaultProps} />);

      const buttonContainer = screen.getByTitle('沉浸写作模式').closest('.flex.items-center.gap-3');
      expect(buttonContainer).toBeInTheDocument();

      const settingsButton = screen.getByTitle('设置');
      expect(buttonContainer).toContainElement(settingsButton);
    });
  });

  describe('用户状态', () => {
    it('应该根据用户登录状态改变设置按钮行为', () => {
      const { rerender } = render(<Header {...defaultProps} user={null} />);

      let settingsButton = screen.getByTitle('设置');
      fireEvent.click(settingsButton);
      expect(defaultProps.onShowAuth).toHaveBeenCalled();

      vi.clearAllMocks();

      const mockUser = { uid: 'test-user', displayName: 'Test User' };
      rerender(<Header {...defaultProps} user={mockUser} />);

      settingsButton = screen.getByTitle('设置');
      fireEvent.click(settingsButton);
      expect(defaultProps.onShowApiConfig).toHaveBeenCalled();
    });
  });
});
