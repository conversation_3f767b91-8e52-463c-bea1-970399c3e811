import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AppProvider } from '../context/AppContext';
import ChatPage from '../pages/ChatPage';

// Mock all external dependencies
vi.mock('../services/chat/chatResponseService', () => ({
  getChatResponse: vi.fn(),
  translateMessage: vi.fn()
}));

vi.mock('../services/ai/ttsService', () => ({
  ttsService: {
    speak: vi.fn()
  }
}));

vi.mock('../utils/soundUtils', () => ({
  playAIResponseSound: vi.fn()
}));

vi.mock('../utils/idGenerator', () => ({
  generateUniqueId: vi.fn(() => 'mock-id-' + Math.random().toString(36).substr(2, 9))
}));

vi.mock('../services/storage/simpleStorageService', () => ({
  default: {
    saveChatSession: vi.fn()
  }
}));

vi.mock('../services/auth/authService', () => ({
  onAuthChange: vi.fn(() => () => { })
}));

// Mock components
vi.mock('../components/Header', () => ({
  default: ({ onShowApiConfig }) => (
    <div data-testid="header">
      <button onClick={onShowApiConfig} data-testid="settings-btn">Settings</button>
    </div>
  )
}));

vi.mock('../components/ChatHistoryModal', () => ({
  default: ({ isOpen }) => isOpen ? <div data-testid="chat-history">Chat History</div> : null
}));

vi.mock('../components/VoiceInputButton', () => ({
  default: () => <button data-testid="voice-input">Voice Input</button>
}));

vi.mock('../components/VoicePlayButton', () => ({
  default: ({ text }) => <button data-testid="voice-play">{text}</button>
}));

vi.mock('../components/DateTimeWeather', () => ({
  default: () => <div data-testid="datetime">DateTime</div>
}));

vi.mock('../components/DebugPanel', () => ({
  default: () => <div data-testid="debug">Debug</div>
}));

// Test wrapper component
const TestWrapper = ({ children }) => {
  return <AppProvider>{children}</AppProvider>;
};

describe('聊天流程集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  });

  describe('自动翻译模式', () => {
    it('应该完整处理自动翻译聊天流程', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      getChatResponse.mockResolvedValue('Hello there!\n---\n你好！');

      render(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={true}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 1. 发送消息
      const input = screen.getByPlaceholderText('Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)');
      const sendButton = screen.getByLabelText('send');

      fireEvent.change(input, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);

      // 2. 验证API调用
      await waitFor(() => {
        expect(getChatResponse).toHaveBeenCalledWith('Hello', expect.any(Array), null, true, [], undefined);
      });

      // 3. 验证消息显示
      await waitFor(() => {
        expect(screen.getAllByText('Hello')).toHaveLength(2); // 用户消息和AI消息中的Hello
        expect(screen.getByText('Hello there!')).toBeInTheDocument();
      });

      // 4. 验证基本聊天功能正常工作
      expect(getChatResponse).toHaveBeenCalledTimes(1);
    });
  });

  describe('手动翻译模式', () => {
    it('应该完整处理手动翻译聊天流程', async () => {
      const { getChatResponse, translateMessage } = await import('../services/chat/chatResponseService');
      getChatResponse.mockResolvedValue('Hello there!');
      translateMessage.mockResolvedValue('你好！');

      render(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={false}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 1. 发送消息
      const input = screen.getByPlaceholderText('Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)');
      const sendButton = screen.getByLabelText('send');

      fireEvent.change(input, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);

      // 2. 验证API调用（不自动翻译）
      await waitFor(() => {
        expect(getChatResponse).toHaveBeenCalledWith('Hello', expect.any(Array), null, false, [], undefined);
      });

      // 3. 验证消息显示
      await waitFor(() => {
        expect(screen.getAllByText('Hello')).toHaveLength(2); // 用户消息和AI消息中的Hello
        expect(screen.getByText('Hello there!')).toBeInTheDocument();
      });

      // 4. 验证翻译按钮显示
      await waitFor(() => {
        expect(screen.getByTitle('点击翻译')).toBeInTheDocument();
      });

      // 5. 点击翻译按钮
      const translateButton = screen.getByTitle('点击翻译');
      fireEvent.click(translateButton);

      // 6. 验证翻译API调用
      await waitFor(() => {
        expect(translateMessage).toHaveBeenCalledWith('Hello there!');
      });

      // 7. 验证翻译功能被触发
      expect(translateMessage).toHaveBeenCalledTimes(1);
    });
  });

  describe('自动纠错建议模式', () => {
    it('应该自动触发纠错建议', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      getChatResponse.mockResolvedValue('Hello there!\n---\n你好！');

      // Mock expression service
      vi.doMock('../services/ai/expressionService', () => ({
        getExpressionSuggestion: vi.fn().mockResolvedValue('Better expression: Hi there!')
      }));

      render(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={true}
            autoShowSuggestion={true}
          />
        </TestWrapper>
      );

      // 发送消息
      const input = screen.getByPlaceholderText('Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)');
      const sendButton = screen.getByLabelText('send');

      fireEvent.change(input, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);

      // 验证消息显示
      await waitFor(() => {
        expect(screen.getAllByText('Hello')).toHaveLength(2); // 用户消息和AI消息中的Hello
        expect(screen.getByText('Hello there!')).toBeInTheDocument();
      });

      // 验证自动纠错建议被触发（通过检查是否有建议相关的UI元素）
      // 注意：这里需要根据实际的UI实现来调整
    });
  });

  describe('设置切换', () => {
    it('应该能够切换翻译模式', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      getChatResponse.mockResolvedValue('Hello there!');

      const { rerender } = render(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={true}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 发送消息
      const input = screen.getByPlaceholderText('Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)');
      const sendButton = screen.getByLabelText('send');

      fireEvent.change(input, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);

      // 验证自动翻译模式
      await waitFor(() => {
        expect(getChatResponse).toHaveBeenCalledWith('Hello', expect.any(Array), null, true, [], undefined);
      });

      // 切换到手动翻译模式
      rerender(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={false}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 发送新消息
      fireEvent.change(input, { target: { value: 'Hi again' } });
      fireEvent.click(sendButton);

      // 验证手动翻译模式
      await waitFor(() => {
        expect(getChatResponse).toHaveBeenCalledWith('Hi again', expect.any(Array), null, false, [], undefined);
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理API错误', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      getChatResponse.mockRejectedValue(new Error('API Error'));

      render(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={true}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 发送消息
      const input = screen.getByPlaceholderText('Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)');
      const sendButton = screen.getByLabelText('send');

      fireEvent.change(input, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);

      // 验证错误消息显示
      await waitFor(() => {
        expect(screen.getByText(/trouble connecting/i)).toBeInTheDocument();
      });
    });

    it('应该处理翻译错误', async () => {
      const { getChatResponse, translateMessage } = await import('../services/chat/chatResponseService');
      getChatResponse.mockResolvedValue('Hello there!');
      translateMessage.mockRejectedValue(new Error('Translation Error'));

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

      render(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={false}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 发送消息
      const input = screen.getByPlaceholderText('Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)');
      const sendButton = screen.getByLabelText('send');

      fireEvent.change(input, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);

      // 等待消息显示
      await waitFor(() => {
        expect(screen.getByText('Hello there!')).toBeInTheDocument();
      });

      // 点击翻译按钮
      const translateButton = screen.getByTitle('点击翻译');
      fireEvent.click(translateButton);

      // 验证错误被记录
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('翻译失败:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });
  });

  describe('本地存储', () => {
    it('应该保存和恢复聊天会话', async () => {
      const { getChatResponse } = await import('../services/chat/chatResponseService');
      getChatResponse.mockResolvedValue('Hello there!\n---\n你好！');

      const { rerender } = render(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={true}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 发送消息
      const input = screen.getByPlaceholderText('Type your message in English... (按 TAB 开始语音输入，拖拽或粘贴图片)');
      const sendButton = screen.getByLabelText('send');

      fireEvent.change(input, { target: { value: 'Hello' } });
      fireEvent.click(sendButton);

      // 等待消息保存
      await waitFor(() => {
        expect(screen.getAllByText('Hello')).toHaveLength(2);
      });

      // 重新渲染（模拟页面刷新）
      rerender(
        <TestWrapper>
          <ChatPage
            onBackToEditor={vi.fn()}
            onShowApiConfig={vi.fn()}
            isDarkMode={false}
            autoPlayTTS={false}
            aiResponseSound={false}
            autoShowTranslation={true}
            autoShowSuggestion={false}
          />
        </TestWrapper>
      );

      // 验证消息被恢复
      await waitFor(() => {
        expect(screen.getAllByText('Hello')).toHaveLength(2);
        expect(screen.getByText('Hello there!')).toBeInTheDocument();
      });
    });
  });
});
