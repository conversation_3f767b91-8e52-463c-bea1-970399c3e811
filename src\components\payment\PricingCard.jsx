// 价格卡片组件
// 显示订阅计划的价格、功能和购买按钮

import React, { useState } from 'react';
import { Check, Loader2, Crown, Zap } from 'lucide-react';

const PricingCard = ({ 
  plan, 
  title, 
  price, 
  period, 
  description, 
  features, 
  isPopular = false, 
  isCurrentPlan = false,
  onSubscribe,
  loading = false,
  isDarkMode = false 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleSubscribe = () => {
    if (onSubscribe && !isCurrentPlan && !loading) {
      onSubscribe(plan);
    }
  };

  // 主题颜色配置
  const colors = {
    light: {
      background: isPopular ? '#FFFBEB' : '#FAF0E6',
      border: isPopular ? '#D97706' : '#C2410C',
      text: '#5D4037',
      textSecondary: '#8D6E63',
      button: isPopular ? '#D97706' : '#C2410C',
      buttonHover: isPopular ? '#B45309' : '#9F1239',
      buttonText: '#FFFFFF',
      feature: '#166534',
      popular: '#D97706'
    },
    dark: {
      background: isPopular ? '#1A1611' : '#2D2419',
      border: isPopular ? '#D2691E' : '#CD853F',
      text: '#E8DCC6',
      textSecondary: '#D4C4A8',
      button: isPopular ? '#D2691E' : '#CD853F',
      buttonHover: isPopular ? '#B8860B' : '#A0522D',
      buttonText: '#1A1611',
      feature: '#90EE90',
      popular: '#D2691E'
    }
  };

  const theme = isDarkMode ? colors.dark : colors.light;

  return (
    <div
      className={`relative rounded-lg p-6 transition-all duration-300 ${
        isHovered ? 'transform scale-105' : ''
      }`}
      style={{
        backgroundColor: theme.background,
        border: `2px solid ${theme.border}`,
        boxShadow: isHovered 
          ? `0 10px 25px rgba(0, 0, 0, ${isDarkMode ? '0.3' : '0.15'})` 
          : `0 4px 6px rgba(0, 0, 0, ${isDarkMode ? '0.2' : '0.1'})`
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 热门标签 */}
      {isPopular && (
        <div
          className="absolute -top-3 left-1/2 transform -translate-x-1/2 px-4 py-1 rounded-full text-sm font-semibold flex items-center gap-1"
          style={{
            backgroundColor: theme.popular,
            color: theme.buttonText
          }}
        >
          <Crown size={14} />
          最受欢迎
        </div>
      )}

      {/* 当前计划标签 */}
      {isCurrentPlan && (
        <div
          className="absolute -top-3 right-4 px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1"
          style={{
            backgroundColor: theme.feature,
            color: theme.buttonText
          }}
        >
          <Zap size={14} />
          当前计划
        </div>
      )}

      {/* 标题和价格 */}
      <div className="text-center mb-6">
        <h3 
          className="text-xl font-bold mb-2"
          style={{ color: theme.text }}
        >
          {title}
        </h3>
        
        <div className="mb-3">
          <span 
            className="text-3xl font-bold"
            style={{ color: theme.text }}
          >
            ¥{price}
          </span>
          <span 
            className="text-sm ml-1"
            style={{ color: theme.textSecondary }}
          >
            /{period}
          </span>
        </div>
        
        <p 
          className="text-sm"
          style={{ color: theme.textSecondary }}
        >
          {description}
        </p>
      </div>

      {/* 功能列表 */}
      <div className="mb-6">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-3">
              <Check 
                size={16} 
                className="mt-0.5 flex-shrink-0"
                style={{ color: theme.feature }}
              />
              <span 
                className="text-sm"
                style={{ color: theme.text }}
              >
                {feature}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* 订阅按钮 */}
      <button
        onClick={handleSubscribe}
        disabled={isCurrentPlan || loading}
        className={`w-full py-3 px-4 rounded-lg font-semibold text-sm transition-all duration-200 flex items-center justify-center gap-2 ${
          isCurrentPlan || loading 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:transform hover:scale-105'
        }`}
        style={{
          backgroundColor: isCurrentPlan 
            ? theme.textSecondary 
            : (isHovered ? theme.buttonHover : theme.button),
          color: theme.buttonText
        }}
      >
        {loading ? (
          <>
            <Loader2 size={16} className="animate-spin" />
            处理中...
          </>
        ) : isCurrentPlan ? (
          '当前计划'
        ) : (
          `选择${title}`
        )}
      </button>

      {/* 额外信息 */}
      {plan === 'pro_yearly' && (
        <div className="mt-3 text-center">
          <span 
            className="text-xs px-2 py-1 rounded-full"
            style={{
              backgroundColor: theme.feature + '20',
              color: theme.feature
            }}
          >
            💰 年付节省2个月费用
          </span>
        </div>
      )}
    </div>
  );
};

export default PricingCard;
