import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, RefreshCw, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import localCacheService from '../services/cache/localCacheService';

/**
 * 缓存状态指示器组件
 * 显示数据同步状态和网络连接状态
 */
const CacheStatusIndicator = ({ isDarkMode, className = '' }) => {
  const [syncStatus, setSyncStatus] = useState('idle'); // idle, syncing, completed, failed
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastSync, setLastSync] = useState(null);
  const [cacheStats, setCacheStats] = useState(null);

  useEffect(() => {
    // 监听网络状态变化
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 监听同步状态变化
    const handleSyncStatus = (status) => {
      setSyncStatus(status);
      if (status === 'completed') {
        // 同步完成后更新统计信息
        setTimeout(() => {
          updateCacheStats();
          setSyncStatus('idle');
        }, 2000);
      }
    };

    localCacheService.addSyncListener(handleSyncStatus);

    // 初始加载统计信息
    updateCacheStats();

    // 定期更新统计信息
    const statsInterval = setInterval(updateCacheStats, 30000); // 30秒更新一次

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      localCacheService.removeSyncListener(handleSyncStatus);
      clearInterval(statsInterval);
    };
  }, []);

  const updateCacheStats = () => {
    const stats = localCacheService.getCacheStats();
    setCacheStats(stats);
    setLastSync(stats.lastSync);
  };

  const handleManualSync = async () => {
    setSyncStatus('syncing');
    try {
      await localCacheService.triggerBackgroundSync();
    } catch (error) {
      console.error('手动同步失败:', error);
      setSyncStatus('failed');
    }
  };

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff className="w-4 h-4" />;
    }

    switch (syncStatus) {
      case 'syncing':
        return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Wifi className="w-4 h-4" />;
    }
  };

  const getStatusText = () => {
    if (!isOnline) {
      return '离线模式';
    }

    switch (syncStatus) {
      case 'syncing':
        return '同步中...';
      case 'completed':
        return '同步完成';
      case 'failed':
        return '同步失败';
      default:
        return '在线';
    }
  };

  const getStatusColor = () => {
    if (!isOnline) {
      return isDarkMode ? '#EF4444' : '#DC2626';
    }

    switch (syncStatus) {
      case 'syncing':
        return isDarkMode ? '#F59E0B' : '#D97706';
      case 'completed':
        return isDarkMode ? '#10B981' : '#059669';
      case 'failed':
        return isDarkMode ? '#EF4444' : '#DC2626';
      default:
        return isDarkMode ? '#6B7280' : '#9CA3AF';
    }
  };

  const formatLastSync = (type) => {
    const timestamp = lastSync?.[type];
    if (!timestamp) return '从未同步';

    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    return `${days}天前`;
  };

  const getCacheSizeText = () => {
    if (!cacheStats) return '';
    const sizeKB = Math.round(cacheStats.totalSize / 1024);
    return `${sizeKB}KB`;
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* 主状态指示器 */}
      <div
        className="flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-200 cursor-pointer hover:opacity-80"
        style={{
          backgroundColor: isDarkMode ? '#374151' : '#F3F4F6',
          color: getStatusColor()
        }}
        onClick={handleManualSync}
        title={`点击手动同步数据\n缓存大小: ${getCacheSizeText()}\n最后同步: ${formatLastSync('chat_history')}`}
      >
        {getStatusIcon()}
        <span className="text-xs font-medium">{getStatusText()}</span>
      </div>

      {/* 详细状态下拉（可选） */}
      {cacheStats && (
        <div className="relative group">
          <button
            className="p-1 rounded transition-colors duration-200"
            style={{
              color: isDarkMode ? '#9CA3AF' : '#6B7280'
            }}
            title="查看详细同步状态"
          >
            <Clock className="w-4 h-4" />
          </button>
          
          {/* 详细状态面板 */}
          <div
            className="absolute right-0 top-full mt-2 p-3 rounded-lg shadow-lg border transition-all duration-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible z-50"
            style={{
              backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
              borderColor: isDarkMode ? '#374151' : '#E5E7EB',
              color: isDarkMode ? '#F9FAFB' : '#111827',
              minWidth: '200px'
            }}
          >
            <div className="text-xs space-y-2">
              <div className="font-medium mb-2">同步状态详情</div>
              
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>聊天历史:</span>
                  <span>{formatLastSync('chat_history')}</span>
                </div>
                <div className="flex justify-between">
                  <span>分析历史:</span>
                  <span>{formatLastSync('analysis_history')}</span>
                </div>
                <div className="flex justify-between">
                  <span>搜索历史:</span>
                  <span>{formatLastSync('dictionary_search')}</span>
                </div>
                <div className="flex justify-between">
                  <span>写作历史:</span>
                  <span>{formatLastSync('writing_history')}</span>
                </div>
                <div className="flex justify-between">
                  <span>日记历史:</span>
                  <span>{formatLastSync('diary_history')}</span>
                </div>
              </div>
              
              <div className="border-t pt-2 mt-2" style={{ borderColor: isDarkMode ? '#374151' : '#E5E7EB' }}>
                <div className="flex justify-between">
                  <span>缓存大小:</span>
                  <span>{getCacheSizeText()}</span>
                </div>
                <div className="flex justify-between">
                  <span>缓存条目:</span>
                  <span>{cacheStats.itemCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>网络状态:</span>
                  <span className={isOnline ? 'text-green-500' : 'text-red-500'}>
                    {isOnline ? '在线' : '离线'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheStatusIndicator;
