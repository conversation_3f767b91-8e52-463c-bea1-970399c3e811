
import React, { Suspense, useEffect, useState } from 'react';
import DictionaryModal from './components/DictionaryModal';
import VintageApiConfigModal from './components/VintageApiConfigModal';
import { AppProvider, useAppContext } from './context/AppContext';
import EditorPage from './pages/EditorPage';
import HelpPage from './pages/HelpPage';

// 使用React.lazy进行代码分割，延迟加载ChatPage
const ChatPage = React.lazy(() => import('./pages/ChatPage'));

import LoginDemo from './demo/LoginDemo';
import { logout, onAuthChange } from './services/auth/authService';
import { cleanupHistoryService, initHistoryService } from './services/history/initHistoryService';
import simpleStorageService from './services/storage/simpleStorageService';
import './utils/clearCacheData'; // 导入缓存清理工具
import './utils/clearFirebaseData'; // 导入Firebase数据清理工具
import './utils/consoleTests'; // 导入控制台测试工具
import './utils/debugDeleteIssue'; // 导入删除问题调试工具
import './utils/debugFirebaseService'; // 导入Firebase调试工具
import './utils/debugFirebaseSync'; // 导入Firebase同步调试工具
import './utils/fixDuplicateKeys'; // 导入重复键修复工具
import './utils/testAIAnalysisSaving'; // 导入AI写作分析保存功能测试工具
import './utils/testAllHistoryServices'; // 导入所有历史记录服务测试工具
import './utils/testBasicFirebaseSync'; // 导入基本Firebase同步测试工具
import './utils/testChatHistoryDisplay'; // 导入聊天历史显示测试工具
import './utils/testChatSessionSaving'; // 导入聊天会话保存逻辑测试工具
import './utils/testDataRendering'; // 导入数据渲染详细验证测试工具
import './utils/testDeleteFunction'; // 导入删除功能测试工具
import './utils/testFirebaseDataCleaning'; // 导入Firebase数据清理测试工具
import './utils/testFirebaseImport'; // 导入Firebase导入测试工具
import './utils/testFirebaseSync'; // 导入Firebase同步测试工具
import './utils/testFrontendRendering'; // 导入前端组件渲染测试工具
import './utils/testImprovedFirebaseSync'; // 导入改进后的Firebase同步测试工具
import './utils/testSimpleStorage'; // 导入简化存储测试工具
import './utils/testSmartChatTitles'; // 导入智能聊天标题测试工具
import './utils/testUsageMonitoring'; // 导入用量监控测试工具
import './utils/testUsageMonitoringSimple'; // 导入简化版用量监控测试工具
import './utils/usageMonitoringDebug'; // 导入用量监控调试工具

function App() {
  return (
    <AppProvider>
      <MainContent />
    </AppProvider>
  );
}

function MainContent() {
  const { state, dispatch } = useAppContext();
  const { currentPage, isDarkMode, showApiConfig, showDictionary, lookupWord, autoPlayTTS, aiResponseSound, dictionaryService, autoShowTranslation, autoShowSuggestion } = state;

  // 用户认证状态
  const [user, setUser] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // 检查URL参数，如果是help页面则切换到帮助页面
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('page') === 'help') {
      dispatch({ type: 'SET_CURRENT_PAGE', payload: 'help' });
    }
  }, [dispatch]);

  // 监听用户认证状态
  useEffect(() => {
    console.log('🔍 App.jsx: 开始监听认证状态变化...');

    let retryCount = 0;
    const maxRetries = 3;

    const setupAuthListener = () => {
      try {
        const unsubscribe = onAuthChange((user) => {
          console.log('🔐 App.jsx: 认证状态变化:', user ? `用户已登录 (${user.uid})` : '用户未登录');
          if (user) {
            console.log('👤 App.jsx: 用户信息:', {
              uid: user.uid,
              email: user.email,
              emailVerified: user.emailVerified,
              displayName: user.displayName
            });
          }
          setUser(user);
          retryCount = 0; // 重置重试计数
        });
        return unsubscribe;
      } catch (error) {
        console.error('❌ Firebase身份验证初始化失败:', error);
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`🔄 重试身份验证初始化 (${retryCount}/${maxRetries})...`);
          setTimeout(setupAuthListener, 2000 * retryCount); // 指数退避
        } else {
          console.error('❌ 身份验证初始化失败，已达到最大重试次数');
        }
        return () => { }; // 返回空函数
      }
    };

    const unsubscribe = setupAuthListener();
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  // 监听网络状态
  useEffect(() => {
    const handleOnline = () => {
      console.log('🌐 网络已连接');
      setIsOnline(true);
    };

    const handleOffline = () => {
      console.log('🌐 网络已断开');
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 初始化历史记录服务
  useEffect(() => {
    // 初始化历史记录服务，自动监听认证状态变化
    initHistoryService(true);

    // 清理函数
    return () => {
      cleanupHistoryService();
    };
  }, []);

  // 初始化简化存储服务
  useEffect(() => {
    if (user) {
      console.log('🚀 初始化简化存储服务，用户ID:', user.uid);
      simpleStorageService.init(user.uid);
    } else {
      console.log('🚀 用户未登录，简化存储服务等待初始化');
    }
  }, [user]);

  const handleSaveApiKey = (newApiKey) => {
    // API Key现在由系统统一管理，不需要保存
    console.log('设置已保存');
  };

  const handleLogout = async () => {
    try {
      await logout();
      // The onAuthChange listener will handle setting user to null
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // 检查URL参数来决定是否显示演示页面
  const urlParams = new URLSearchParams(window.location.search);
  const showDemo = urlParams.get('demo') === 'login';

  if (showDemo) {
    return <LoginDemo />;
  }

  return (
    <>
      {/* 网络状态提示 */}
      {!isOnline && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-red-500 text-white text-center py-2 px-4">
          <span className="text-sm">⚠️ 网络连接已断开，部分功能可能不可用</span>
        </div>
      )}

      {currentPage === 'editor' ? (
        <EditorPage />
      ) : currentPage === 'help' ? (
        <HelpPage />
      ) : (
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center transition-colors duration-300" style={{
            backgroundColor: isDarkMode ? '#1A1611' : '#F5EFE6'
          }}>
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{
                borderColor: isDarkMode ? '#D2691E' : '#166534'
              }}></div>
              <p className="text-lg" style={{
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}>
                加载聊天页面中...
              </p>
            </div>
          </div>
        }>
          <ChatPage
            onBackToEditor={() => dispatch({ type: 'SET_CURRENT_PAGE', payload: 'editor' })}
            onShowApiConfig={() => dispatch({ type: 'SHOW_API_CONFIG', payload: true })}
            isDarkMode={isDarkMode}
            autoPlayTTS={autoPlayTTS}
            aiResponseSound={aiResponseSound}
            autoShowTranslation={autoShowTranslation}
            autoShowSuggestion={autoShowSuggestion}
          />
        </Suspense>
      )}

      <VintageApiConfigModal
        isOpen={showApiConfig}
        onClose={() => dispatch({ type: 'SHOW_API_CONFIG', payload: false })}
        onSave={handleSaveApiKey}
        isDarkMode={isDarkMode}
        onToggleDarkMode={() => dispatch({ type: 'TOGGLE_DARK_MODE' })}
        autoPlayTTS={autoPlayTTS}
        onToggleAutoPlayTTS={() => dispatch({ type: 'TOGGLE_AUTO_PLAY_TTS' })}
        aiResponseSound={aiResponseSound}
        onToggleAIResponseSound={() => dispatch({ type: 'TOGGLE_AI_RESPONSE_SOUND' })}
        dictionaryService={dictionaryService}
        onDictionaryServiceChange={(service) => dispatch({ type: 'SET_DICTIONARY_SERVICE', payload: service })}
        autoShowTranslation={autoShowTranslation}
        onToggleAutoShowTranslation={() => dispatch({ type: 'TOGGLE_AUTO_SHOW_TRANSLATION' })}
        autoShowSuggestion={autoShowSuggestion}
        onToggleAutoShowSuggestion={() => dispatch({ type: 'TOGGLE_AUTO_SHOW_SUGGESTION' })}
        user={user}
        onLogout={handleLogout}
      />

      <DictionaryModal
        isOpen={showDictionary}
        onClose={() => dispatch({ type: 'SHOW_DICTIONARY', payload: false })}
        word={lookupWord}
        isDarkMode={isDarkMode}
        dictionaryService={dictionaryService}
      />


    </>
  );
}

export default App;
