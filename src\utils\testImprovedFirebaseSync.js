/**
 * 测试改进后的Firebase同步功能
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试改进后的Firebase同步
export const testImprovedFirebaseSync = async () => {
  console.log('🧪 开始测试改进后的Firebase同步功能...');
  
  // 模拟用户登录
  const testUserId = 'test_user_' + Date.now();
  simpleStorageService.init(testUserId);
  
  console.log('👤 测试用户ID:', testUserId);
  
  // 测试1: 保存聊天数据
  console.log('💬 测试1: 保存聊天数据...');
  const testChat = simpleStorageService.saveChatSession([
    { role: 'user', content: 'Hello, this is a test message' },
    { role: 'assistant', content: 'Hi! I received your test message.' }
  ], 'Test Chat Session');
  console.log('✅ 聊天数据保存成功:', testChat.id);
  
  // 测试2: 保存分析数据
  console.log('📝 测试2: 保存分析数据...');
  const testAnalysis = simpleStorageService.saveAnalysis(
    'This is a test text for analysis',
    'Raw analysis result',
    { suggestions: ['suggestion1', 'suggestion2'], score: 85 }
  );
  console.log('✅ 分析数据保存成功:', testAnalysis.id);
  
  // 测试3: 等待2秒后检查同步状态
  console.log('⏳ 测试3: 等待2秒后检查同步状态...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const stats = simpleStorageService.getStats();
  console.log('📊 当前统计:', stats);
  
  // 测试4: 手动同步
  console.log('🔄 测试4: 手动同步到Firebase...');
  try {
    await simpleStorageService.manualSync();
    console.log('✅ 手动同步完成');
  } catch (error) {
    console.log('⚠️ 手动同步失败（可能是网络问题）:', error.message);
  }
  
  // 测试5: 从Firebase同步数据
  console.log('🔄 测试5: 从Firebase同步数据...');
  try {
    const syncedChat = await simpleStorageService.syncFromFirebase('CHAT_HISTORY');
    console.log('✅ 从Firebase同步聊天数据:', syncedChat.length, '条记录');
    
    const syncedAnalysis = await simpleStorageService.syncFromFirebase('ANALYSIS_HISTORY');
    console.log('✅ 从Firebase同步分析数据:', syncedAnalysis.length, '条记录');
  } catch (error) {
    console.log('⚠️ 从Firebase同步失败（可能是网络问题）:', error.message);
  }
  
  // 测试6: 删除数据
  console.log('🗑️ 测试6: 删除聊天数据...');
  const deleteResult = simpleStorageService.deleteChatSession(testChat.id);
  console.log('✅ 删除结果:', deleteResult);
  
  // 测试7: 清空数据
  console.log('🗑️ 测试7: 清空分析数据...');
  const clearResult = simpleStorageService.clearAnalysisHistory();
  console.log('✅ 清空结果:', clearResult);
  
  // 最终统计
  const finalStats = simpleStorageService.getStats();
  console.log('📊 最终统计:', finalStats);
  
  console.log('🎉 改进后的Firebase同步功能测试完成！');
  
  // 清理测试数据
  simpleStorageService.cleanup();
  console.log('🧹 测试数据已清理');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testImprovedFirebaseSync = testImprovedFirebaseSync;
}
