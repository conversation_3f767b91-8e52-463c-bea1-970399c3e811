import React, { useState } from 'react';
import VoiceInputButton from './VoiceInputButton';
import NewDocumentButton from './NewDocumentButton';

/**
 * 编辑器底部控制组件
 * 包含语音输入、新文档等底部按钮和语音转录预览
 */
const EditorBottomControls = ({
  isDarkMode,
  isAnalyzing,
  voiceTranscript,
  onNewDocument,
  onVoiceTranscriptChange,
  onVoiceFinalTranscript
}) => {
  const [showBottomButtons, setShowBottomButtons] = useState(false);

  return (
    <div className="relative">
      {/* 底部按钮组悬停区域 */}
      <div
        className="absolute bottom-0 right-0 z-20 p-4"
        style={{ width: '200px', height: '120px' }}
        onMouseEnter={() => setShowBottomButtons(true)}
        onMouseLeave={() => setShowBottomButtons(false)}
      >
        {/* 底部按钮组 */}
        <div className="absolute bottom-4 right-4 flex items-center gap-2">
          {/* 新文档按钮 */}
          <NewDocumentButton
            onNewDocument={onNewDocument}
            isDarkMode={isDarkMode}
            disabled={isAnalyzing}
            forceVisible={showBottomButtons}
          />

          {/* 语音输入按钮 */}
          <VoiceInputButton
            onTranscriptChange={onVoiceTranscriptChange}
            onFinalTranscript={onVoiceFinalTranscript}
            isDarkMode={isDarkMode}
            disabled={isAnalyzing}
            forceVisible={showBottomButtons}
          />
        </div>
      </div>

      {/* 语音转录预览 */}
      {voiceTranscript && (
        <div
          className="absolute bottom-4 right-24 p-3 rounded-xl text-sm z-15"
          style={{
            backgroundColor: isDarkMode ? '#4A3F35' : '#F0E6D2',
            color: isDarkMode ? '#C4B59A' : '#8B4513',
            border: `1px solid ${isDarkMode ? '#6B5B4F' : '#E6D7B8'}`,
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            fontStyle: 'italic',
            maxHeight: '100px',
            maxWidth: '300px',
            minWidth: '200px',
            overflowY: 'auto'
          }}
        >
          <div className="text-xs mb-1" style={{ color: isDarkMode ? '#D2691E' : '#166534' }}>
            正在倾听...
          </div>
          {voiceTranscript}
        </div>
      )}
    </div>
  );
};

export default EditorBottomControls;
