/**
 * AI翻译服务
 * 专门用于将英文内容翻译为中文
 */
import { getDoubaoApiKey } from '../user/userSettingsService.js';

const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

/**
 * 将英文内容翻译为中文
 * @param {string} englishText - 需要翻译的英文文本
 * @returns {Promise<string>} - 返回中文翻译
 */
export const translateToChinese = async (englishText) => {
    if (!englishText || englishText.trim() === '') {
        throw new Error('英文文本不能为空');
    }

    const apiKey = await getDoubaoApiKey();
    if (!apiKey) {
        throw new Error('系统API密钥未配置，请联系管理员');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages: [
            {
                role: 'system',
                content: `你是一位经验丰富的翻译专家，专门负责将英文内容翻译成地道自然的中文。请将用户提供的英文内容翻译成符合中文表达习惯的流畅文本。

## 翻译原则：
1. **语言自然性**：使用地道的中文表达，避免直译和生硬的翻译
2. **语气保持**：准确传达原文的语气、情感和语调
3. **文化适应**：适当调整表达方式以符合中文文化背景
4. **对话自然**：如果是对话内容，确保翻译后的对话听起来像真实的中文对话
5. **符号保留**：完整保留原文中的表情符号、标点符号和特殊字符
6. **简洁明了**：避免冗长的表达，保持简洁有力

## 翻译技巧：
- 将英文的被动语态转换为中文的主动语态
- 适当调整语序以符合中文表达习惯
- 使用中文常用的连接词和语气词
- 根据语境选择最合适的词汇
- 保持原文的幽默感和亲切感

## 翻译示例：
英文："That sounds really interesting! What got you started with that?"
中文："听起来很有意思！你是怎么开始接触这个的？"

英文："I totally get that feeling! When I'm photographing rare plants, time just disappears."
中文："我完全理解那种感觉！当我拍摄珍稀植物时，时间仿佛都消失了。"

英文："Wow, that's amazing! How did you discover that?"
中文："哇，太棒了！你是怎么发现的？"

## 输出要求：
- 只输出翻译结果，不要添加任何解释、说明或标签
- 保持原文的段落结构和格式
- 确保翻译准确且自然流畅
- 让翻译后的文本读起来像母语者写的`
            },
            {
                role: 'user',
                content: englishText
            }
        ],
        temperature: 0.7,
        max_tokens: 1500,
        thinking: { type: "disabled" }
    };

    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers,
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            const errorBody = await response.text();
            throw new Error(`翻译API请求失败: ${response.status} ${errorBody}`);
        }

        const result = await response.json();
        const translation = result.choices[0].message.content.trim();

        console.log('翻译成功:', { englishText: englishText.substring(0, 100) + '...', translation: translation.substring(0, 100) + '...' });

        return translation;
    } catch (error) {
        console.error('翻译失败:', error);
        throw error;
    }
};
