import { Play, Square, VolumeX } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { ttsService } from '../services/ai/ttsService';

const VoicePlayButton = ({
  text,
  isDarkMode,
  size = 'small',
  className = '',
  onPlayStart = null,
  onPlayEnd = null,
  onError = null
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 检查TTS支持
  const [isSupported, setIsSupported] = useState(ttsService.status.isSupported);

  useEffect(() => {
    setIsSupported(ttsService.status.isSupported);
  }, []);

  // 处理播放/停止
  const handlePlayStop = async () => {
    console.log('🎵 播放按钮点击，当前状态:', { isPlaying, isLoading });

    if (!isSupported) {
      const errorMsg = '您的浏览器不支持语音播放功能';
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (!text || !text.trim()) {
      const errorMsg = '没有可播放的文本内容';
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    // 如果正在播放，则停止
    if (isPlaying) {
      console.log('🛑 停止播放');
      ttsService.stop();
      setIsPlaying(false);
      setIsLoading(false);
      onPlayEnd?.(text);
      return;
    }

    // 开始播放
    console.log('▶️ 开始播放');
    setIsLoading(true);
    setError(null);
    onPlayStart?.(text);

    try {
      // 先停止其他正在播放的语音
      if (ttsService.status.isPlaying) {
        ttsService.stop();
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 设置播放状态
      console.log('🎯 设置播放状态为true');
      setIsLoading(false);
      setIsPlaying(true);

      // 开始播放
      await ttsService.speak(text, {
        lang: 'en-US',
        rate: 0.9,
        pitch: 1.0,
        volume: 1.0
      });

      // 播放完成
      console.log('🏁 播放完成');
      setIsPlaying(false);
      onPlayEnd?.(text);

    } catch (error) {
      console.error('TTS播放失败:', error);

      if (error.message === '用户主动停止播放') {
        console.log('✅ 用户主动停止');
        setIsPlaying(false);
        setIsLoading(false);
      } else {
        const errorMsg = error.message || '语音播放失败';
        setError(errorMsg);
        setIsPlaying(false);
        setIsLoading(false);
        onError?.(errorMsg);
      }
    }
  };

  // 监听TTS状态变化
  useEffect(() => {
    const handleStatusChange = (status) => {
      // 当TTS完全停止时，重置播放状态
      if (status.isStopped && isPlaying) {
        console.log('🔄 TTS已停止，重置组件状态');
        setIsPlaying(false);
        setIsLoading(false);
      }
    };

    ttsService.addStatusListener(handleStatusChange);
    return () => ttsService.removeStatusListener(handleStatusChange);
  }, [isPlaying]);

  // 样式配置
  const sizeConfig = {
    small: {
      button: 'w-8 h-8',
      icon: 'w-4 h-4'
    },
    medium: {
      button: 'w-10 h-10',
      icon: 'w-5 h-5'
    },
    large: {
      button: 'w-12 h-12',
      icon: 'w-6 h-6'
    }
  };

  const config = sizeConfig[size] || sizeConfig.small;

  // 获取当前图标
  const getCurrentIcon = () => {
    if (isLoading) {
      return (
        <div
          className="animate-spin rounded-full border-2 border-current border-t-transparent"
          style={{ width: '16px', height: '16px' }}
        />
      );
    }

    if (!isSupported) {
      return <VolumeX className={config.icon} />;
    }

    if (isPlaying) {
      return <Square className={config.icon} />;
    }

    return <Play className={config.icon} />;
  };

  // 获取按钮标题
  const getButtonTitle = () => {
    if (!isSupported) return '浏览器不支持语音播放';
    if (error) return `错误: ${error}`;
    if (isLoading) return '加载中...';
    if (isPlaying) return '点击停止播放';
    return '点击播放语音';
  };

  // 获取按钮CSS类名
  const getButtonClassName = () => {
    let className = `voice-play-btn ${config.button}`;

    if (!isSupported || error) {
      className += ' unsupported';
    } else if (isPlaying) {
      className += ' playing';
    } else {
      className += ' default';
    }

    return className;
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* 主播放按钮 */}
      <button
        onClick={handlePlayStop}
        disabled={isLoading || !isSupported}
        className={getButtonClassName()}
        title={getButtonTitle()}
      >
        {getCurrentIcon()}
      </button>





      {/* 错误提示 */}
      {error && size !== 'small' && (
        <span
          className="text-xs ml-2"
          style={{ color: isDarkMode ? '#D2691E' : '#B91C1C' }}
          title={error}
        >
          ⚠️
        </span>
      )}
    </div>
  );
};

export default VoicePlayButton;
