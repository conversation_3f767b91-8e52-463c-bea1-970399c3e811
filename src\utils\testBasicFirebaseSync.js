/**
 * 测试基本的Firebase同步功能
 * 从最简单的写入开始测试
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试基本Firebase写入
export const testBasicFirebaseWrite = async () => {
  console.log('🧪 开始测试基本Firebase写入同步...');
  
  // 模拟用户登录
  const testUserId = 'test_user_' + Date.now();
  console.log('👤 测试用户ID:', testUserId);
  
  // 初始化服务
  simpleStorageService.init(testUserId);
  
  // 测试1: 保存聊天数据到本地
  console.log('📝 测试1: 保存聊天数据到本地...');
  const testChat = {
    id: 'test_chat_' + Date.now(),
    messages: [
      { role: 'user', content: 'Hello, this is a test message' },
      { role: 'assistant', content: 'Hi! This is a test response.' }
    ],
    sessionTitle: 'Test Chat Session',
    timestamp: new Date().toISOString(),
    userId: testUserId
  };
  
  const savedChat = simpleStorageService.saveChatSession(testChat);
  console.log('✅ 聊天数据已保存到本地:', savedChat.id);
  
  // 测试2: 检查同步队列
  console.log('📋 测试2: 检查同步队列...');
  const stats = simpleStorageService.getStats();
  console.log('📊 当前统计:', stats);
  
  // 测试3: 手动触发同步到Firebase
  console.log('🔄 测试3: 手动同步到Firebase...');
  try {
    await simpleStorageService.manualSync();
    console.log('✅ Firebase同步完成');
  } catch (error) {
    console.error('❌ Firebase同步失败:', error);
    console.log('💡 可能的原因:');
    console.log('   - Firebase配置问题');
    console.log('   - 网络连接问题');
    console.log('   - Firebase权限问题');
    return false;
  }
  
  // 测试4: 验证数据是否真的同步到了Firebase
  console.log('🔍 测试4: 从Firebase读取数据验证...');
  try {
    const syncedData = await simpleStorageService.syncFromFirebase('CHAT_HISTORY');
    console.log('✅ 从Firebase读取到数据:', syncedData.length, '条记录');
    
    if (syncedData.length > 0) {
      console.log('📄 最新记录:', syncedData[0]);
      console.log('🎉 Firebase同步验证成功！');
      return true;
    } else {
      console.log('⚠️ Firebase中没有找到数据');
      return false;
    }
  } catch (error) {
    console.error('❌ 从Firebase读取失败:', error);
    return false;
  }
};

// 测试Firebase配置
export const testFirebaseConfig = () => {
  console.log('🔧 测试Firebase配置...');
  
  try {
    // 检查Firebase配置
    const { db } = require('../config/firebaseConfig');
    console.log('✅ Firebase数据库连接正常');
    
    // 检查Firebase导入
    const { collection, addDoc } = require('firebase/firestore');
    console.log('✅ Firebase Firestore函数导入正常');
    
    return true;
  } catch (error) {
    console.error('❌ Firebase配置问题:', error);
    return false;
  }
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testBasicFirebaseWrite = testBasicFirebaseWrite;
  window.testFirebaseConfig = testFirebaseConfig;
}
