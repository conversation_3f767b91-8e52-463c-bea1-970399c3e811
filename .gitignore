# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Temporary debug/test files
*-debug.html
*-fix.html
*-test.html
debug-*.html
test-*.html

# Local Netlify folder
.netlify

# Test coverage reports
coverage/

# Test output and cache
test-results/
.vitest/

# Environment variables
.env
.env.*
!/.env.example
netlify.env

# 编辑.gitignore文件，在第50行之前添加：
!package.json
!package-lock.json
# Sensitive credentials
*.json
english-assistant-*.json

# ECDICT source data (use public/data/dictionary/ for deployment)
ECDICT/
