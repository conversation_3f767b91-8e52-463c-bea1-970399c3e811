"use strict";function _createForOfIteratorHelper(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw i}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function eudic_onMacmillanResultLexClick(e){if(e.target.classList){var t=e.target,n=t.classList.contains("toggle-open")||t.classList.contains("toggle-close");if(!n)for(var r=t;r;r=r.parentElement)if(r.classList.contains("toggle-toggle")){n=!0;break}if(n)for(var o=t;o;o=o.parentElement)if(o.classList.contains("toggleable")){o.classList.toggle("closed"),e.preventDefault(),e.stopPropagation();break}}}function eudic_onlineDictPlugin_getParameterByName(e,t){e=e.replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&]"+e+"(=([^&#]*)|&|#|$)").exec(t);return n?n[2]?decodeURIComponent(n[2].replace(/\+/g," ")):"":null}function eudic_onlineDictPlugin_onloadFinish(){var e=eudic_onlineDictPlugin_getParameterByName("id",document.currentScript.src),t=document.getElementById("eudic-onlinedict-section-"+e);if(t){var n=t.querySelectorAll("[eudic-onlinedict-custom-onclick]");if(n&&n.length>0){var r,o=_createForOfIteratorHelper(n);try{for(o.s();!(r=o.n()).done;){var i=r.value,a=i.getAttribute("eudic-onlinedict-custom-onclick"),c=window[a];"function"==typeof c&&(i.ontouchend=c)}}catch(e){o.e(e)}finally{o.f()}}}}eudic_onlineDictPlugin_onloadFinish();