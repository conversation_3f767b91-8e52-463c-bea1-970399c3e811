/**
 * 用量监控功能测试工具
 * 测试API使用量跟踪和限制功能
 */

import { onAuthChange } from '../services/auth/authService';
import {
    checkApiUsageLimit,
    getSystemConfig,
    getUserSettings,
    recordApiUsage,
    updateUserSettings
} from '../services/user/userSettingsService';

/**
 * 测试系统配置获取
 */
export const testSystemConfig = async () => {
  console.log('🧪 测试系统配置获取...');

  try {
    const config = await getSystemConfig();
    console.log('✅ 系统配置获取成功:', {
      hasApiKey: !!config.doubaoApiKey,
      freeUserLimits: config.freeUserLimits,
      paidUserLimits: config.paidUserLimits,
      enablePaymentSystem: config.enablePaymentSystem
    });

    // 检查新的分类限制结构
    const hasValidConfig = config.doubaoApiKey &&
                          config.freeUserLimits &&
                          config.freeUserLimits.chatRequestsPerDay &&
                          config.freeUserLimits.writingAnalysisPerDay &&
                          config.paidUserLimits &&
                          config.paidUserLimits.basicPlan;

    if (hasValidConfig) {
      console.log('✅ 系统配置结构验证通过:', {
        apiKey: '已配置',
        freeChat: config.freeUserLimits.chatRequestsPerDay + '/天',
        freeWriting: config.freeUserLimits.writingAnalysisPerDay + '/天',
        paidPlan: config.paidUserLimits.basicPlan.price + '元/月'
      });
      return true;
    } else {
      console.log('❌ 系统配置结构不完整:', {
        hasApiKey: !!config.doubaoApiKey,
        hasFreeUserLimits: !!config.freeUserLimits,
        hasPaidUserLimits: !!config.paidUserLimits
      });
      return false;
    }
  } catch (error) {
    console.error('❌ 系统配置获取失败:', error);
    return false;
  }
};

/**
 * 测试用户设置获取
 */
export const testUserSettings = async (userId) => {
  console.log('🧪 测试用户设置获取...');

  if (!userId) {
    console.log('❌ 用户未登录，无法测试用户设置');
    return false;
  }

  try {
    const settings = await getUserSettings(userId);
    console.log('✅ 用户设置获取成功:', {
      requestsUsedToday: settings.requestsUsedToday,
      lastRequestDate: settings.lastRequestDate,
      isPremiumUser: settings.isPremiumUser,
      theme: settings.theme
    });

    return true;
  } catch (error) {
    console.error('❌ 用户设置获取失败:', error);
    return false;
  }
};

/**
 * 测试使用量检查
 */
export const testUsageCheck = async (userId) => {
  console.log('🧪 测试分类使用量检查...');

  if (!userId) {
    console.log('❌ 用户未登录，无法测试使用量检查');
    return false;
  }

  try {
    const [chatUsage, writingUsage] = await Promise.all([
      checkApiUsageLimit(userId, 'chat'),
      checkApiUsageLimit(userId, 'writing')
    ]);

    console.log('✅ AI对话使用量检查成功:', {
      canUse: chatUsage.canUse,
      remainingRequests: chatUsage.remainingRequests,
      maxRequests: chatUsage.maxRequests,
      currentUsage: chatUsage.currentUsage,
      isPaidUser: chatUsage.isPaidUser
    });

    console.log('✅ 写作纠错使用量检查成功:', {
      canUse: writingUsage.canUse,
      remainingRequests: writingUsage.remainingRequests,
      maxRequests: writingUsage.maxRequests,
      currentUsage: writingUsage.currentUsage,
      isPaidUser: writingUsage.isPaidUser
    });

    return true;
  } catch (error) {
    console.error('❌ 使用量检查失败:', error);
    return false;
  }
};

/**
 * 测试使用量记录
 */
export const testUsageRecord = async (userId) => {
  console.log('🧪 测试分类使用量记录...');

  if (!userId) {
    console.log('❌ 用户未登录，无法测试使用量记录');
    return false;
  }

  try {
    // 测试AI对话使用量记录
    console.log('📝 测试AI对话使用量记录...');
    const beforeChatUsage = await checkApiUsageLimit(userId, 'chat');
    console.log('📊 记录前AI对话使用量:', beforeChatUsage.currentUsage);

    await recordApiUsage(userId, 'chat');
    console.log('✅ AI对话使用量记录成功');

    const afterChatUsage = await checkApiUsageLimit(userId, 'chat');
    console.log('📊 记录后AI对话使用量:', afterChatUsage.currentUsage);

    // 测试写作纠错使用量记录
    console.log('📝 测试写作纠错使用量记录...');
    const beforeWritingUsage = await checkApiUsageLimit(userId, 'writing');
    console.log('📊 记录前写作纠错使用量:', beforeWritingUsage.currentUsage);

    await recordApiUsage(userId, 'writing');
    console.log('✅ 写作纠错使用量记录成功');

    const afterWritingUsage = await checkApiUsageLimit(userId, 'writing');
    console.log('📊 记录后写作纠错使用量:', afterWritingUsage.currentUsage);

    // 验证使用量是否正确增加
    const chatIncreased = afterChatUsage.currentUsage - beforeChatUsage.currentUsage === 1;
    const writingIncreased = afterWritingUsage.currentUsage - beforeWritingUsage.currentUsage === 1;

    if (chatIncreased && writingIncreased) {
      console.log('✅ 分类使用量记录验证成功');
      return true;
    } else {
      console.log('❌ 分类使用量记录验证失败', { chatIncreased, writingIncreased });
      return false;
    }
  } catch (error) {
    console.error('❌ 使用量记录失败:', error);
    return false;
  }
};

/**
 * 测试使用量限制
 */
export const testUsageLimit = async (userId) => {
  console.log('🧪 测试使用量限制...');

  if (!userId) {
    console.log('❌ 用户未登录，无法测试使用量限制');
    return false;
  }

  try {
    const settings = await getUserSettings(userId);
    const systemConfig = await getSystemConfig();

    // 获取用户的最大请求数
    const subscription = settings.subscription || { plan: 'free', status: 'active' };
    const isPaidUser = subscription.plan === 'basic' && subscription.status === 'active';
    const limits = isPaidUser
      ? systemConfig.paidUserLimits?.basicPlan
      : systemConfig.freeUserLimits;

    const chatMaxRequests = limits?.chatRequestsPerDay || 10;
    const writingMaxRequests = limits?.writingAnalysisPerDay || 5;

    console.log('📊 用户限制信息:', {
      isPaidUser,
      chatMaxRequests,
      writingMaxRequests,
      plan: subscription.plan
    });

    const today = new Date().toISOString().split('T')[0];

    // 如果是付费用户，跳过限制测试
    if (isPaidUser) {
      console.log('✅ 付费用户无限制，跳过限制测试');
      return true;
    }

    // 测试AI对话限制
    console.log('📝 测试AI对话使用量限制...');
    await updateUserSettings(userId, {
      usageToday: {
        chatRequests: chatMaxRequests - 1,
        writingAnalysis: 0,
        lastRequestDate: today
      },
      requestsUsedToday: chatMaxRequests - 1,
      lastRequestDate: today
    });

    // 检查AI对话是否还能使用
    const chatUsageCheck1 = await checkApiUsageLimit(userId, 'chat');
    console.log('📊 AI对话接近限制时的检查结果:', {
      canUse: chatUsageCheck1.canUse,
      remainingRequests: chatUsageCheck1.remainingRequests,
      currentUsage: chatUsageCheck1.currentUsage
    });

    if (!chatUsageCheck1.canUse || chatUsageCheck1.remainingRequests !== 1) {
      console.log('❌ AI对话接近限制时的检查结果不正确');
      return false;
    }

    // 再增加一次AI对话使用，应该达到限制
    await recordApiUsage(userId, 'chat');
    const chatUsageCheck2 = await checkApiUsageLimit(userId, 'chat');
    console.log('📊 AI对话达到限制时的检查结果:', {
      canUse: chatUsageCheck2.canUse,
      remainingRequests: chatUsageCheck2.remainingRequests,
      currentUsage: chatUsageCheck2.currentUsage
    });

    if (chatUsageCheck2.canUse || chatUsageCheck2.remainingRequests !== 0) {
      console.log('❌ AI对话达到限制时的检查结果不正确');
      return false;
    }

    console.log('✅ 使用量限制测试成功');

    // 恢复原始使用量
    console.log('🔄 恢复原始使用量...');
    await updateUserSettings(userId, {
      usageToday: settings.usageToday || {
        chatRequests: 0,
        writingAnalysis: 0,
        lastRequestDate: today
      },
      requestsUsedToday: settings.requestsUsedToday || 0,
      lastRequestDate: settings.lastRequestDate || today
    });

    return true;
  } catch (error) {
    console.error('❌ 使用量限制测试失败:', error);
    return false;
  }
};

/**
 * 测试日期重置功能
 */
export const testDateReset = async (userId) => {
  console.log('🧪 测试日期重置功能...');

  if (!userId) {
    console.log('❌ 用户未登录，无法测试日期重置');
    return false;
  }

  try {
    const settings = await getUserSettings(userId);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];
    const today = new Date().toISOString().split('T')[0];

    // 设置昨天的使用量（同时更新新旧两个字段）
    console.log('📝 设置昨天的使用量...');
    await updateUserSettings(userId, {
      // 新的分类使用量系统
      usageToday: {
        chatRequests: 8,
        writingAnalysis: 3,
        lastRequestDate: yesterdayStr
      },
      // 兼容旧版本字段
      requestsUsedToday: 50,
      lastRequestDate: yesterdayStr
    });

    // 检查今天的AI对话使用量是否重置
    const chatUsageCheck = await checkApiUsageLimit(userId, 'chat');
    console.log('📊 AI对话日期重置后的检查结果:', {
      canUse: chatUsageCheck.canUse,
      currentUsage: chatUsageCheck.currentUsage,
      maxRequests: chatUsageCheck.maxRequests,
      apiType: 'chat'
    });

    // 检查今天的写作纠错使用量是否重置
    const writingUsageCheck = await checkApiUsageLimit(userId, 'writing');
    console.log('📊 写作纠错日期重置后的检查结果:', {
      canUse: writingUsageCheck.canUse,
      currentUsage: writingUsageCheck.currentUsage,
      maxRequests: writingUsageCheck.maxRequests,
      apiType: 'writing'
    });

    // 验证两种类型的使用量都重置为0
    if (chatUsageCheck.currentUsage === 0 && writingUsageCheck.currentUsage === 0) {
      console.log('✅ 日期重置功能测试成功 - 所有使用量都已重置');

      // 恢复原始设置
      await updateUserSettings(userId, {
        usageToday: settings.usageToday || {
          chatRequests: 0,
          writingAnalysis: 0,
          lastRequestDate: today
        },
        requestsUsedToday: settings.requestsUsedToday || 0,
        lastRequestDate: settings.lastRequestDate || today
      });

      return true;
    } else {
      console.log('❌ 日期重置功能测试失败 - 使用量未正确重置');
      console.log('   - AI对话使用量:', chatUsageCheck.currentUsage, '(应为0)');
      console.log('   - 写作纠错使用量:', writingUsageCheck.currentUsage, '(应为0)');
      return false;
    }
  } catch (error) {
    console.error('❌ 日期重置功能测试失败:', error);
    return false;
  }
};

/**
 * 获取当前用户ID
 */
export const getCurrentUserId = () => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthChange((user) => {
      unsubscribe();
      resolve(user ? user.uid : null);
    });
  });
};

/**
 * 运行所有用量监控测试
 */
export const runAllUsageTests = async () => {
  console.log('🚀 开始用量监控功能完整测试...');
  console.log('='.repeat(60));

  const results = {
    systemConfig: false,
    userSettings: false,
    usageCheck: false,
    usageRecord: false,
    usageLimit: false,
    dateReset: false
  };

  // 获取当前用户ID
  const userId = await getCurrentUserId();
  if (!userId) {
    console.log('❌ 用户未登录，请先登录后再运行测试');
    return results;
  }

  console.log('👤 当前用户ID:', userId);
  console.log('');

  // 运行各项测试
  results.systemConfig = await testSystemConfig();
  console.log('');

  results.userSettings = await testUserSettings(userId);
  console.log('');

  results.usageCheck = await testUsageCheck(userId);
  console.log('');

  results.usageRecord = await testUsageRecord(userId);
  console.log('');

  results.usageLimit = await testUsageLimit(userId);
  console.log('');

  results.dateReset = await testDateReset(userId);
  console.log('');

  // 输出测试总结
  console.log('📊 测试结果总结:');
  console.log('='.repeat(60));
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? '通过' : '失败'}`);
  });

  console.log('');
  console.log(`🎯 测试通过率: ${passedTests}/${totalTests} (${((passedTests/totalTests)*100).toFixed(1)}%)`);

  if (passedTests === totalTests) {
    console.log('🎉 所有用量监控功能测试通过！');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }

  return results;
};

// 导出到全局对象，方便在控制台调用
if (typeof window !== 'undefined') {
  window.usageMonitoringTest = {
    runAllTests: runAllUsageTests,
    testSystemConfig,
    testUserSettings,
    testUsageCheck,
    testUsageRecord,
    testUsageLimit,
    testDateReset,
    getCurrentUserId
  };

  // 同时暴露服务函数供外部测试页面使用
  window.userSettingsService = {
    checkApiUsageLimit,
    recordApiUsage,
    getUserSettings,
    updateUserSettings,
    getSystemConfig
  };

  console.log('🧪 用量监控测试工具已加载到 window.usageMonitoringTest');
  console.log('🔧 用户设置服务已暴露到 window.userSettingsService');
}
