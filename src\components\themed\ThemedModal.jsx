import React from 'react';
import { X } from 'lucide-react';
import ThemedButton from './ThemedButton';

const ThemedModal = ({
  isOpen,
  onClose,
  title,
  children,
  className = '',
  maxWidth = 'md',
  isDarkMode = false,
  ...props
}) => {

  if (!isOpen) return null;

  // 最大宽度样式
  const maxWidthStyles = {
    sm: { maxWidth: '400px' },
    md: { maxWidth: '500px' },
    lg: { maxWidth: '600px' },
    xl: { maxWidth: '800px' },
  };

  // 背景遮罩样式
  const backdropStyles = {
    position: 'fixed',
    inset: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: '50',
    backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.7)' : 'rgba(93, 64, 55, 0.5)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    backdropFilter: 'blur(8px)',
    WebkitBackdropFilter: 'blur(8px)',
  };

  // 模态框内容样式
  const contentStyles = {
    backgroundColor: isDarkMode ? '#2A241D' : '#FEFCF5',
    color: 'var(--color-text-primary)',
    border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.08)' : '1px solid rgba(93, 64, 55, 0.12)',
    borderRadius: '16px',
    width: '100%',
    margin: '20px',
    boxShadow: isDarkMode
      ? '0 32px 64px -12px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.08)'
      : '0 32px 64px -12px rgba(93, 64, 55, 0.3), 0 0 0 1px rgba(93, 64, 55, 0.12)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    overflow: 'hidden',
    transform: 'scale(1)',
    animation: 'modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    ...maxWidthStyles[maxWidth],
  };

  // 头部样式
  const headerStyles = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '32px 32px 24px 32px',
  };

  // 标题样式
  const titleStyles = {
    fontSize: '20px',
    fontWeight: '600',
    color: 'var(--color-text-primary)',
    fontFamily: 'Georgia, "Noto Serif SC", serif',
    letterSpacing: '0.05em',
    margin: '0',
  };

  // 内容样式
  const bodyStyles = {
    padding: '0 32px 32px 32px',
  };

  // 处理背景点击
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <>
      <style jsx>{`
        @keyframes modalSlideIn {
          0% {
            opacity: 0;
            transform: scale(0.95) translateY(-20px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>

      <div
        style={backdropStyles}
        onClick={handleBackdropClick}
        {...props}
      >
        <div
          className={`themed-modal ${className}`}
          style={contentStyles}
        >
          {/* 头部 - 只在有标题时显示 */}
          {title && (
            <div style={headerStyles}>
              <h3 style={titleStyles}>{title}</h3>
              <ThemedButton
                variant="ghost"
                size="icon"
                onClick={onClose}
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  transition: 'all 0.2s ease'
                }}
              >
                <X size={20} />
              </ThemedButton>
            </div>
          )}

          {/* 关闭按钮 - 当没有标题时显示在右上角 */}
          {!title && (
            <div style={{
              position: 'absolute',
              top: '16px',
              right: '16px',
              zIndex: 10
            }}>
              <ThemedButton
                variant="ghost"
                size="icon"
                onClick={onClose}
                style={{
                  width: '36px',
                  height: '36px',
                  borderRadius: '8px',
                  backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(93, 64, 55, 0.1)',
                  transition: 'all 0.2s ease'
                }}
              >
                <X size={18} />
              </ThemedButton>
            </div>
          )}

          {/* 内容 */}
          <div style={title ? bodyStyles : { padding: '0' }}>
            {children}
          </div>
        </div>
      </div>
    </>
  );
};

export default ThemedModal;