[build]
  # 构建命令
  command = "npm run build"
  # 发布目录
  publish = "dist"
  # 基础目录（确保在项目根目录构建）
  base = "."

[build.environment]
  # 设置Node.js版本
  NODE_VERSION = "18"
  # 设置npm版本
  NPM_VERSION = "9"

# 重定向规则（用于SPA路由）
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 头部设置
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# JavaScript文件MIME类型设置
[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.jsx"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.mjs"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

# 开发环境特殊MIME类型设置
[[headers]]
  for = "/src/*"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "/@vite/*"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "/@react-refresh/*"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

# 开发服务器设置
[dev]
  # 开发服务器端口（Netlify代理端口）
  port = 3000
  # 目标端口（Vite实际运行的端口）
  targetPort = 5173
  # 开发服务器命令
  command = "npm run dev"
  # 发布目录（开发时）
  publish = "dist"
  # 自动重载
  autoLaunch = true
  # 框架检测
  framework = "vite"

# 函数设置（如果有的话）
[functions]
  directory = "netlify/functions"

# 插件设置
[[plugins]]
  package = "@netlify/plugin-lighthouse"
