// 清洁释义测试工具
// 用于测试移除多余词性标签后的效果

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试清洁释义效果
const testCleanDefinitions = async () => {
  console.log('🧪 测试清洁释义效果...');
  console.log('='.repeat(50));
  
  const testWords = ['water', 'doing', 'nothing', 'school'];
  const results = {};
  
  for (const word of testWords) {
    try {
      console.log(`\n📖 测试单词: ${word}`);
      console.log('-'.repeat(30));
      
      const result = await getWordDetails(word, 'ecdict');
      
      if (result && !result.notFound) {
        console.log(`✅ ${word} 查询成功`);
        console.log(`📊 数据源: ${result.source}`);
        
        if (result.definitionGroups && result.definitionGroups.length > 0) {
          console.log(`🔗 分组数量: ${result.definitionGroups.length}`);
          
          result.definitionGroups.forEach((group, groupIndex) => {
            console.log(`\n  ${groupIndex + 1}. 词性分组:`);
            console.log(`     🏷️  词性: ${group.pos} (${group.posName})`);
            console.log(`     🇺🇸 英文释义数量: ${group.english.length}`);
            console.log(`     🇨🇳 中文释义数量: ${group.chinese.length}`);
            
            // 检查英文释义是否还有词性标签
            const hasPosInEnglish = group.english.some(def => /^[a-z]+\.?\s+/.test(def));
            if (hasPosInEnglish) {
              console.log(`     ❌ 英文释义中仍有词性标签`);
              group.english.forEach((def, defIndex) => {
                if (/^[a-z]+\.?\s+/.test(def)) {
                  console.log(`       ${defIndex + 1}. ${def} ← 包含词性标签`);
                }
              });
            } else {
              console.log(`     ✅ 英文释义中无词性标签`);
            }
            
            // 检查中文释义是否还有词性标签
            const hasPosInChinese = group.chinese.some(def => /^[a-z]+\.?\s+/.test(def));
            if (hasPosInChinese) {
              console.log(`     ❌ 中文释义中仍有词性标签`);
              group.chinese.forEach((def, defIndex) => {
                if (/^[a-z]+\.?\s+/.test(def)) {
                  console.log(`       ${defIndex + 1}. ${def} ← 包含词性标签`);
                }
              });
            } else {
              console.log(`     ✅ 中文释义中无词性标签`);
            }
            
            // 显示清洁后的释义
            if (group.english.length > 0) {
              console.log(`     📝 英文释义:`);
              group.english.forEach((def, defIndex) => {
                console.log(`       ${defIndex + 1}. ${def}`);
              });
            }
            
            if (group.chinese.length > 0) {
              console.log(`     📝 中文释义:`);
              group.chinese.forEach((def, defIndex) => {
                console.log(`       ${defIndex + 1}. ${def}`);
              });
            }
          });
          
          results[word] = {
            success: true,
            groupsCount: result.definitionGroups.length,
            hasGroups: true,
            groups: result.definitionGroups
          };
        } else {
          console.log('❌ 没有分组释义数据');
          
          results[word] = {
            success: true,
            groupsCount: 0,
            hasGroups: false
          };
        }
      } else {
        console.log(`❌ ${word} 查询失败`);
        console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
        
        results[word] = {
          success: false,
          error: result?.error || '未找到该单词'
        };
      }
    } catch (error) {
      console.log(`❌ ${word} 查询出错: ${error.message}`);
      results[word] = {
        success: false,
        error: error.message
      };
    }
  }
  
  // 统计结果
  console.log('\n📊 清洁释义统计:');
  console.log('='.repeat(50));
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const groupsCount = Object.values(results).filter(r => r.hasGroups).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`✅ 成功查询: ${successCount}/${totalCount}`);
  console.log(`🔗 有分组释义: ${groupsCount}/${totalCount}`);
  
  // 检查清洁效果
  let totalGroups = 0;
  let cleanGroups = 0;
  
  Object.entries(results).forEach(([word, result]) => {
    if (result.success && result.hasGroups) {
      result.groups.forEach(group => {
        totalGroups++;
        const hasPosInEnglish = group.english.some(def => /^[a-z]+\.?\s+/.test(def));
        const hasPosInChinese = group.chinese.some(def => /^[a-z]+\.?\s+/.test(def));
        
        if (!hasPosInEnglish && !hasPosInChinese) {
          cleanGroups++;
        }
      });
    }
  });
  
  if (totalGroups > 0) {
    const cleanRate = (cleanGroups / totalGroups * 100).toFixed(1);
    console.log(`🎯 清洁效果: ${cleanGroups}/${totalGroups} (${cleanRate}%)`);
  }
  
  Object.entries(results).forEach(([word, result]) => {
    if (result.success) {
      if (result.hasGroups) {
        console.log(`  ✅ ${word}: ${result.groupsCount} 个词性分组`);
      } else {
        console.log(`  ⚠️  ${word}: 无分组释义数据`);
      }
    } else {
      console.log(`  ❌ ${word}: ${result.error}`);
    }
  });
  
  return results;
};

// 测试特定单词的清洁效果
const testSpecificWordClean = async (word) => {
  console.log(`🧪 测试单词 "${word}" 的清洁效果...`);
  console.log('='.repeat(50));
  
  try {
    const result = await getWordDetails(word, 'ecdict');
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log(`📖 单词: ${result.word}`);
      console.log(`🔊 音标: ${result.phonetic || '无'}`);
      console.log(`📊 数据源: ${result.source}`);
      
      if (result.definitionGroups && result.definitionGroups.length > 0) {
        console.log(`\n🔗 清洁效果预览 (${result.definitionGroups.length} 个分组):`);
        console.log('-'.repeat(50));
        console.log('释义：');
        
        result.definitionGroups.forEach((group, groupIndex) => {
          console.log(`\n[${group.pos}] ${group.posName}`);
          
          if (group.english.length > 0) {
            group.english.forEach((def, defIndex) => {
              console.log(`  ${defIndex + 1}. ${def}`);
            });
          }
          
          if (group.chinese.length > 0) {
            group.chinese.forEach((def, defIndex) => {
              console.log(`  ${defIndex + 1}. ${def}`);
            });
          }
        });
        
        console.log('\n✅ 清洁效果:');
        console.log('  - 释义内容中无词性标签');
        console.log('  - 词性标签只在分组标题中显示');
        console.log('  - 布局简洁清晰');
        
        return result;
      } else {
        console.log('❌ 没有分组释义数据');
        return null;
      }
    } else {
      console.log('❌ 查询失败');
      console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return null;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始清洁释义测试...');
  console.log('='.repeat(60));
  
  try {
    // 测试清洁释义效果
    const results = await testCleanDefinitions();
    
    // 测试特定单词清洁效果
    console.log('\n🔍 详细测试 "water" 单词清洁效果:');
    await testSpecificWordClean('water');
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    return results;
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testCleanDefinitions,
  testSpecificWordClean,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testCleanDefinitions = {
    testCleanDefinitions,
    testSpecificWordClean,
    runAllTests
  };
  
  console.log('🧪 清洁释义测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testCleanDefinitions.runAllTests() // 运行所有测试');
  console.log('  - window.testCleanDefinitions.testCleanDefinitions() // 测试清洁释义效果');
  console.log('  - window.testCleanDefinitions.testSpecificWordClean("water") // 测试特定单词清洁效果');
}