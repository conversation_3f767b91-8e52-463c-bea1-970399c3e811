// 释义分组显示测试工具
// 用于测试新的按词性分组显示效果

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试分组显示效果
const testDefinitionGroups = async () => {
  console.log('🧪 测试释义分组显示效果...');
  console.log('='.repeat(50));
  
  const testWords = ['water', 'doing', 'nothing', 'school'];
  const results = {};
  
  for (const word of testWords) {
    try {
      console.log(`\n📖 测试单词: ${word}`);
      console.log('-'.repeat(30));
      
      const result = await getWordDetails(word, 'ecdict');
      
      if (result && !result.notFound) {
        console.log(`✅ ${word} 查询成功`);
        console.log(`📊 数据源: ${result.source}`);
        
        if (result.definitionGroups && result.definitionGroups.length > 0) {
          console.log(`🔗 分组数量: ${result.definitionGroups.length}`);
          
          result.definitionGroups.forEach((group, groupIndex) => {
            console.log(`\n  ${groupIndex + 1}. 词性分组:`);
            console.log(`     🏷️  词性: ${group.pos} (${group.posName})`);
            console.log(`     🇺🇸 英文释义数量: ${group.english.length}`);
            console.log(`     🇨🇳 中文释义数量: ${group.chinese.length}`);
            
            if (group.english.length > 0) {
              console.log(`     📝 英文释义:`);
              group.english.forEach((def, defIndex) => {
                console.log(`       ${defIndex + 1}. ${def}`);
              });
            }
            
            if (group.chinese.length > 0) {
              console.log(`     📝 中文释义:`);
              group.chinese.forEach((def, defIndex) => {
                console.log(`       ${defIndex + 1}. ${def}`);
              });
            }
          });
          
          results[word] = {
            success: true,
            groupsCount: result.definitionGroups.length,
            hasGroups: true,
            groups: result.definitionGroups
          };
        } else {
          console.log('❌ 没有分组释义数据');
          
          results[word] = {
            success: true,
            groupsCount: 0,
            hasGroups: false
          };
        }
      } else {
        console.log(`❌ ${word} 查询失败`);
        console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
        
        results[word] = {
          success: false,
          error: result?.error || '未找到该单词'
        };
      }
    } catch (error) {
      console.log(`❌ ${word} 查询出错: ${error.message}`);
      results[word] = {
        success: false,
        error: error.message
      };
    }
  }
  
  // 统计结果
  console.log('\n📊 分组显示统计:');
  console.log('='.repeat(50));
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const groupsCount = Object.values(results).filter(r => r.hasGroups).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`✅ 成功查询: ${successCount}/${totalCount}`);
  console.log(`🔗 有分组释义: ${groupsCount}/${totalCount}`);
  
  Object.entries(results).forEach(([word, result]) => {
    if (result.success) {
      if (result.hasGroups) {
        console.log(`  ✅ ${word}: ${result.groupsCount} 个词性分组`);
      } else {
        console.log(`  ⚠️  ${word}: 无分组释义数据`);
      }
    } else {
      console.log(`  ❌ ${word}: ${result.error}`);
    }
  });
  
  return results;
};

// 测试特定单词的分组显示
const testSpecificWordGroups = async (word) => {
  console.log(`🧪 测试单词 "${word}" 的分组显示...`);
  console.log('='.repeat(50));
  
  try {
    const result = await getWordDetails(word, 'ecdict');
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log(`📖 单词: ${result.word}`);
      console.log(`🔊 音标: ${result.phonetic || '无'}`);
      console.log(`📊 数据源: ${result.source}`);
      
      if (result.definitionGroups && result.definitionGroups.length > 0) {
        console.log(`\n🔗 分组释义详细分析 (${result.definitionGroups.length} 个分组):`);
        console.log('-'.repeat(50));
        
        result.definitionGroups.forEach((group, groupIndex) => {
          console.log(`\n${groupIndex + 1}. 词性分组:`);
          console.log(`   词性标签: ${group.pos} (${group.posName})`);
          
          if (group.english.length > 0) {
            console.log(`   英文释义 (${group.english.length} 个):`);
            group.english.forEach((def, defIndex) => {
              console.log(`     ${defIndex + 1}. ${def}`);
            });
          }
          
          if (group.chinese.length > 0) {
            console.log(`   中文释义 (${group.chinese.length} 个):`);
            group.chinese.forEach((def, defIndex) => {
              console.log(`     ${defIndex + 1}. ${def}`);
            });
          }
        });
        
        // 检查UI显示效果
        console.log('\n🎨 UI显示效果预览:');
        console.log('-'.repeat(50));
        console.log('释义：');
        
        result.definitionGroups.forEach((group, groupIndex) => {
          console.log(`\n[${group.pos}] ${group.posName}`);
          
          if (group.english.length > 0) {
            console.log('英文释义：');
            group.english.forEach((def, defIndex) => {
              console.log(`  ${defIndex + 1}. ${def}`);
            });
          }
          
          if (group.chinese.length > 0) {
            console.log('中文释义：');
            group.chinese.forEach((def, defIndex) => {
              console.log(`  ${defIndex + 1}. ${def}`);
            });
          }
        });
        
        return result;
      } else {
        console.log('❌ 没有分组释义数据');
        return null;
      }
    } else {
      console.log('❌ 查询失败');
      console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return null;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始释义分组显示测试...');
  console.log('='.repeat(60));
  
  try {
    // 测试分组显示效果
    const results = await testDefinitionGroups();
    
    // 测试特定单词分组
    console.log('\n🔍 详细测试 "water" 单词分组:');
    await testSpecificWordGroups('water');
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    return results;
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testDefinitionGroups,
  testSpecificWordGroups,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testDefinitionGroups = {
    testDefinitionGroups,
    testSpecificWordGroups,
    runAllTests
  };
  
  console.log('🧪 释义分组显示测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testDefinitionGroups.runAllTests() // 运行所有测试');
  console.log('  - window.testDefinitionGroups.testDefinitionGroups() // 测试分组显示效果');
  console.log('  - window.testDefinitionGroups.testSpecificWordGroups("water") // 测试特定单词分组');
}