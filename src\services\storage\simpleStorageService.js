/**
 * 简化统一存储服务
 * 统一AI聊天和AI写作的存储架构
 * 本地优先 + 后台Firebase同步
 */

import {
    addDoc,
    collection,
    deleteDoc,
    limit as firestoreLimit,
    getDocs,
    orderBy,
    query
} from 'firebase/firestore';
import { db } from '../../config/firebaseConfig';
import { generateUniqueId } from '../../utils/idGenerator';

// 存储键名
const STORAGE_KEYS = {
  CHAT_HISTORY: 'simple_chat_history',
  ANALYSIS_HISTORY: 'simple_analysis_history'
};

// Firebase集合名称
const FIREBASE_COLLECTIONS = {
  CHAT_HISTORY: 'chat_history',
  ANALYSIS_HISTORY: 'analysis_history'
};

// 最大存储数量
const MAX_ITEMS = {
  CHAT_HISTORY: 50,
  ANALYSIS_HISTORY: 100
};

class SimpleStorageService {
  constructor() {
    this.userId = null;
    this.syncQueue = []; // 待同步队列
    this.isSyncing = false;
  }

  // 初始化服务
  init(userId) {
    this.userId = userId;
    console.log('🚀 简化存储服务初始化:', userId);

    // 启动后台同步
    this.startBackgroundSync();

    // 监听网络状态变化
    this.setupNetworkListener();
  }

  // 启动后台同步
  startBackgroundSync() {
    // 每30秒检查一次同步队列
    setInterval(() => {
      if (this.syncQueue.length > 0 && !this.isSyncing) {
        this.processSyncQueue();
      }
    }, 30000);

    // 立即处理同步队列（减少延迟）
    setTimeout(() => {
      if (this.syncQueue.length > 0 && !this.isSyncing) {
        this.processSyncQueue();
      }
    }, 2000); // 2秒后立即同步
  }

  // 设置网络状态监听
  setupNetworkListener() {
    if (typeof window === 'undefined') return;

    const handleOnline = () => {
      console.log('🌐 网络已连接，开始同步数据...');
      if (this.syncQueue.length > 0 && !this.isSyncing) {
        this.processSyncQueue();
      }
    };

    window.addEventListener('online', handleOnline);

    // 保存清理函数
    this.networkCleanup = () => {
      window.removeEventListener('online', handleOnline);
    };
  }

  // 处理同步队列
  async processSyncQueue() {
    if (this.isSyncing || this.syncQueue.length === 0 || !this.userId) return;

    this.isSyncing = true;
    console.log('🔄 开始后台同步，队列长度:', this.syncQueue.length);

    try {
      // 逐个处理同步项目，避免batch操作过于复杂
      for (const item of this.syncQueue) {
        const { type, action, data } = item;
        const collectionName = FIREBASE_COLLECTIONS[type];

        if (!collectionName) continue;

        try {
          const userCollection = collection(db, collectionName, this.userId, 'records');

          switch (action) {
            case 'save':
              // 清理数据，移除undefined值
              const cleanData = this.cleanDataForFirebase(data);
              await addDoc(userCollection, {
                ...cleanData,
                syncedAt: new Date(),
                userId: this.userId
              });
              console.log(`✅ 同步${type}数据到Firebase:`, data.id);
              break;
            case 'delete':
              // 删除操作：需要先查询Firebase文档，然后删除
              try {
                const querySnapshot = await getDocs(userCollection);
                const docToDelete = querySnapshot.docs.find(doc =>
                  doc.data().id === data.id || doc.id === data.id
                );
                if (docToDelete) {
                  await deleteDoc(docToDelete.ref);
                  console.log(`✅ 从Firebase删除${type}数据:`, data.id);
                } else {
                  console.log(`⚠️ 未找到要删除的${type}数据:`, data.id);
                }
              } catch (deleteError) {
                console.error(`❌ 从Firebase删除${type}数据失败:`, deleteError);
              }
              break;
            case 'clear':
              // 清空操作：删除用户的所有记录
              try {
                const querySnapshot = await getDocs(userCollection);
                const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
                await Promise.all(deletePromises);
                console.log(`✅ 从Firebase清空${type}数据，删除了${querySnapshot.docs.length}条记录`);
              } catch (clearError) {
                console.error(`❌ 从Firebase清空${type}数据失败:`, clearError);
              }
              break;
          }
        } catch (itemError) {
          console.error(`❌ 同步${type}数据失败:`, itemError);
          // 继续处理其他项目
        }
      }

      // 清空同步队列
      this.syncQueue = [];
      console.log('✅ 后台同步完成');
    } catch (error) {
      console.error('❌ 后台同步失败:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  // 从Firebase同步数据到本地
  async syncFromFirebase(type) {
    if (!this.userId) return;

    try {
      const collectionName = FIREBASE_COLLECTIONS[type];
      if (!collectionName) return;

      console.log(`🔄 从Firebase同步${type}数据...`);

      const userCollection = collection(db, collectionName, this.userId, 'records');
      const q = query(
        userCollection,
        orderBy('timestamp', 'desc'),
        firestoreLimit(MAX_ITEMS[type])
      );

      const querySnapshot = await getDocs(q);
      const firebaseData = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        firebaseData.push({
          id: data.id || doc.id, // 使用数据中的id或文档id
          ...data
        });
      });

      // 合并到本地存储
      const localData = this.getHistory(type);
      const mergedData = this.mergeData(localData, firebaseData);

      // 保存合并后的数据
      const key = STORAGE_KEYS[type];
      localStorage.setItem(key, JSON.stringify(mergedData));

      console.log(`✅ ${type}数据同步完成，共${mergedData.length}条记录`);
      return mergedData;
    } catch (error) {
      console.error(`❌ 从Firebase同步${type}数据失败:`, error);
      return this.getHistory(type);
    }
  }

  // 清理数据，移除undefined值
  cleanDataForFirebase(data) {
    const cleaned = {};

    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined) {
        if (Array.isArray(value)) {
          // 清理数组中的undefined值
          cleaned[key] = value.filter(item => item !== undefined);
        } else if (typeof value === 'object' && value !== null) {
          // 递归清理对象中的undefined值
          cleaned[key] = this.cleanDataForFirebase(value);
        } else {
          cleaned[key] = value;
        }
      }
    }

    return cleaned;
  }

  // 合并本地和Firebase数据
  mergeData(localData, firebaseData) {
    const merged = [...localData];

    for (const firebaseItem of firebaseData) {
      const exists = merged.find(item => item.id === firebaseItem.id);
      if (!exists) {
        merged.push(firebaseItem);
      }
    }

    // 按时间戳排序
    return merged.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  // 检查存储空间是否足够
  checkStorageSpace(dataSize) {
    try {
      // 估算数据大小（JSON字符串长度）
      const testData = JSON.stringify(dataSize);
      const estimatedSize = new Blob([testData]).size;

      // 检查localStorage剩余空间
      let totalSize = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          totalSize += localStorage[key].length;
        }
      }

      // 假设localStorage总限制为5MB（大多数浏览器）
      const maxStorage = 5 * 1024 * 1024;
      const availableSpace = maxStorage - totalSize;

      return {
        hasSpace: availableSpace > estimatedSize * 2, // 留一些缓冲空间
        availableSpace,
        estimatedSize
      };
    } catch (error) {
      console.warn('无法检查存储空间:', error);
      return { hasSpace: true, availableSpace: 0, estimatedSize: 0 };
    }
  }

  // 清理旧记录以释放空间
  cleanupOldRecords(type, targetReduction = 0.5) {
    try {
      const key = STORAGE_KEYS[type];
      const history = this.getHistory(type);

      if (history.length === 0) return;

      // 计算需要删除的记录数量
      const deleteCount = Math.ceil(history.length * targetReduction);
      const remainingHistory = history.slice(deleteCount);

      // 保存清理后的历史
      localStorage.setItem(key, JSON.stringify(remainingHistory));

      console.log(`🧹 清理了${deleteCount}条旧${type}记录，剩余${remainingHistory.length}条`);
      return true;
    } catch (error) {
      console.error(`清理${type}记录失败:`, error);
      return false;
    }
  }

  // 通用保存方法
  saveHistory(type, data) {
    try {
      const key = STORAGE_KEYS[type];
      const maxItems = MAX_ITEMS[type];

      if (!key) {
        throw new Error(`不支持的历史类型: ${type}`);
      }

      const history = this.getHistory(type);
      const newRecord = {
        id: generateUniqueId(),
        ...data,
        timestamp: new Date().toISOString(),
        userId: this.userId
      };

      // 添加到历史记录开头
      const updatedHistory = [newRecord, ...history];

      // 限制数量
      if (updatedHistory.length > maxItems) {
        updatedHistory.splice(maxItems);
      }

      // 检查存储空间
      const spaceCheck = this.checkStorageSpace(updatedHistory);
      if (!spaceCheck.hasSpace) {
        console.warn('⚠️ 存储空间不足，尝试清理旧记录...');
        // 清理50%的旧记录
        this.cleanupOldRecords(type, 0.5);
        // 重新计算历史记录
        const cleanedHistory = this.getHistory(type);
        const finalHistory = [newRecord, ...cleanedHistory];
        if (finalHistory.length > maxItems) {
          finalHistory.splice(maxItems);
        }
        updatedHistory.splice(0, updatedHistory.length, ...finalHistory);
      }

      // 立即保存到本地
      localStorage.setItem(key, JSON.stringify(updatedHistory));
      console.log(`✅ ${type}历史保存成功:`, newRecord.id);

      // 添加到同步队列
      this.syncQueue.push({
        type,
        action: 'save',
        data: newRecord
      });

      return newRecord;
    } catch (error) {
      // 处理QuotaExceededError
      if (error.name === 'QuotaExceededError') {
        console.warn('⚠️ 存储空间已满，尝试清理所有旧记录...');

        // 尝试清理更多空间
        const cleanupSuccess = this.cleanupOldRecords(type, 0.8);

        if (cleanupSuccess) {
          // 重试保存
          try {
            const key = STORAGE_KEYS[type];
            const history = this.getHistory(type);
            const newRecord = {
              id: generateUniqueId(),
              ...data,
              timestamp: new Date().toISOString(),
              userId: this.userId
            };

            const updatedHistory = [newRecord, ...history];
            if (updatedHistory.length > MAX_ITEMS[type]) {
              updatedHistory.splice(MAX_ITEMS[type]);
            }

            localStorage.setItem(key, JSON.stringify(updatedHistory));
            console.log(`✅ ${type}历史保存成功（清理后）:`, newRecord.id);

            this.syncQueue.push({
              type,
              action: 'save',
              data: newRecord
            });

            return newRecord;
          } catch (retryError) {
            console.error(`重试保存${type}历史失败:`, retryError);
            // 如果还是失败，至少保存到内存中（不持久化）
            console.warn('⚠️ 无法保存到本地存储，数据将不会持久化');
            return {
              id: generateUniqueId(),
              ...data,
              timestamp: new Date().toISOString(),
              userId: this.userId,
              _temp: true // 标记为临时数据
            };
          }
        } else {
          console.error(`清理${type}记录失败，无法保存新记录`);
          throw new Error('存储空间不足且无法清理，请手动清理浏览器数据');
        }
      } else {
        console.error(`保存${type}历史失败:`, error);
        throw error;
      }
    }
  }

  // 通用获取方法
  getHistory(type, limit = null) {
    try {
      const key = STORAGE_KEYS[type];
      if (!key) {
        throw new Error(`不支持的历史类型: ${type}`);
      }

      const history = localStorage.getItem(key);
      const parsed = history ? JSON.parse(history) : [];

      // 如果指定了限制数量
      if (limit && limit > 0) {
        return parsed.slice(0, limit);
      }

      return parsed;
    } catch (error) {
      console.error(`获取${type}历史失败:`, error);
      return [];
    }
  }

  // 通用删除方法
  deleteHistory(type, recordId) {
    try {
      const key = STORAGE_KEYS[type];
      if (!key) {
        throw new Error(`不支持的历史类型: ${type}`);
      }

      const history = this.getHistory(type);
      const updatedHistory = history.filter(record => record.id !== recordId);

      // 立即更新本地
      localStorage.setItem(key, JSON.stringify(updatedHistory));
      console.log(`✅ ${type}历史删除成功:`, recordId);

      // 添加到同步队列
      this.syncQueue.push({
        type,
        action: 'delete',
        data: { id: recordId }
      });

      return true;
    } catch (error) {
      console.error(`删除${type}历史失败:`, error);
      return false;
    }
  }

  // 通用清空方法
  clearHistory(type) {
    try {
      const key = STORAGE_KEYS[type];
      if (!key) {
        throw new Error(`不支持的历史类型: ${type}`);
      }

      // 立即清空本地
      localStorage.removeItem(key);
      console.log(`✅ ${type}历史清空成功`);

      // 添加到同步队列
      this.syncQueue.push({
        type,
        action: 'clear',
        data: {}
      });

      return true;
    } catch (error) {
      console.error(`清空${type}历史失败:`, error);
      return false;
    }
  }

  // 聊天历史专用方法
  saveChatSession(messages, sessionTitle = null) {
    // 生成智能标题
    const smartTitle = sessionTitle || this.generateChatTitle(messages);

    return this.saveHistory('CHAT_HISTORY', {
      messages,
      title: smartTitle, // 使用智能生成的标题
      sessionTitle: smartTitle, // 保持向后兼容
      messageCount: messages ? messages.length : 0
    });
  }

  // 生成聊天标题的智能算法
  generateChatTitle(messages) {
    if (!messages || messages.length === 0) {
      return '新对话';
    }

    // 获取用户的第一条消息作为标题基础
    const firstUserMessage = messages.find(msg =>
      msg.role === 'user' || msg.type === 'user'
    );

    if (!firstUserMessage) {
      return '新对话';
    }

    const content = firstUserMessage.content || firstUserMessage.text || '';

    // 清理内容，移除特殊字符和多余空格
    let cleanContent = content
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ') // 保留字母、数字、空格和中文
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();

    // 如果内容太短，直接返回
    if (cleanContent.length <= 3) {
      return '新对话';
    }

    // 如果内容太长，截取前20个字符
    if (cleanContent.length > 20) {
      cleanContent = cleanContent.substring(0, 20) + '...';
    }

    // 根据内容类型和关键词添加前缀
    const lowerContent = cleanContent.toLowerCase();

    // 帮助类
    if (lowerContent.includes('help') || lowerContent.includes('帮助') ||
        lowerContent.includes('how to') || lowerContent.includes('如何')) {
      return `💡 ${cleanContent}`;
    }

    // 写作类
    if (lowerContent.includes('write') || lowerContent.includes('写作') ||
        lowerContent.includes('story') || lowerContent.includes('故事') ||
        lowerContent.includes('essay') || lowerContent.includes('文章') ||
        lowerContent.includes('composition') || lowerContent.includes('作文')) {
      return `✍️ ${cleanContent}`;
    }

    // 学习类
    if (lowerContent.includes('learn') || lowerContent.includes('学习') ||
        lowerContent.includes('study') || lowerContent.includes('研究') ||
        lowerContent.includes('understand') || lowerContent.includes('理解')) {
      return `📚 ${cleanContent}`;
    }

    // 练习类
    if (lowerContent.includes('practice') || lowerContent.includes('练习') ||
        lowerContent.includes('exercise') || lowerContent.includes('训练') ||
        lowerContent.includes('drill') || lowerContent.includes('操练')) {
      return `🎯 ${cleanContent}`;
    }

    // 日记类
    if (lowerContent.includes('diary') || lowerContent.includes('日记') ||
        lowerContent.includes('journal') || lowerContent.includes('日志') ||
        lowerContent.includes('entry') || lowerContent.includes('条目')) {
      return `📖 ${cleanContent}`;
    }

    // 语法类
    if (lowerContent.includes('grammar') || lowerContent.includes('语法') ||
        lowerContent.includes('sentence') || lowerContent.includes('句子') ||
        lowerContent.includes('structure') || lowerContent.includes('结构') ||
        lowerContent.includes('tense') || lowerContent.includes('时态')) {
      return `📝 ${cleanContent}`;
    }

    // 词汇类
    if (lowerContent.includes('vocabulary') || lowerContent.includes('词汇') ||
        lowerContent.includes('word') || lowerContent.includes('单词') ||
        lowerContent.includes('phrase') || lowerContent.includes('短语') ||
        lowerContent.includes('expression') || lowerContent.includes('表达')) {
      return `📖 ${cleanContent}`;
    }

    // 发音类
    if (lowerContent.includes('pronunciation') || lowerContent.includes('发音') ||
        lowerContent.includes('speak') || lowerContent.includes('说话') ||
        lowerContent.includes('accent') || lowerContent.includes('口音') ||
        lowerContent.includes('sound') || lowerContent.includes('声音')) {
      return `🎤 ${cleanContent}`;
    }

    // 翻译类
    if (lowerContent.includes('translate') || lowerContent.includes('翻译') ||
        lowerContent.includes('translation') || lowerContent.includes('译文') ||
        lowerContent.includes('meaning') || lowerContent.includes('意思')) {
      return `🔄 ${cleanContent}`;
    }

    // 听力类
    if (lowerContent.includes('listen') || lowerContent.includes('听力') ||
        lowerContent.includes('hearing') || lowerContent.includes('听') ||
        lowerContent.includes('audio') || lowerContent.includes('音频')) {
      return `👂 ${cleanContent}`;
    }

    // 阅读类
    if (lowerContent.includes('read') || lowerContent.includes('阅读') ||
        lowerContent.includes('reading') || lowerContent.includes('读') ||
        lowerContent.includes('text') || lowerContent.includes('文本')) {
      return `📖 ${cleanContent}`;
    }

    // 考试类
    if (lowerContent.includes('exam') || lowerContent.includes('考试') ||
        lowerContent.includes('test') || lowerContent.includes('测试') ||
        lowerContent.includes('quiz') || lowerContent.includes('测验')) {
      return `📋 ${cleanContent}`;
    }

    // 默认对话类
    return `💬 ${cleanContent}`;
  }

  getChatHistory(limit = 50) {
    return this.getHistory('CHAT_HISTORY', limit);
  }

  deleteChatSession(sessionId) {
    return this.deleteHistory('CHAT_HISTORY', sessionId);
  }

  clearChatHistory() {
    return this.clearHistory('CHAT_HISTORY');
  }

  // AI分析历史专用方法
  saveAnalysis(text, rawAnalysis, analysis) {
    return this.saveHistory('ANALYSIS_HISTORY', {
      text,
      rawAnalysis,
      analysis
    });
  }

  getAnalysisHistory(limit = 100) {
    return this.getHistory('ANALYSIS_HISTORY', limit);
  }

  deleteAnalysis(recordId) {
    return this.deleteHistory('ANALYSIS_HISTORY', recordId);
  }

  clearAnalysisHistory() {
    return this.clearHistory('ANALYSIS_HISTORY');
  }

  // 手动同步所有数据
  async manualSync() {
    if (!this.userId) {
      console.log('❌ 用户未登录，无法同步');
      return;
    }

    console.log('🔄 开始手动同步...');

    try {
      // 同步聊天历史
      await this.syncFromFirebase('CHAT_HISTORY');

      // 同步分析历史
      await this.syncFromFirebase('ANALYSIS_HISTORY');

      // 处理待同步队列
      await this.processSyncQueue();

      console.log('✅ 手动同步完成');
    } catch (error) {
      console.error('❌ 手动同步失败:', error);
    }
  }

  // 获取统计信息
  getStats() {
    return {
      chat: this.getChatHistory().length,
      analysis: this.getAnalysisHistory().length,
      syncQueue: this.syncQueue.length
    };
  }

  // 手动清理存储空间
  clearAllStorage() {
    try {
      console.log('🧹 开始清理所有本地存储...');

      // 清理聊天历史
      localStorage.removeItem(STORAGE_KEYS.CHAT_HISTORY);
      console.log('✅ 聊天历史已清理');

      // 清理分析历史
      localStorage.removeItem(STORAGE_KEYS.ANALYSIS_HISTORY);
      console.log('✅ 分析历史已清理');

      // 清理其他相关数据
      localStorage.removeItem('current_chat_messages');
      localStorage.removeItem('current_chat_suggestions');
      localStorage.removeItem('shared_writing_context');
      console.log('✅ 临时数据已清理');

      // 清空同步队列
      this.syncQueue = [];

      console.log('✅ 所有本地存储已清理完成');
      return true;
    } catch (error) {
      console.error('❌ 清理存储失败:', error);
      return false;
    }
  }

  // 获取存储使用情况
  getStorageInfo() {
    try {
      let totalSize = 0;
      const details = {};

      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          const size = localStorage[key].length;
          totalSize += size;
          details[key] = {
            size: size,
            sizeKB: Math.round(size / 1024 * 100) / 100
          };
        }
      }

      return {
        totalSize: totalSize,
        totalSizeKB: Math.round(totalSize / 1024 * 100) / 100,
        totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
        details: details,
        estimatedLimit: '5MB (浏览器默认限制)'
      };
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return null;
    }
  }

  // 清理资源
  cleanup() {
    if (this.networkCleanup) {
      this.networkCleanup();
    }
    this.userId = null;
    this.syncQueue = [];
    this.isSyncing = false;
  }
}

// 创建单例实例
const simpleStorageService = new SimpleStorageService();

export default simpleStorageService;
