/**
 * 文本高亮工具函数
 * 用于处理文本高亮渲染的复杂逻辑
 */

/**
 * 过滤并排序可高亮的建议
 * @param {Array} suggestions - 建议数组
 * @returns {Array} 排序后的唯一建议数组
 */
export const filterAndSortSuggestions = (suggestions) => {
  // 获取可以高亮的建议（有具体位置信息的）
  const highlightableSuggestions = suggestions.filter(s => {
    const hasPositions = s.positions && s.positions.length > 0;
    return hasPositions;
  });

  // 确保每个建议都有唯一的位置
  const uniquePositionSuggestions = [];
  const positionMap = new Map(); // 用于跟踪已处理的位置

  highlightableSuggestions.forEach(suggestion => {
    const position = suggestion.positions[0];
    const positionKey = `${position.start}-${position.end}`;

    // 如果这个位置还没有被处理过，添加到唯一建议列表
    if (!positionMap.has(positionKey)) {
      positionMap.set(positionKey, suggestion);
      uniquePositionSuggestions.push(suggestion);
    }
  });

  // 按位置排序
  return uniquePositionSuggestions.sort((a, b) => {
    const aPos = a.positions[0].start;
    const bPos = b.positions[0].start;
    return aPos - bPos;
  });
};

/**
 * 创建零长度位置的高亮元素
 * @param {Object} suggestion - 建议对象
 * @param {number} index - 索引
 * @param {boolean} isDarkMode - 是否为暗色模式
 * @param {Function} onMouseEnter - 鼠标进入处理函数
 * @param {Function} onMouseLeave - 鼠标离开处理函数
 * @returns {JSX.Element} 高亮元素
 */
export const createZeroLengthHighlight = (suggestion, index, isDarkMode, onMouseEnter, onMouseLeave) => {
  return (
    <span
      key={`suggestion-${suggestion.id}`}
      className={`suggestion-highlight ${suggestion.category}-error`}
      style={{
        position: 'relative',
        borderLeft: '2px solid',
        borderColor: isDarkMode ? '#D2691E' : '#991B1B',
        paddingLeft: '2px',
        marginLeft: '-2px',
        pointerEvents: 'auto',
        color: isDarkMode ? '#E8DCC6' : '#5D4037'
      }}
      data-suggestion-id={suggestion.id}
      onMouseEnter={(e) => onMouseEnter(e, suggestion)}
      onMouseLeave={onMouseLeave}
    >
      ⚬
    </span>
  );
};

/**
 * 创建普通高亮元素
 * @param {Object} suggestion - 建议对象
 * @param {string} highlightText - 高亮文本
 * @param {boolean} isHovered - 是否悬停
 * @param {boolean} isDarkMode - 是否为暗色模式
 * @param {Function} onMouseEnter - 鼠标进入处理函数
 * @param {Function} onMouseLeave - 鼠标离开处理函数
 * @returns {JSX.Element} 高亮元素
 */
export const createHighlightElement = (suggestion, highlightText, isHovered, isDarkMode, onMouseEnter, onMouseLeave) => {
  return (
    <span
      key={suggestion.id}
      className="relative inline cursor-pointer"
      style={{
        borderBottom: `2px solid ${isDarkMode ? '#D2691E' : '#991B1B'}`,
        backgroundColor: isHovered ?
          (isDarkMode ? 'rgba(210, 105, 30, 0.2)' : '#FEF2F2') :
          'transparent',
        borderRadius: isHovered ? '4px' : '0',
        padding: '2px 4px', // 始终保持相同的内边距
        margin: '-2px -4px', // 使用负margin抵消padding，保持整体尺寸不变
        cursor: 'pointer',
        display: 'inline-block',
        position: 'relative',
        pointerEvents: 'auto',
        color: isDarkMode ? '#E8DCC6' : '#5D4037',
        transition: 'background-color 0.2s ease' // 只对背景色应用过渡效果
      }}
      data-suggestion-id={suggestion.id}
      onMouseEnter={(e) => onMouseEnter(e, suggestion)}
      onMouseLeave={onMouseLeave}
    >
      {highlightText}
    </span>
  );
};

/**
 * 创建普通文本元素
 * @param {string} text - 文本内容
 * @param {number} startIndex - 开始索引
 * @param {number} endIndex - 结束索引
 * @param {number} index - 元素索引
 * @returns {JSX.Element} 文本元素
 */
export const createTextElement = (text, startIndex, endIndex, index) => {
  return (
    <span key={`text-${index}-${startIndex}`}>
      {text.substring(startIndex, endIndex)}
    </span>
  );
};
