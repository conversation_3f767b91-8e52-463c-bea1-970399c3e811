/**
 * 测试聊天历史记录显示功能
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试聊天历史记录数据结构
export const testChatHistoryStructure = () => {
  console.log('🧪 测试聊天历史记录数据结构...');
  
  const testUserId = 'display_test_' + Date.now();
  simpleStorageService.init(testUserId);
  
  // 创建测试聊天数据
  const testMessages = [
    { role: 'user', content: 'Hello, this is a test message' },
    { role: 'assistant', content: 'Hi! This is a test response.' },
    { role: 'user', content: 'How are you?' },
    { role: 'assistant', content: 'I am doing well, thank you!' }
  ];
  
  console.log('💾 保存测试聊天数据...');
  const savedChat = simpleStorageService.saveChatSession(testMessages, '测试对话');
  console.log('✅ 聊天数据保存成功:', savedChat);
  
  // 获取聊天历史
  const chatHistory = simpleStorageService.getChatHistory();
  console.log('📋 聊天历史记录:', chatHistory);
  
  if (chatHistory.length > 0) {
    const session = chatHistory[0];
    console.log('📄 最新会话详情:');
    console.log('  - ID:', session.id);
    console.log('  - 标题:', session.title);
    console.log('  - 会话标题:', session.sessionTitle);
    console.log('  - 消息数量:', session.messageCount);
    console.log('  - 消息数组长度:', session.messages ? session.messages.length : 'undefined');
    console.log('  - 时间戳:', session.timestamp);
    
    // 检查字段是否存在
    const hasTitle = session.title !== undefined;
    const hasMessageCount = session.messageCount !== undefined;
    const hasMessages = session.messages !== undefined;
    
    console.log('🔍 字段检查:');
    console.log('  - 有title字段:', hasTitle);
    console.log('  - 有messageCount字段:', hasMessageCount);
    console.log('  - 有messages字段:', hasMessages);
    
    if (hasTitle && hasMessageCount && hasMessages) {
      console.log('✅ 数据结构正确！');
      return true;
    } else {
      console.log('❌ 数据结构不完整');
      return false;
    }
  } else {
    console.log('❌ 没有获取到聊天历史');
    return false;
  }
};

// 测试ChatHistoryModal兼容性
export const testChatHistoryModalCompatibility = () => {
  console.log('🎭 测试ChatHistoryModal兼容性...');
  
  const testUserId = 'modal_test_' + Date.now();
  simpleStorageService.init(testUserId);
  
  // 创建多个测试会话
  const testSessions = [
    {
      messages: [
        { role: 'user', content: 'First conversation' },
        { role: 'assistant', content: 'Hello!' }
      ],
      sessionTitle: '第一个对话'
    },
    {
      messages: [
        { role: 'user', content: 'Second conversation' },
        { role: 'assistant', content: 'Hi there!' },
        { role: 'user', content: 'How are you?' }
      ],
      sessionTitle: '第二个对话'
    },
    {
      messages: [
        { role: 'user', content: 'Third conversation' }
      ]
      // 没有sessionTitle，测试默认值
    }
  ];
  
  console.log('💾 保存多个测试会话...');
  testSessions.forEach((session, index) => {
    const saved = simpleStorageService.saveChatSession(session.messages, session.sessionTitle);
    console.log(`✅ 会话${index + 1}保存成功:`, saved.id);
  });
  
  // 获取所有聊天历史
  const allHistory = simpleStorageService.getChatHistory();
  console.log('📋 所有聊天历史:', allHistory.length, '条记录');
  
  // 模拟ChatHistoryModal的渲染逻辑
  allHistory.forEach((session, index) => {
    console.log(`\n📄 会话${index + 1}:`);
    console.log('  - 显示标题:', session.title || session.sessionTitle || '未命名对话');
    console.log('  - 显示消息数:', session.messageCount || (session.messages ? session.messages.length : 0));
    console.log('  - 时间戳:', session.timestamp);
    
    // 检查是否有必要字段
    const displayTitle = session.title || session.sessionTitle || '未命名对话';
    const displayCount = session.messageCount || (session.messages ? session.messages.length : 0);
    
    if (displayTitle && displayCount >= 0) {
      console.log('  ✅ 可以正常显示');
    } else {
      console.log('  ❌ 显示可能有问题');
    }
  });
  
  console.log('\n🎉 ChatHistoryModal兼容性测试完成！');
  return true;
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testChatHistoryStructure = testChatHistoryStructure;
  window.testChatHistoryModalCompatibility = testChatHistoryModalCompatibility;
}
