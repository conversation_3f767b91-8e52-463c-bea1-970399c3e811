import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock the browser's speech synthesis API
const mockSpeechSynthesis = {
  getVoices: vi.fn().mockReturnValue([
    { name: 'Google US English', lang: 'en-US' },
    { name: 'Google UK English Female', lang: 'en-GB' },
  ]),
  speak: vi.fn(),
  cancel: vi.fn(),
  pause: vi.fn(),
  resume: vi.fn(),
  onvoiceschanged: null,
};

Object.defineProperty(window, 'speechSynthesis', {
  value: mockSpeechSynthesis,
  writable: true,
});

// Mock the scrollIntoView function
window.HTMLElement.prototype.scrollIntoView = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock DOMPurify for online-dict module
vi.mock('dompurify', () => ({
  default: {
    sanitize: vi.fn((input, config) => {
      // Simple mock that returns a DOM fragment or string based on config
      if (config && config.RETURN_DOM_FRAGMENT) {
        // Create a mock DOM fragment
        const fragment = {
          firstChild: {
            innerHTML: typeof input === 'string' ? input : input.innerHTML || '',
            outerHTML: typeof input === 'string' ? input : input.outerHTML || input.innerHTML || '',
          }
        };
        return fragment;
      }
      // Return the input as-is for string sanitization
      return typeof input === 'string' ? input : input.innerHTML || input.textContent || '';
    })
  }
}));
