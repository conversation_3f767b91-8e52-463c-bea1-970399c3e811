import React from 'react';
import { Zap, Lightbulb, Clock, Book } from 'lucide-react';
import DateTimeWeather from './DateTimeWeather';

/**
 * 编辑器操作栏组件
 * 包含日期天气显示和各种操作按钮
 */
const EditorActionBar = ({
  isDarkMode,
  rawAIResponse,
  onShowDictionary,
  onShowHistory,
  onShowAIResponse
}) => {
  return (
    <div className="flex items-center justify-between" style={{ marginBottom: '40px', position: 'relative', zIndex: 20 }}>
      <DateTimeWeather isDarkMode={isDarkMode} />
      <div className="flex items-center gap-6">
        <div className="flex items-center gap-3">
          <button
            onClick={onShowDictionary}
            className="editor-action-btn"
            title="词典查询"
          >
            <Book
              style={{
                width: '24px',
                height: '24px',
                color: isDarkMode ? '#D2691E' : '#166534',
                fill: 'none',
                stroke: 'currentColor',
                strokeWidth: 2,
                pointerEvents: 'none'
              }}
            />
          </button>
          
          <button
            onClick={onShowHistory}
            className="editor-action-btn"
            title="历史记录"
          >
            <Clock
              style={{
                width: '24px',
                height: '24px',
                color: isDarkMode ? '#C4B59A' : '#8B4513',
                fill: 'none',
                stroke: 'currentColor',
                strokeWidth: 2,
                pointerEvents: 'none'
              }}
            />
          </button>
        </div>

        {rawAIResponse && (
          <button
            onClick={onShowAIResponse}
            className="ai-response-btn"
            title="分析与改进建议"
          >
            <Lightbulb
              style={{
                width: '24px',
                height: '24px',
                color: isDarkMode ? '#C4B59A' : '#8B4513',
                fill: 'none',
                stroke: 'currentColor',
                strokeWidth: 2,
                pointerEvents: 'none'
              }}
            />
          </button>
        )}
      </div>
    </div>
  );
};

export default EditorActionBar;
