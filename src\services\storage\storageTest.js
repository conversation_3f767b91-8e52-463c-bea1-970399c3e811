/**
 * 统一存储服务测试脚本
 * 用于验证新的存储架构是否正常工作
 */

import unifiedStorageService from './unifiedStorageService';

/**
 * 测试基本缓存功能
 */
export const testBasicCache = async () => {
  console.log('🧪 开始测试基本缓存功能...');
  
  try {
    // 测试设置和获取缓存
    const testData = { message: 'Hello World', timestamp: Date.now() };
    unifiedStorageService.setCache('test_cache', testData);
    
    const retrievedData = unifiedStorageService.getCache('test_cache', 60000);
    if (JSON.stringify(retrievedData) === JSON.stringify(testData)) {
      console.log('✅ 缓存设置和获取测试通过');
    } else {
      console.log('❌ 缓存设置和获取测试失败');
      return false;
    }
    
    // 测试缓存过期
    unifiedStorageService.setCache('test_expiry', testData);
    const expiredData = unifiedStorageService.getCache('test_expiry', 1); // 1ms过期
    if (expiredData === null) {
      console.log('✅ 缓存过期测试通过');
    } else {
      console.log('❌ 缓存过期测试失败');
      return false;
    }
    
    // 清理测试数据
    unifiedStorageService.clearCache('test_cache');
    unifiedStorageService.clearCache('test_expiry');
    
    console.log('✅ 基本缓存功能测试全部通过');
    return true;
  } catch (error) {
    console.error('❌ 基本缓存功能测试失败:', error);
    return false;
  }
};

/**
 * 测试AI分析历史存储
 */
export const testAnalysisHistory = async () => {
  console.log('🧪 开始测试AI分析历史存储...');
  
  try {
    // 模拟用户ID
    const testUserId = 'test-user-123';
    unifiedStorageService.init(testUserId);
    
    // 模拟AI分析数据
    const mockAnalysisData = {
      text: 'This is a test text for analysis.',
      rawAnalysis: 'AI analysis result...',
      analysis: {
        suggestions: [
          { original: 'test', replacement: 'example', explanation: 'Better word choice' }
        ]
      }
    };
    
    // 测试保存分析记录
    const savedRecord = await unifiedStorageService.saveAnalysis(mockAnalysisData);
    console.log('✅ 保存AI分析记录成功:', savedRecord.id);
    
    // 测试获取分析历史
    const history = await unifiedStorageService.getAnalysisHistory(10);
    console.log('✅ 获取AI分析历史成功，记录数:', history.length);
    
    // 验证数据一致性
    const foundRecord = history.find(record => record.id === savedRecord.id);
    if (foundRecord && foundRecord.text === mockAnalysisData.text) {
      console.log('✅ 数据一致性验证通过');
    } else {
      console.log('❌ 数据一致性验证失败');
      return false;
    }
    
    console.log('✅ AI分析历史存储测试全部通过');
    return true;
  } catch (error) {
    console.error('❌ AI分析历史存储测试失败:', error);
    return false;
  }
};

/**
 * 测试缓存性能
 */
export const testCachePerformance = async () => {
  console.log('🧪 开始测试缓存性能...');
  
  try {
    const testData = Array.from({ length: 100 }, (_, i) => ({
      id: `test-${i}`,
      content: `测试消息 ${i}`,
      timestamp: Date.now()
    }));
    
    // 测试设置缓存性能
    const setStartTime = performance.now();
    unifiedStorageService.setCache('performance_test', testData);
    const setEndTime = performance.now();
    const setTime = setEndTime - setStartTime;
    
    // 测试获取缓存性能
    const getStartTime = performance.now();
    const retrievedData = unifiedStorageService.getCache('performance_test', 300000);
    const getEndTime = performance.now();
    const getTime = getEndTime - getStartTime;
    
    console.log('📊 性能测试结果:');
    console.log(`  设置缓存: ${setTime.toFixed(2)}ms`);
    console.log(`  获取缓存: ${getTime.toFixed(2)}ms`);
    console.log(`  数据大小: ${JSON.stringify(testData).length} 字符`);
    
    if (setTime < 10 && getTime < 5) {
      console.log('✅ 缓存性能测试通过');
      return true;
    } else {
      console.log('⚠️ 缓存性能可能需要优化');
      return false;
    }
  } catch (error) {
    console.error('❌ 缓存性能测试失败:', error);
    return false;
  } finally {
    // 清理测试数据
    unifiedStorageService.clearCache('performance_test');
  }
};

/**
 * 测试同步状态监听
 */
export const testSyncListeners = () => {
  console.log('🧪 开始测试同步状态监听...');
  
  try {
    let listenerCalled = false;
    
    // 添加监听器
    const testListener = (status) => {
      console.log(`📡 同步状态变化: ${status}`);
      listenerCalled = true;
    };
    
    unifiedStorageService.addSyncListener(testListener);
    
    // 模拟同步状态变化
    unifiedStorageService.triggerSyncStatus('test');
    
    // 移除监听器
    unifiedStorageService.removeSyncListener(testListener);
    
    if (listenerCalled) {
      console.log('✅ 同步状态监听测试通过');
      return true;
    } else {
      console.log('❌ 同步状态监听测试失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 同步状态监听测试失败:', error);
    return false;
  }
};

/**
 * 测试缓存统计
 */
export const testCacheStats = () => {
  console.log('🧪 开始测试缓存统计...');
  
  try {
    // 设置一些测试缓存
    unifiedStorageService.setCache('test_stats_1', { data: 'test1' });
    unifiedStorageService.setCache('test_stats_2', { data: 'test2' });
    
    // 获取统计信息
    const stats = unifiedStorageService.getCacheStats();
    
    if (stats && typeof stats.totalSize === 'number' && stats.itemCount >= 2) {
      console.log('✅ 缓存统计测试通过');
      console.log(`📊 缓存统计:`, {
        总大小: `${Math.round(stats.totalSize / 1024)}KB`,
        条目数: stats.itemCount,
        在线状态: stats.isOnline,
        同步状态: stats.syncInProgress
      });
    } else {
      console.log('❌ 缓存统计测试失败');
      return false;
    }
    
    // 清理测试数据
    unifiedStorageService.clearCache('test_stats_1');
    unifiedStorageService.clearCache('test_stats_2');
    
    return true;
  } catch (error) {
    console.error('❌ 缓存统计测试失败:', error);
    return false;
  }
};

/**
 * 运行所有存储测试
 */
export const runAllStorageTests = async () => {
  console.log('🚀 开始运行所有存储测试...');
  console.log('='.repeat(50));
  
  const tests = [
    { name: '基本缓存功能', test: testBasicCache },
    { name: 'AI分析历史存储', test: testAnalysisHistory },
    { name: '缓存性能', test: testCachePerformance },
    { name: '同步状态监听', test: testSyncListeners },
    { name: '缓存统计', test: testCacheStats }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    console.log(`\n📋 运行测试: ${name}`);
    try {
      const result = await test();
      if (result) {
        passedTests++;
        console.log(`✅ ${name} 测试通过`);
      } else {
        console.log(`❌ ${name} 测试失败`);
      }
    } catch (error) {
      console.error(`❌ ${name} 测试出错:`, error);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有存储测试通过！');
  } else {
    console.log('⚠️ 部分测试失败，请检查存储功能');
  }
  
  return passedTests === totalTests;
};

// 导出测试函数供控制台使用
if (typeof window !== 'undefined') {
  window.storageTests = {
    testBasicCache,
    testAnalysisHistory,
    testCachePerformance,
    testSyncListeners,
    testCacheStats,
    runAllStorageTests
  };
  
  console.log('🔧 存储测试函数已加载到 window.storageTests');
  console.log('💡 使用方法: await window.storageTests.runAllStorageTests()');
}
