import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore, enableNetwork, disableNetwork, enableIndexedDbPersistence } from "firebase/firestore";

// Your web app's Firebase configuration, taken from SDK.TXT
const firebaseConfig = {
  apiKey: "AIzaSyCcJd72vonL3qCtXVI_mhrxPbgTy1TialE",
  authDomain: "english-assistant-4662d.firebaseapp.com",
  projectId: "english-assistant-4662d",
  storageBucket: "english-assistant-4662d.appspot.com",
  messagingSenderId: "420501525543",
  appId: "1:420501525543:web:e9a853c7adabf85416ea02",
  measurementId: "G-CKMD9MD6WE"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize and export Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);

// 启用Firebase持久性缓存
let persistenceEnabled = false;
const enablePersistence = async () => {
  try {
    await enableIndexedDbPersistence(db);
    persistenceEnabled = true;
    console.log('✅ Firebase持久性缓存已启用');
  } catch (err) {
    if (err.code === 'failed-precondition') {
      console.warn('⚠️ Firebase持久性缓存启用失败：多个标签页同时打开');
    } else if (err.code === 'unimplemented') {
      console.warn('⚠️ Firebase持久性缓存不支持当前浏览器');
    } else {
      console.error('❌ Firebase持久性缓存启用失败:', err);
    }
  }
};

// 立即启用持久性缓存
enablePersistence();

// 导出网络控制函数
export const enableFirebaseNetwork = () => enableNetwork(db);
export const disableFirebaseNetwork = () => disableNetwork(db);
export const isPersistenceEnabled = () => persistenceEnabled;
