/**
 * 本地存储测试工具
 * 用于检查现有localStorage功能是否正常工作
 */

/**
 * 测试localStorage基本功能
 */
export const testLocalStorageBasic = () => {
  console.log('🧪 开始测试localStorage基本功能...');
  
  try {
    // 测试写入
    localStorage.setItem('test_key', 'test_value');
    console.log('✅ localStorage写入测试通过');
    
    // 测试读取
    const value = localStorage.getItem('test_key');
    if (value === 'test_value') {
      console.log('✅ localStorage读取测试通过');
    } else {
      console.log('❌ localStorage读取测试失败');
      return false;
    }
    
    // 测试删除
    localStorage.removeItem('test_key');
    const deletedValue = localStorage.getItem('test_key');
    if (deletedValue === null) {
      console.log('✅ localStorage删除测试通过');
    } else {
      console.log('❌ localStorage删除测试失败');
      return false;
    }
    
    // 清理测试数据
    localStorage.removeItem('test_key');
    
    console.log('✅ localStorage基本功能测试全部通过');
    return true;
  } catch (error) {
    console.error('❌ localStorage基本功能测试失败:', error);
    return false;
  }
};

/**
 * 测试JSON序列化和反序列化
 */
export const testJSONSerialization = () => {
  console.log('🧪 开始测试JSON序列化功能...');
  
  try {
    const testData = {
      id: 1,
      text: '测试文本',
      timestamp: new Date().toISOString(),
      array: [1, 2, 3],
      object: { key: 'value' }
    };
    
    // 测试序列化
    const serialized = JSON.stringify(testData);
    localStorage.setItem('test_json', serialized);
    console.log('✅ JSON序列化测试通过');
    
    // 测试反序列化
    const retrieved = localStorage.getItem('test_json');
    const deserialized = JSON.parse(retrieved);
    
    // 验证数据完整性
    if (deserialized.id === testData.id && 
        deserialized.text === testData.text &&
        deserialized.array.length === testData.array.length) {
      console.log('✅ JSON反序列化测试通过');
    } else {
      console.log('❌ JSON反序列化测试失败');
      return false;
    }
    
    // 清理测试数据
    localStorage.removeItem('test_json');
    
    console.log('✅ JSON序列化功能测试全部通过');
    return true;
  } catch (error) {
    console.error('❌ JSON序列化功能测试失败:', error);
    return false;
  }
};

/**
 * 测试现有历史记录服务的localStorage功能
 */
export const testExistingHistoryServices = async () => {
  console.log('🧪 开始测试现有历史记录服务...');
  
  try {
    // 测试字典搜索历史
    const { getSearchHistory, addSearchToHistory, clearSearchHistory } = 
      await import('../writing/historyService');
    
    // 测试添加搜索词
    addSearchToHistory('test_word_1');
    addSearchToHistory('test_word_2');
    console.log('✅ 字典搜索历史添加测试通过');
    
    // 测试获取搜索历史
    const searchHistory = getSearchHistory();
    if (Array.isArray(searchHistory) && searchHistory.length >= 2) {
      console.log('✅ 字典搜索历史获取测试通过，数量:', searchHistory.length);
    } else {
      console.log('❌ 字典搜索历史获取测试失败');
      return false;
    }
    
    // 测试清空搜索历史
    clearSearchHistory();
    const clearedHistory = getSearchHistory();
    if (clearedHistory.length === 0) {
      console.log('✅ 字典搜索历史清空测试通过');
    } else {
      console.log('❌ 字典搜索历史清空测试失败');
      return false;
    }
    
    console.log('✅ 现有历史记录服务测试通过');
    return true;
  } catch (error) {
    console.error('❌ 现有历史记录服务测试失败:', error);
    return false;
  }
};

/**
 * 测试聊天历史服务
 */
export const testChatHistoryService = async () => {
  console.log('🧪 开始测试聊天历史服务...');
  
  try {
    const { 
      startNewChatSession, 
      getCurrentSessionId, 
      saveChatSession, 
      getChatHistory 
    } = await import('../chat/chatHistoryService');
    
    // 测试开始新会话
    const sessionId = startNewChatSession();
    if (sessionId) {
      console.log('✅ 开始新聊天会话测试通过，ID:', sessionId);
    } else {
      console.log('❌ 开始新聊天会话测试失败');
      return false;
    }
    
    // 测试获取当前会话ID
    const currentSessionId = getCurrentSessionId();
    if (currentSessionId === sessionId) {
      console.log('✅ 获取当前会话ID测试通过');
    } else {
      console.log('❌ 获取当前会话ID测试失败');
      return false;
    }
    
    // 测试保存聊天会话
    const testMessages = [
      { type: 'user', content: 'Hello', timestamp: new Date().toISOString() },
      { type: 'ai', content: 'Hi there!', timestamp: new Date().toISOString() }
    ];
    
    const savedSession = saveChatSession(testMessages, sessionId, '测试对话');
    if (savedSession) {
      console.log('✅ 保存聊天会话测试通过');
    } else {
      console.log('❌ 保存聊天会话测试失败');
      return false;
    }
    
    // 测试获取聊天历史
    const chatHistory = getChatHistory();
    if (Array.isArray(chatHistory) && chatHistory.length > 0) {
      console.log('✅ 获取聊天历史测试通过，数量:', chatHistory.length);
    } else {
      console.log('❌ 获取聊天历史测试失败');
      return false;
    }
    
    console.log('✅ 聊天历史服务测试通过');
    return true;
  } catch (error) {
    console.error('❌ 聊天历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试AI分析历史服务
 */
export const testAIAnalysisService = async () => {
  console.log('🧪 开始测试AI分析历史服务...');
  
  try {
    const { 
      saveAnalysisToHistory, 
      getAnalysisHistory, 
      deleteHistoryRecord, 
      clearAllHistory 
    } = await import('../writing/historyService');
    
    // 测试保存AI分析记录
    const testAnalysis = {
      text: 'This is a test text for analysis.',
      rawAnalysis: 'AI analysis result',
      analysis: { sentiment: 'positive', language: 'en' }
    };
    
    saveAnalysisToHistory(testAnalysis);
    console.log('✅ 保存AI分析记录测试通过');
    
    // 测试获取AI分析历史
    const analysisHistory = getAnalysisHistory();
    if (Array.isArray(analysisHistory) && analysisHistory.length > 0) {
      console.log('✅ 获取AI分析历史测试通过，数量:', analysisHistory.length);
    } else {
      console.log('❌ 获取AI分析历史测试失败');
      return false;
    }
    
    // 测试删除单条记录
    if (analysisHistory.length > 0) {
      const recordToDelete = analysisHistory[0];
      const updatedHistory = deleteHistoryRecord(recordToDelete.id);
      if (updatedHistory.length === analysisHistory.length - 1) {
        console.log('✅ 删除AI分析记录测试通过');
      } else {
        console.log('❌ 删除AI分析记录测试失败');
        return false;
      }
    }
    
    // 测试清空所有历史
    clearAllHistory();
    const clearedHistory = getAnalysisHistory();
    if (clearedHistory.length === 0) {
      console.log('✅ 清空AI分析历史测试通过');
    } else {
      console.log('❌ 清空AI分析历史测试失败');
      return false;
    }
    
    console.log('✅ AI分析历史服务测试通过');
    return true;
  } catch (error) {
    console.error('❌ AI分析历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试日记服务
 */
export const testDiaryService = async () => {
  console.log('🧪 开始测试日记服务...');
  
  try {
    const { saveDiary, getDiaries } = await import('../writing/diaryService');
    
    // 测试保存日记
    const testDiary = {
      date: new Date().toISOString().split('T')[0],
      content: 'Today was a great day for testing!',
      mood: 'excited',
      weather: 'sunny'
    };
    
    const savedDiary = saveDiary(testDiary);
    if (savedDiary && savedDiary.id) {
      console.log('✅ 保存日记测试通过，ID:', savedDiary.id);
    } else {
      console.log('❌ 保存日记测试失败');
      return false;
    }
    
    // 测试获取日记
    const diaries = getDiaries();
    if (Array.isArray(diaries) && diaries.length > 0) {
      console.log('✅ 获取日记测试通过，数量:', diaries.length);
    } else {
      console.log('❌ 获取日记测试失败');
      return false;
    }
    
    console.log('✅ 日记服务测试通过');
    return true;
  } catch (error) {
    console.error('❌ 日记服务测试失败:', error);
    return false;
  }
};

/**
 * 检查localStorage中的数据
 */
export const inspectLocalStorageData = () => {
  console.log('🔍 检查localStorage中的数据...');
  
  const keys = Object.keys(localStorage);
  console.log('📋 localStorage中的键数量:', keys.length);
  
  const historyKeys = keys.filter(key => 
    key.includes('history') || 
    key.includes('chat') || 
    key.includes('diary') || 
    key.includes('writing') ||
    key.includes('analysis')
  );
  
  console.log('📚 历史记录相关的键:', historyKeys);
  
  historyKeys.forEach(key => {
    try {
      const value = localStorage.getItem(key);
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        console.log(`  ${key}: ${parsed.length} 条记录`);
      } else if (typeof parsed === 'object') {
        console.log(`  ${key}: 对象数据`);
      } else {
        console.log(`  ${key}: ${typeof parsed} 类型数据`);
      }
    } catch (error) {
      console.log(`  ${key}: 无法解析的数据`);
    }
  });
  
  return historyKeys;
};

/**
 * 运行所有本地存储测试
 */
export const runAllLocalStorageTests = async () => {
  console.log('🚀 开始运行所有本地存储测试...');
  
  const results = {
    basic: testLocalStorageBasic(),
    json: testJSONSerialization(),
    existingHistory: await testExistingHistoryServices(),
    chatHistory: await testChatHistoryService(),
    aiAnalysis: await testAIAnalysisService(),
    diary: await testDiaryService()
  };
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log('📊 本地存储测试结果汇总:');
  console.log('✅ 通过:', passed);
  console.log('❌ 失败:', total - passed);
  console.log('📈 成功率:', Math.round((passed / total) * 100) + '%');
  
  // 检查现有数据
  inspectLocalStorageData();
  
  return results;
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中
  window.testLocalStorage = {
    runAllLocalStorageTests,
    testLocalStorageBasic,
    testJSONSerialization,
    testExistingHistoryServices,
    testChatHistoryService,
    testAIAnalysisService,
    testDiaryService,
    inspectLocalStorageData
  };
  
  console.log('🧪 本地存储测试工具已加载到 window.testLocalStorage');
  console.log('使用方法: testLocalStorage.runAllLocalStorageTests()');
}
