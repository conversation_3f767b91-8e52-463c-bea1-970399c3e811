const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
import { getDoubaoApiKey } from '../user/userSettingsService.js';
import { translateToChinese } from '../ai/translationService.js';

async function fetchFromAI(data) {
    const apiKey = await getDoubaoApiKey();
    if (!apiKey) {
        throw new Error('系统API密钥未配置，请联系管理员');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorBody}`);
    }

    const result = await response.json();
    return result.choices[0].message.content;
}

import { loadPrompt } from '../ai/promptLoader.js';

// AI生成新开场白函数 - 修改为两次API调用
export const generateNewGreeting = async () => {
    try {
        // 第一次调用：生成英文开场白
        const englishGreeting = await generateEnglishGreeting();
        console.log('📥 英文开场白生成成功:', englishGreeting);
        
        // 第二次调用：翻译为中文
        const chineseTranslation = await translateToChinese(englishGreeting);
        console.log('📥 中文翻译完成:', chineseTranslation);
        
        // 返回格式化的响应
        return `${englishGreeting}\n---\n${chineseTranslation}`;
    } catch (error) {
        console.error('开场白生成失败:', error);
        throw error;
    }
};

// 生成英文开场白的函数
const generateEnglishGreeting = async () => {
    const systemPrompt = await loadPrompt('greetingPrompt');
    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: 'Generate a short, friendly greeting!' }
        ],
        temperature: 2.0,
        max_tokens: 80, // 更短的开场白
        thinking: { type: "disabled" }
    };

    return fetchFromAI(data);
};