import { loadPrompt, replacePromptVariables } from '../ai/promptLoader.js';
import { getDoubaoApiKey } from '../user/userSettingsService.js';
import { translateToChinese } from '../ai/translationService.js';

const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

async function fetchFromAI(data) {
    const apiKey = await getDoubaoApiKey();
    if (!apiKey) {
        throw new Error('系统API密钥未配置，请联系管理员');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorBody}`);
    }

    const result = await response.json();
    return result.choices[0].message.content;
}

// 生成写作分享的个性化AI回复 - 修改为两次API调用
export const generateWritingSharingResponse = async (contextData) => {
    try {
        console.log('🔄 开始生成写作分享回复...');
        
        // 第一次调用：生成英文响应
        const englishResponse = await generateEnglishWritingResponse(contextData);
        console.log('📥 英文写作分享响应生成成功:', englishResponse.substring(0, 100) + '...');
        
        // 第二次调用：翻译为中文
        const chineseTranslation = await translateToChinese(englishResponse);
        console.log('📥 中文翻译完成:', chineseTranslation.substring(0, 100) + '...');
        
        // 返回格式化的响应
        return `${englishResponse}\n---\n${chineseTranslation}`;
    } catch (error) {
        console.error('❌ 生成写作分享回复失败:', error);
        throw error;
    }
};

// 生成英文写作分享响应的函数
const generateEnglishWritingResponse = async (contextData) => {
    // 从中央加载器加载并替换变量
    let systemPrompt = await loadPrompt('writingSharingPrompt');
    systemPrompt = replacePromptVariables(systemPrompt, contextData);

    console.log('✅ 系统提示词加载完成');

    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `I'm sharing my writing with you: "${contextData.title}". Please give me an enthusiastic and personalized response!` }
        ],
        temperature: 2.0,
        max_tokens: 600,
        thinking: { type: "disabled" }
    };

    console.log('📤 发送英文生成请求...');
    return await fetchFromAI(data);
};
