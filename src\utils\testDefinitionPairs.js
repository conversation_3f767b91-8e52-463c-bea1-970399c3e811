// 释义配对显示测试工具
// 用于测试新的英文-中文释义配对显示格式

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试配对释义解析
const testDefinitionPairs = async () => {
  console.log('🧪 测试释义配对显示...');
  console.log('='.repeat(50));
  
  const testWords = ['doing', 'nothing', 'school', 'beautiful'];
  const results = {};
  
  for (const word of testWords) {
    try {
      console.log(`\n📖 测试单词: ${word}`);
      console.log('-'.repeat(30));
      
      const result = await getWordDetails(word, 'ecdict');
      
      if (result && !result.notFound) {
        console.log(`✅ ${word} 查询成功`);
        console.log(`📊 数据源: ${result.source}`);
        
        // 检查配对释义
        if (result.definitionPairs && result.definitionPairs.length > 0) {
          console.log(`🔗 配对释义数量: ${result.definitionPairs.length}`);
          
          result.definitionPairs.forEach((pair, index) => {
            console.log(`\n  ${pair.index}. 配对释义:`);
            console.log(`     🇺🇸 英文: ${pair.english}`);
            console.log(`     🇨🇳 中文: ${pair.chinese}`);
          });
          
          results[word] = {
            success: true,
            pairsCount: result.definitionPairs.length,
            hasPairs: true
          };
        } else {
          console.log('❌ 没有配对释义数据');
          
          // 检查原始数据
          if (result.definition) {
            console.log('📝 原始英文释义:');
            console.log(`  ${result.definition}`);
          }
          
          if (result.translation) {
            console.log('📝 原始中文释义:');
            console.log(`  ${result.translation}`);
          }
          
          results[word] = {
            success: true,
            pairsCount: 0,
            hasPairs: false,
            hasDefinition: !!result.definition,
            hasTranslation: !!result.translation
          };
        }
      } else {
        console.log(`❌ ${word} 查询失败`);
        console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
        
        results[word] = {
          success: false,
          error: result?.error || '未找到该单词'
        };
      }
    } catch (error) {
      console.log(`❌ ${word} 查询出错: ${error.message}`);
      results[word] = {
        success: false,
        error: error.message
      };
    }
  }
  
  // 统计结果
  console.log('\n📊 测试结果统计:');
  console.log('='.repeat(50));
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const pairsCount = Object.values(results).filter(r => r.hasPairs).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`✅ 成功查询: ${successCount}/${totalCount}`);
  console.log(`🔗 有配对释义: ${pairsCount}/${totalCount}`);
  
  Object.entries(results).forEach(([word, result]) => {
    if (result.success) {
      if (result.hasPairs) {
        console.log(`  ✅ ${word}: ${result.pairsCount} 个配对释义`);
      } else {
        console.log(`  ⚠️  ${word}: 无配对释义 (英文: ${result.hasDefinition ? '有' : '无'}, 中文: ${result.hasTranslation ? '有' : '无'})`);
      }
    } else {
      console.log(`  ❌ ${word}: ${result.error}`);
    }
  });
  
  return results;
};

// 测试特定单词的详细配对
const testSpecificWord = async (word) => {
  console.log(`🧪 测试单词 "${word}" 的详细配对...`);
  console.log('='.repeat(50));
  
  try {
    const result = await getWordDetails(word, 'ecdict');
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log(`📖 单词: ${result.word}`);
      console.log(`🔊 音标: ${result.phonetic || '无'}`);
      console.log(`📊 数据源: ${result.source}`);
      
      if (result.definitionPairs && result.definitionPairs.length > 0) {
        console.log(`\n🔗 配对释义 (${result.definitionPairs.length} 个):`);
        console.log('-'.repeat(40));
        
        result.definitionPairs.forEach((pair, index) => {
          console.log(`\n${pair.index}. 释义对:`);
          console.log(`   🇺🇸 英文: ${pair.english}`);
          console.log(`   🇨🇳 中文: ${pair.chinese}`);
        });
        
        // 检查UI显示效果
        console.log('\n🎨 UI显示效果预览:');
        console.log('-'.repeat(40));
        console.log('释义：');
        
        result.definitionPairs.forEach((pair, index) => {
          if (pair.english) {
            console.log(`${pair.index}. 英文释义：`);
            console.log(`    ${pair.english}`);
          }
          if (pair.chinese) {
            console.log(`${pair.index}. 中文释义：`);
            console.log(`    ${pair.chinese}`);
          }
        });
        
        return result;
      } else {
        console.log('❌ 没有配对释义数据');
        return null;
      }
    } else {
      console.log('❌ 查询失败');
      console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return null;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始释义配对显示测试...');
  console.log('='.repeat(60));
  
  try {
    // 测试多个单词
    const results = await testDefinitionPairs();
    
    // 测试特定单词
    console.log('\n🔍 详细测试 "doing" 单词:');
    await testSpecificWord('doing');
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    return results;
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testDefinitionPairs,
  testSpecificWord,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testDefinitionPairs = {
    testDefinitionPairs,
    testSpecificWord,
    runAllTests
  };
  
  console.log('🧪 释义配对显示测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testDefinitionPairs.runAllTests() // 运行所有测试');
  console.log('  - window.testDefinitionPairs.testDefinitionPairs() // 测试多个单词');
  console.log('  - window.testDefinitionPairs.testSpecificWord("doing") // 测试特定单词');
}