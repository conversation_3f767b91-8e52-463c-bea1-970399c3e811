# 修复重复键错误

## 问题描述

在打开写作历史记录时，控制台出现以下React错误：

```
Encountered two children with the same key, `[number]`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted - the behavior is unsupported and could change in a future version.
```

## 问题原因

问题出现在 `src/services/writing/historyService.js` 中，AI分析历史记录使用 `Date.now()` 作为ID：

```javascript
id: Date.now(),
```

当在同一毫秒内创建多个记录时，`Date.now()` 会返回相同的值，导致多个记录具有相同的ID，从而在React渲染时产生重复的key错误。

## 解决方案

### 1. 创建唯一ID生成器

创建了 `src/utils/idGenerator.js` 文件，提供多种ID生成策略：

```javascript
// 生成唯一ID，结合时间戳和随机数
export const generateUniqueId = () => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// 生成简单唯一ID
export const generateSimpleId = () => {
  return Math.random().toString(36).substr(2, 9);
};

// 生成基于时间戳的ID
export const generateTimestampId = () => {
  return Date.now();
};

// 生成递增ID
let incrementId = 0;
export const generateIncrementId = () => {
  return ++incrementId;
};
```

### 2. 更新相关文件

更新了以下文件中的ID生成逻辑：

- `src/services/writing/historyService.js` - AI分析历史记录
- `src/hooks/useChat.js` - 聊天消息ID
- `src/pages/ChatPage.jsx` - 聊天页面消息ID

### 3. 修复前后对比

**修复前：**
```javascript
const newRecord = {
  id: Date.now(), // 可能产生重复ID
  timestamp: new Date().toISOString(),
  text,
  rawAnalysis,
  analysis,
};
```

**修复后：**
```javascript
import { generateUniqueId } from '../../utils/idGenerator';

const newRecord = {
  id: generateUniqueId(), // 确保唯一性
  timestamp: new Date().toISOString(),
  text,
  rawAnalysis,
  analysis,
};
```

## 测试验证

创建了测试文件 `src/utils/idGenerator.test.js` 来验证ID生成器的唯一性：

```javascript
test('generateUniqueId should generate unique IDs', () => {
  const ids = new Set();
  const count = 1000;
  
  for (let i = 0; i < count; i++) {
    const id = generateUniqueId();
    expect(ids.has(id)).toBe(false);
    ids.add(id);
  }
  
  expect(ids.size).toBe(count);
});
```

## 影响范围

此修复解决了以下问题：

1. **写作历史记录**：不再出现重复键错误
2. **聊天消息**：确保每条消息都有唯一ID
3. **AI分析记录**：避免重复ID导致的渲染问题
4. **系统稳定性**：提高React组件的渲染稳定性

## 使用建议

1. **新功能开发**：使用 `generateUniqueId()` 替代 `Date.now()`
2. **需要排序的场景**：使用 `generateTimestampId()`
3. **需要连续ID的场景**：使用 `generateIncrementId()`
4. **简单场景**：使用 `generateSimpleId()`

## 注意事项

1. 修复后的ID格式为 `timestamp-randomString`，如 `1703123456789-abc123def`
2. 现有数据中的旧ID格式不会受到影响
3. 新生成的记录将使用新的ID格式
4. 建议在数据迁移时考虑ID格式的统一性

## 验证方法

1. 打开写作历史记录，检查控制台是否还有重复键错误
2. 快速创建多个AI分析记录，验证ID的唯一性
3. 运行测试：`npm test src/utils/idGenerator.test.js`

---

此修复确保了系统中所有动态生成的ID都具有唯一性，解决了React渲染时的重复键错误问题。
