/**
 * 提示词测试工具
 * 用于验证提示词是否正确加载
 */

import { loadPrompt } from '../ai/promptLoader.js';

/**
 * 测试所有提示词加载
 */
export const testAllPrompts = async () => {
  console.log('🧪 开始测试提示词加载...');
  
  const promptNames = [
    'chatResponsePrompt',
    'writingSharingPrompt', 
    'greetingPrompt',
    'diaryPrompt',
    'expressionSuggestionPrompt',
    'writingAnalysisPrompt'
  ];
  
  const results = {};
  
  for (const promptName of promptNames) {
    try {
      console.log(`\n📝 测试 ${promptName}...`);
      const prompt = await loadPrompt(promptName);
      
      if (prompt) {
        // 检查是否包含关键内容
        const hasAlex = prompt.includes('Alex');
        const hasBotanist = prompt.includes('botanist') || prompt.includes('Botanist');
        const hasCostaRica = prompt.includes('Costa Rica');
        const hasEmoji = prompt.includes('🌿') || prompt.includes('📸');
        const hasFormat = prompt.includes('---');
        
        results[promptName] = {
          success: true,
          length: prompt.length,
          hasAlex,
          hasBotanist,
          hasCostaRica,
          hasEmoji,
          hasFormat,
          preview: prompt.substring(0, 200) + '...'
        };
        
        console.log(`✅ ${promptName} 加载成功`);
        console.log(`   长度: ${prompt.length} 字符`);
        console.log(`   包含Alex: ${hasAlex ? '✅' : '❌'}`);
        console.log(`   包含植物学家: ${hasBotantist ? '✅' : '❌'}`);
        console.log(`   包含哥斯达黎加: ${hasCostaRica ? '✅' : '❌'}`);
        console.log(`   包含表情符号: ${hasEmoji ? '✅' : '❌'}`);
        console.log(`   包含格式分隔符: ${hasFormat ? '✅' : '❌'}`);
        console.log(`   预览: ${prompt.substring(0, 100)}...`);
      } else {
        results[promptName] = { success: false, error: '提示词为空' };
        console.log(`❌ ${promptName} 加载失败: 提示词为空`);
      }
    } catch (error) {
      results[promptName] = { success: false, error: error.message };
      console.log(`❌ ${promptName} 加载失败: ${error.message}`);
    }
  }
  
  // 输出测试结果汇总
  console.log('\n📊 提示词测试结果汇总:');
  const successCount = Object.values(results).filter(r => r.success).length;
  const totalCount = promptNames.length;
  
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`📈 成功率: ${Math.round((successCount / totalCount) * 100)}%`);
  
  // 检查关键提示词
  const criticalPrompts = ['chatResponsePrompt', 'writingSharingPrompt'];
  const criticalSuccess = criticalPrompts.every(name => 
    results[name] && results[name].success
  );
  
  if (criticalSuccess) {
    console.log('🎉 关键提示词加载成功！');
  } else {
    console.log('⚠️ 部分关键提示词加载失败');
  }
  
  return results;
};

/**
 * 测试特定提示词
 */
export const testSpecificPrompt = async (promptName) => {
  try {
    console.log(`🧪 测试提示词: ${promptName}`);
    const prompt = await loadPrompt(promptName);
    
    if (prompt) {
      console.log(`✅ 加载成功，长度: ${prompt.length} 字符`);
      console.log(`📝 内容预览:`);
      console.log(prompt.substring(0, 500) + '...');
      
      // 检查关键特征
      const checks = {
        '包含Alex': prompt.includes('Alex'),
        '包含植物学家': prompt.includes('botanist') || prompt.includes('Botanist'),
        '包含哥斯达黎加': prompt.includes('Costa Rica'),
        '包含表情符号': prompt.includes('🌿') || prompt.includes('📸'),
        '包含格式要求': prompt.includes('---') || prompt.includes('RESPONSE FORMAT'),
        '包含详细背景': prompt.includes('Monteverde') || prompt.includes('cloud forest')
      };
      
      console.log('\n🔍 内容检查结果:');
      Object.entries(checks).forEach(([check, result]) => {
        console.log(`   ${check}: ${result ? '✅' : '❌'}`);
      });
      
      return { success: true, prompt, checks };
    } else {
      console.log('❌ 提示词为空');
      return { success: false, error: '提示词为空' };
    }
  } catch (error) {
    console.error(`❌ 测试失败: ${error.message}`);
    return { success: false, error: error.message };
  }
};

/**
 * 比较提示词版本
 */
export const comparePromptVersions = async () => {
  console.log('🔍 比较提示词版本...');
  
  // 测试主要提示词
  const chatPrompt = await loadPrompt('chatResponsePrompt');
  const writingPrompt = await loadPrompt('writingSharingPrompt');
  
  if (chatPrompt && writingPrompt) {
    console.log('\n📊 提示词内容分析:');
    
    // 检查是否包含新的详细背景
    const hasDetailedBackstory = chatPrompt.includes('Monteverde') && 
                                chatPrompt.includes('Oregon') && 
                                chatPrompt.includes('grandfather');
    
    const hasEmojiGuidance = chatPrompt.includes('🌿') && 
                            chatPrompt.includes('📸') && 
                            chatPrompt.includes('emoji');
    
    const hasFormatEnforcement = chatPrompt.includes('CRITICAL') && 
                                prompt.includes('WARNING') && 
                                prompt.includes('FAILURE');
    
    console.log(`   包含详细背景故事: ${hasDetailedBackstory ? '✅' : '❌'}`);
    console.log(`   包含表情符号指导: ${hasEmojiGuidance ? '✅' : '❌'}`);
    console.log(`   包含格式强制要求: ${hasFormatEnforcement ? '✅' : '❌'}`);
    
    if (hasDetailedBackstory && hasEmojiGuidance) {
      console.log('🎉 提示词已更新到最新版本！');
    } else {
      console.log('⚠️ 提示词可能还是旧版本');
    }
  }
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中
  window.testPrompts = {
    testAllPrompts,
    testSpecificPrompt,
    comparePromptVersions
  };
  
  console.log('🧪 提示词测试工具已加载到 window.testPrompts');
  console.log('使用方法:');
  console.log('  testPrompts.testAllPrompts() - 测试所有提示词');
  console.log('  testPrompts.testSpecificPrompt("chatResponsePrompt") - 测试特定提示词');
  console.log('  testPrompts.comparePromptVersions() - 比较提示词版本');
}
