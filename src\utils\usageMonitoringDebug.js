/**
 * 用量监控调试工具
 * 为主应用提供实时用量监控调试功能
 */

import { onAuthChange } from '../services/auth/authService';
import {
    checkApiUsageLimit,
    getSystemConfig,
    getUserSettings,
    recordApiUsage,
    updateUserSettings
} from '../services/user/userSettingsService';

/**
 * 获取当前用户ID
 */
const getCurrentUserId = () => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthChange((user) => {
      unsubscribe();
      resolve(user ? user.uid : null);
    });
  });
};

/**
 * 实时显示用量状态
 */
const showUsageStatus = async () => {
  console.log('📊 实时用量状态监控');
  console.log('='.repeat(50));

  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ 用户未登录');
      return;
    }

    console.log('👤 用户ID:', userId);

    // 获取用户设置
    const settings = await getUserSettings(userId);
    console.log('⚙️ 用户设置:', {
      subscription: settings.subscription,
      usageToday: settings.usageToday,
      lastRequestDate: settings.lastRequestDate
    });

    // 获取系统配置
    const systemConfig = await getSystemConfig();
    console.log('🔧 系统配置:', {
      freeUserLimits: systemConfig.freeUserLimits,
      paidUserLimits: systemConfig.paidUserLimits
    });

    // 检查各类API使用量
    const [chatUsage, writingUsage] = await Promise.all([
      checkApiUsageLimit(userId, 'chat'),
      checkApiUsageLimit(userId, 'writing')
    ]);

    console.log('💬 AI对话使用量:', {
      当前使用: `${chatUsage.currentUsage}/${chatUsage.maxRequests === -1 ? '无限制' : chatUsage.maxRequests}`,
      剩余次数: chatUsage.remainingRequests === -1 ? '无限制' : chatUsage.remainingRequests,
      是否可用: chatUsage.canUse ? '✅' : '❌',
      付费用户: chatUsage.isPaidUser ? '✅' : '❌'
    });

    console.log('✍️ 写作纠错使用量:', {
      当前使用: `${writingUsage.currentUsage}/${writingUsage.maxRequests === -1 ? '无限制' : writingUsage.maxRequests}`,
      剩余次数: writingUsage.remainingRequests === -1 ? '无限制' : writingUsage.remainingRequests,
      是否可用: writingUsage.canUse ? '✅' : '❌',
      付费用户: writingUsage.isPaidUser ? '✅' : '❌'
    });

    return {
      userId,
      settings,
      systemConfig,
      chatUsage,
      writingUsage
    };

  } catch (error) {
    console.error('❌ 获取用量状态失败:', error);
    return null;
  }
};

/**
 * 模拟API调用并记录用量
 */
const simulateApiCall = async (apiType = 'chat') => {
  console.log(`🧪 模拟 ${apiType} API调用...`);

  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ 用户未登录');
      return false;
    }

    // 检查是否可以使用
    const usageCheck = await checkApiUsageLimit(userId, apiType);
    console.log('📋 使用前检查:', usageCheck);

    if (!usageCheck.canUse) {
      console.log('❌ 已达到使用限制，无法调用API');
      return false;
    }

    // 记录API使用
    await recordApiUsage(userId, apiType);
    console.log('✅ API使用记录成功');

    // 再次检查使用量
    const afterUsage = await checkApiUsageLimit(userId, apiType);
    console.log('📋 使用后检查:', afterUsage);

    return true;

  } catch (error) {
    console.error('❌ 模拟API调用失败:', error);
    return false;
  }
};

/**
 * 重置今日使用量（仅用于测试）
 */
const resetTodayUsage = async () => {
  console.log('🔄 重置今日使用量（测试功能）...');

  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ 用户未登录');
      return false;
    }

    const today = new Date().toISOString().split('T')[0];

    await updateUserSettings(userId, {
      usageToday: {
        chatRequests: 0,
        writingAnalysis: 0,
        lastRequestDate: today
      }
    });

    console.log('✅ 今日使用量已重置');
    return true;

  } catch (error) {
    console.error('❌ 重置使用量失败:', error);
    return false;
  }
};

/**
 * 设置用户为付费用户（仅用于测试）
 */
const setPaidUser = async (isPaid = true) => {
  console.log(`🔄 设置用户为${isPaid ? '付费' : '免费'}用户（测试功能）...`);

  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ 用户未登录');
      return false;
    }

    await updateUserSettings(userId, {
      subscription: {
        plan: isPaid ? 'basic' : 'free',
        status: 'active',
        startDate: new Date().toISOString(),
        endDate: isPaid ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() : null
      }
    });

    console.log(`✅ 用户已设置为${isPaid ? '付费' : '免费'}用户`);
    return true;

  } catch (error) {
    console.error('❌ 设置用户类型失败:', error);
    return false;
  }
};

/**
 * 简洁的用量状态显示
 */
const quickStatus = async () => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ 用户未登录');
      return;
    }

    const [chatUsage, writingUsage] = await Promise.all([
      checkApiUsageLimit(userId, 'chat'),
      checkApiUsageLimit(userId, 'writing')
    ]);

    console.log('📊 当前用量状态:');
    console.log(`💬 AI对话: ${chatUsage.currentUsage}/${chatUsage.maxRequests === -1 ? '∞' : chatUsage.maxRequests} ${chatUsage.canUse ? '✅' : '❌'}`);
    console.log(`✍️ 写作纠错: ${writingUsage.currentUsage}/${writingUsage.maxRequests === -1 ? '∞' : writingUsage.maxRequests} ${writingUsage.canUse ? '✅' : '❌'}`);
    console.log(`👑 用户类型: ${chatUsage.isPaidUser ? '付费' : '免费'}`);

    return { chatUsage, writingUsage };
  } catch (error) {
    console.error('❌ 获取状态失败:', error);
  }
};

/**
 * 简化的快速测试
 */
const quickTest = async () => {
  console.log('🚀 快速用量测试');
  console.log('='.repeat(30));

  await quickStatus();

  console.log('\n🧪 模拟调用测试:');
  await simulateApiCall('chat');
  await simulateApiCall('writing');

  console.log('\n📊 测试后状态:');
  await quickStatus();

  console.log('\n✅ 测试完成！');
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.usageDebug = {
    status: quickStatus,
    test: quickTest,
    simulate: simulateApiCall,
    reset: resetTodayUsage,
    setPaid: setPaidUser,
    full: showUsageStatus
  };

  console.log('🔧 用量监控调试工具已加载');
  console.log('💡 简洁命令:');
  console.log('  - await usageDebug.status() // 查看当前状态');
  console.log('  - await usageDebug.test() // 快速测试');
  console.log('  - await usageDebug.simulate("chat") // 模拟AI对话');
  console.log('  - await usageDebug.simulate("writing") // 模拟写作纠错');
  console.log('  - await usageDebug.reset() // 重置今日使用量');
  console.log('  - await usageDebug.setPaid(true/false) // 设置用户类型');
}

export {
    getCurrentUserId,
    quickStatus,
    quickTest,
    resetTodayUsage,
    setPaidUser,
    showUsageStatus,
    simulateApiCall
};
