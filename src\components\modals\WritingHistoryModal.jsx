import React, { useState, useEffect } from 'react';
import { FileText, Share } from 'lucide-react';

const WritingHistoryModal = ({ isOpen, onClose, onShareWriting, isDarkMode }) => {
  const [writingHistory, setWritingHistory] = useState([]);
  const [selectedWriting, setSelectedWriting] = useState(null);

  useEffect(() => {
    if (isOpen) {
      try {
        const history = localStorage.getItem('writing_history');
        const parsedHistory = history ? JSON.parse(history) : [];
        setWritingHistory(parsedHistory.slice(0, 10));
      } catch (error) {
        console.error('获取写作历史失败:', error);
        setWritingHistory([]);
      }
    }
  }, [isOpen]);

  const handleShare = () => {
    if (selectedWriting) {
      onShareWriting(selectedWriting);
      setSelectedWriting(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ backgroundColor: 'var(--modal-backdrop)' }}
    >
      <div
        className="w-full max-w-2xl max-h-[80vh] rounded-2xl shadow-2xl overflow-hidden flex flex-col"
        style={{
          backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
        }}
      >
        <div
          className="flex items-center justify-between p-6 border-b"
          style={{ borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8' }}
        >
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6" style={{ color: isDarkMode ? '#D2691E' : '#166534' }} />
            <h2
              className="text-2xl font-bold"
              style={{
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}
            >
              分享写作历史
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg transition-colors duration-200"
            style={{
              color: isDarkMode ? '#C4B59A' : '#8B4513',
              backgroundColor: 'transparent'
            }}
          >
            ✕
          </button>
        </div>

        <div 
          className="flex-1 overflow-y-auto custom-scrollbar p-6"
          style={{ 
            maxHeight: 'calc(80vh - 140px)',
            minHeight: '200px'
          }}
        >
          {writingHistory.length === 0 ? (
            <div
              className="text-center py-12"
              style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
            >
              <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg">暂无写作历史</p>
              <p className="text-sm mt-2">完成写作后，历史记录会显示在这里</p>
            </div>
          ) : (
            <div className="space-y-3">
              <p
                className="text-sm mb-4"
                style={{
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  fontFamily: 'Georgia, "Noto Serif SC", serif'
                }}
              >
                选择一篇写作作品分享给AI，它将基于你的写作内容进行更有针对性的对话：
              </p>

              {writingHistory.map((writing, index) => (
                <div
                  key={writing.id || `writing-${index}-${writing.timestamp || Date.now()}`}
                  className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${selectedWriting === writing ? 'border-opacity-100' : 'border-opacity-30'}`}
                  style={{
                    backgroundColor: selectedWriting === writing
                      ? (isDarkMode ? '#332B22' : '#FFFEF7')
                      : (isDarkMode ? '#1A1611' : '#F5EFE6'),
                    borderColor: selectedWriting === writing
                      ? (isDarkMode ? '#D2691E' : '#166534')
                      : (isDarkMode ? '#4A3F35' : '#E6D7B8')
                  }}
                  onClick={() => setSelectedWriting(writing)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3
                        className="font-medium mb-2"
                        style={{
                          color: isDarkMode ? '#E8DCC6' : '#5D4037',
                          fontFamily: 'Georgia, "Noto Serif SC", serif'
                        }}
                      >
                        {writing.title || '未命名文档'}
                      </h3>
                      <p
                        className="text-sm mb-2 line-clamp-2"
                        style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
                      >
                        {typeof writing.content === 'string' ? `${writing.content.substring(0, 100)}...` : '[内容格式错误]'}
                      </p>
                      <div
                        className="text-xs flex items-center gap-4"
                        style={{ color: isDarkMode ? '#A0916B' : '#9B6B47' }}
                      >
                        <span>{typeof writing.content === 'string' ? writing.content.split(/\s+/).length : 0} 词</span>
                        <span>{new Date(writing.timestamp).toLocaleDateString()}</span>
                      </div>
                    </div>
                    {selectedWriting === writing && (
                      <Share
                        className="w-5 h-5 ml-3"
                        style={{ color: isDarkMode ? '#D2691E' : '#166534' }}
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {writingHistory.length > 0 && (
          <div
            className="flex items-center justify-end gap-3 p-6 border-t"
            style={{ borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8' }}
          >
            <button
              onClick={onClose}
              className="px-6 py-2 rounded-xl transition-colors duration-200"
              style={{
                color: isDarkMode ? '#C4B59A' : '#8B4513',
                backgroundColor: 'transparent',
                border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
              }}
            >
              取消
            </button>
            <button
              onClick={handleShare}
              disabled={!selectedWriting}
              className="px-6 py-2 rounded-xl transition-colors duration-200 disabled:opacity-50"
              style={{
                backgroundColor: selectedWriting
                  ? (isDarkMode ? '#D2691E' : '#166534')
                  : (isDarkMode ? '#4A3F35' : '#E6D7B8'),
                color: '#FEFCF5',
                border: 'none'
              }}
            >
              分享到聊天
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WritingHistoryModal;
