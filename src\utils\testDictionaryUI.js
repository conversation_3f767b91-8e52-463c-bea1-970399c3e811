// 词典UI布局测试工具
// 用于测试新的整合式词典显示布局

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试单词列表
const TEST_WORDS = [
  'school',
  'nothing', 
  'beautiful',
  'computer',
  'education'
];

// 测试两种词典服务的布局
const testBothDictionaryLayouts = async () => {
  console.log('🧪 测试两种词典服务的布局...');
  console.log('='.repeat(60));
  
  const testWord = 'school';
  
  try {
    // 测试 ECDICT 布局
    console.log('\n📚 测试 ECDICT 英汉字典布局:');
    console.log('-'.repeat(40));
    const ecdictResult = await getWordDetails(testWord, 'ecdict');
    
    if (ecdictResult && !ecdictResult.notFound) {
      console.log('✅ ECDICT 查询成功');
      console.log('📖 数据源:', ecdictResult.source);
      console.log('🔤 单词:', ecdictResult.word);
      
      if (ecdictResult.definition) {
        console.log('🇺🇸 英文释义:', ecdictResult.definition);
      }
      
      if (ecdictResult.translation) {
        console.log('🇨🇳 中文释义:', ecdictResult.translation);
      }
      
      console.log('🎨 布局类型: 英汉字典布局');
      console.log('  - 整合式释义显示');
      console.log('  - 中英文分离显示');
      console.log('  - 包含考试标签和词频信息');
    } else {
      console.log('❌ ECDICT 查询失败');
    }
    
    // 测试 Free Dictionary 布局
    console.log('\n📚 测试 Free Dictionary 英文字典布局:');
    console.log('-'.repeat(40));
    const freeDictResult = await getWordDetails(testWord, 'free_dictionary');
    
    if (freeDictResult && !freeDictResult.notFound) {
      console.log('✅ Free Dictionary 查询成功');
      console.log('📖 数据源:', freeDictResult.source);
      console.log('🔤 单词:', freeDictResult.word);
      
      if (freeDictResult.meanings && freeDictResult.meanings.length > 0) {
        console.log('📝 词义数量:', freeDictResult.meanings.length);
        freeDictResult.meanings.forEach((meaning, index) => {
          console.log(`  ${index + 1}. ${meaning.partOfSpeech}: ${meaning.definitions.length} 个定义`);
        });
      }
      
      console.log('🎨 布局类型: 英文字典布局');
      console.log('  - 按词性分组显示');
      console.log('  - 详细的定义和例句');
      console.log('  - 同义词和反义词标签');
    } else {
      console.log('❌ Free Dictionary 查询失败');
    }
    
    return {
      ecdict: ecdictResult,
      freeDictionary: freeDictResult
    };
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 测试单个单词的UI显示
const testWordUI = async (word) => {
  console.log(`🧪 测试单词 "${word}" 的UI显示...`);
  console.log('='.repeat(50));
  
  try {
    const startTime = performance.now();
    const result = await getWordDetails(word, 'ecdict');
    const endTime = performance.now();
    
    const queryTime = endTime - startTime;
    
    console.log(`⏱️ 查询时间: ${queryTime.toFixed(2)}ms`);
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log('📖 数据源:', result.source);
      console.log('🔤 单词:', result.word);
      
      if (result.phonetic) {
        console.log('🔊 音标:', result.phonetic);
      }
      
      // 检查释义数据
      console.log('\n📝 释义数据:');
      if (result.definition) {
        console.log('🇺🇸 英文释义:');
        console.log('  ', result.definition);
      }
      
      if (result.translation) {
        console.log('🇨🇳 中文释义:');
        console.log('  ', result.translation);
      }
      
      // 检查换行符处理
      if (result.definition && result.definition.includes('\\n')) {
        console.log('⚠️ 英文释义包含未处理的换行符');
      }
      
      if (result.translation && result.translation.includes('\\n')) {
        console.log('⚠️ 中文释义包含未处理的换行符');
      }
      
      // 检查其他信息
      if (result.tags && result.tags.length > 0) {
        console.log('🏷️ 考试标签:', result.tags.join(', '));
      }
      
      if (result.collins > 0) {
        console.log('⭐ 柯林斯星级:', result.collins);
      }
      
      if (result.oxford) {
        console.log('📚 牛津3000: 是');
      }
      
      if (result.bnc > 0) {
        console.log('📊 BNC词频:', result.bnc);
      }
      
      if (result.frq > 0) {
        console.log('📈 当代词频:', result.frq);
      }
      
      if (result.exchange && Object.keys(result.exchange).length > 0) {
        console.log('🔄 词形变化:', result.exchange);
      }
      
      // 检查UI布局数据
      console.log('\n🎨 UI布局数据:');
      console.log('  - 有英文释义:', !!result.definition);
      console.log('  - 有中文释义:', !!result.translation);
      console.log('  - 有词义列表:', !!(result.meanings && result.meanings.length > 0));
      console.log('  - 应该显示整合释义:', !!(result.definition || result.translation));
      console.log('  - 应该显示词义列表:', !!(result.meanings && result.meanings.length > 0 && !result.definition && !result.translation));
      
      return true;
    } else {
      console.log('❌ 查询失败');
      console.log('📝 错误信息:', result?.error || '未找到该单词');
      return false;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return false;
  }
};

// 测试所有单词的UI显示
const testAllWordsUI = async () => {
  console.log('🧪 测试所有单词的UI显示...');
  console.log('='.repeat(60));
  
  const results = {};
  
  for (const word of TEST_WORDS) {
    console.log(`\n测试单词: ${word}`);
    const success = await testWordUI(word);
    results[word] = success;
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(60));
  
  const successCount = Object.values(results).filter(r => r).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`总体成功率: ${successCount}/${totalCount} (${((successCount/totalCount)*100).toFixed(1)}%)`);
  
  for (const [word, success] of Object.entries(results)) {
    console.log(`${success ? '✅' : '❌'} ${word}`);
  }
  
  return results;
};

// 测试换行符处理
const testNewlineHandling = async () => {
  console.log('🧪 测试换行符处理...');
  console.log('='.repeat(50));
  
  const testWord = 'school';
  
  try {
    const result = await getWordDetails(testWord, 'ecdict');
    
    if (result && !result.notFound) {
      console.log('测试单词:', testWord);
      
      if (result.definition) {
        console.log('\n英文释义原始数据:');
        console.log(JSON.stringify(result.definition));
        
        console.log('\n英文释义显示效果:');
        console.log(result.definition);
        
        if (result.definition.includes('\\n')) {
          console.log('⚠️ 包含 \\n 字符，需要处理');
        } else {
          console.log('✅ 换行符处理正常');
        }
      }
      
      if (result.translation) {
        console.log('\n中文释义原始数据:');
        console.log(JSON.stringify(result.translation));
        
        console.log('\n中文释义显示效果:');
        console.log(result.translation);
        
        if (result.translation.includes('\\n')) {
          console.log('⚠️ 包含 \\n 字符，需要处理');
        } else {
          console.log('✅ 换行符处理正常');
        }
      }
    }
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
};

// 运行所有UI测试
const runAllUITests = async () => {
  console.log('🚀 开始词典UI布局测试...');
  console.log('='.repeat(70));
  
  try {
    // 测试两种词典布局
    const layoutResults = await testBothDictionaryLayouts();
    
    // 测试所有单词
    const wordResults = await testAllWordsUI();
    
    // 测试换行符处理
    await testNewlineHandling();
    
    console.log('\n🎯 UI测试完成！');
    console.log('='.repeat(70));
    
    return {
      layoutResults,
      wordResults
    };
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testBothDictionaryLayouts,
  testWordUI,
  testAllWordsUI,
  testNewlineHandling,
  runAllUITests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testDictionaryUI = {
    testBothDictionaryLayouts,
    testWordUI,
    testAllWordsUI,
    testNewlineHandling,
    runAllUITests
  };
  
  console.log('🧪 词典UI布局测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testDictionaryUI.runAllUITests() // 运行所有UI测试');
  console.log('  - window.testDictionaryUI.testBothDictionaryLayouts() // 测试两种词典布局');
  console.log('  - window.testDictionaryUI.testWordUI("school") // 测试特定单词UI');
  console.log('  - window.testDictionaryUI.testNewlineHandling() // 测试换行符处理');
}
