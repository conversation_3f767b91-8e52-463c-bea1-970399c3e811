/* eslint-disable */
/* stylelint-disable */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Noto Serif Simplified Chinese 字体 - 变量字体 */
@font-face {
  font-family: 'Noto Serif SC';
  src: url('/fonts/Noto_Serif_SC/NotoSerifSC-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 200 900; /* 支持从ExtraLight到Black的所有字重 */
  font-style: normal;
  font-display: swap;
}

/* 为不支持变量字体的浏览器提供静态字体备选 */
@font-face {
  font-family: 'Noto Serif SC';
  src: url('/fonts/Noto_Serif_SC/NotoSerifSC-VariableFont_wght.ttf') format('truetype');
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/fonts/Noto_Serif_SC/static/NotoSerifSC-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* stylelint-disable */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* stylelint-enable */

:root {
  /* 浅色模式变量 */
  --color-bg-primary: #F5EFE6;
  --color-bg-secondary: #FEFCF5;
  --color-bg-tertiary: #FFFEF7;
  --color-bg-accent: #F0E6D2;
  --color-bg-hover: #E6D7B8;

  --color-text-primary: #5D4037;
  --color-text-secondary: #8B4513;
  --color-text-muted: #4E443C;

  --color-accent-red: #B91C1C;
  --color-accent-red-hover: #991B1B;
  --color-accent-green: #166534;
  --color-accent-green-hover: #2D5A3D;
  --color-accent-blue: #1E40AF;
  --color-accent-yellow: #92400E;

  /* 日记组件样式 */
  --diary-bg: #F9F7F4;
  --diary-content-bg: #FFFEF7;
  --diary-translation-bg: #F0E6D2;

  --color-border: #E5E7EB;
  --color-border-accent: #F0E6D2;

  --color-suggestion-grammar: #991B1B;
  --color-suggestion-style: #1E40AF;
  --color-suggestion-clarity: #92400E;
  --color-suggestion-grammar-bg: #FEF2F2;
  --color-suggestion-style-bg: #DBEAFE;
  --color-suggestion-clarity-bg: #FEF3C7;
  --color-suggestion-green-bg: #E6F0E2;

  --modal-backdrop: rgba(93, 64, 55, 0.4);
}

.dark {
  /* 暗色模式变量 */
  --color-bg-primary: #1A1611;
  --color-bg-secondary: #2A241D;
  --color-bg-tertiary: #332B22;
  --color-bg-accent: #4A3F35;
  --color-bg-hover: #5A4F45;

  --color-text-primary: #E8DCC6;
  --color-text-secondary: #C4B59A;
  --color-text-muted: #8B7D6B;

  --color-accent-red: #D2691E;
  --color-accent-red-hover: #B8591A;
  --color-accent-green: #D2691E;
  --color-accent-green-hover: #B8591A;
  --color-accent-blue: #4682B4;
  --color-accent-yellow: #DAA520;

  --color-border: #4A3F35;
  --color-border-accent: #5A4F45;

  --color-suggestion-grammar: #D2691E;
  --color-suggestion-style: #4682B4;
  --color-suggestion-clarity: #DAA520;
  --color-suggestion-grammar-bg: rgba(210, 105, 30, 0.2);
  --color-suggestion-style-bg: rgba(70, 130, 180, 0.2);
  --color-suggestion-clarity-bg: rgba(218, 165, 32, 0.2);
  --color-suggestion-green-bg: rgba(210, 105, 30, 0.2);

  --modal-backdrop: rgba(26, 22, 17, 0.6);
}

/* 语音播放按钮样式 */
.voice-play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.voice-play-btn.default {
  background-color: var(--color-bg-accent);
  color: var(--color-text-secondary);
  border-color: var(--color-border-accent);
}

.voice-play-btn.default:hover:not(:disabled) {
  background-color: var(--color-accent-green);
  color: #FEFCF5;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(22, 101, 52, 0.3);
}

.voice-play-btn.playing {
  background-color: var(--color-accent-red);
  color: #FEFCF5;
  border-color: var(--color-accent-red);
  animation: pulse-playing 2s infinite;
}

.voice-play-btn.playing:hover:not(:disabled) {
  background-color: var(--color-accent-red-hover);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(185, 28, 28, 0.4);
}

.voice-play-btn.unsupported {
  background-color: var(--color-bg-hover);
  color: var(--color-text-muted);
  cursor: not-allowed;
  opacity: 0.5;
}

.voice-play-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 播放中的脉冲动画 */
@keyframes pulse-playing {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(185, 28, 28, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(185, 28, 28, 0.1);
  }
}

/* 翻译按钮样式 */
.translate-btn {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  border: 1px solid;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-accent);
  color: var(--color-text-secondary);
  border-color: var(--color-border-accent);
}

.translate-btn:hover:not(:disabled) {
  background-color: var(--color-accent-green);
  color: #FEFCF5;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(22, 101, 52, 0.3);
}

.translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.translate-btn.loading {
  opacity: 0.6;
  cursor: not-allowed;
}

@layer base {
  html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
  }

  body {
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    font-feature-settings: "rlig" 1, "calt" 1;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  #root {
    width: 100%;
    height: 100%;
  }

  /* 隐藏整个页面的滚动条 */
  html {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  html::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  body {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* 动画定义 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 登录界面特定样式 */
.auth-modal-content {
  position: relative;
  overflow: hidden;
}

.auth-header {
  position: relative;
}

.auth-icon {
  position: relative;
  overflow: hidden;
}

.auth-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.auth-icon:hover::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 输入框样式增强 */
.themed-input {
  position: relative;
}

.themed-input::placeholder {
  transition: all 0.3s ease;
}

.themed-input:focus::placeholder {
  transform: translateY(-2px);
  opacity: 0.7;
}

/* 按钮悬停效果 */
.themed-button {
  position: relative;
  overflow: hidden;
}

.themed-button:not(:disabled):hover {
  transform: translateY(-1px);
}

.themed-button:not(:disabled):active {
  transform: translateY(0);
}

/* 模态框背景模糊效果 */
.themed-modal {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

@layer components {
  .suggestion-highlight {
    cursor: pointer;
    transition: all 0.2s;
    background-color: var(--color-suggestion-clarity-bg);
    border-bottom: 2px solid var(--color-suggestion-clarity);
  }

  .suggestion-highlight:hover {
    opacity: 0.8;
  }

  .grammar-error {
    cursor: pointer;
    background-color: var(--color-suggestion-grammar-bg);
    border-bottom: 2px solid var(--color-suggestion-grammar);
  }

  .style-improvement {
    cursor: pointer;
    background-color: var(--color-suggestion-style-bg);
    border-bottom: 2px solid var(--color-suggestion-style);
  }

  .clarity-improvement {
    cursor: pointer;
    background-color: var(--color-suggestion-clarity-bg);
    border-bottom: 2px solid var(--color-suggestion-clarity);
  }

  .text-editor {
    width: 100%;
    min-height: 400px;
    padding: 1.5rem;
    border-radius: 0.5rem;
    outline: none;
    resize: none;
    background-color: var(--color-bg-tertiary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  .text-editor:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    border-color: transparent;
    box-shadow: 0 0 0 2px var(--color-accent-red);
  }

  .suggestion-card {
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    max-width: 24rem;
    background-color: var(--color-bg-secondary);
    border: 1px solid var(--color-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  .btn-primary {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: colors 0.2s;
    background-color: var(--color-accent-red);
    color: #FEFCF5;
  }

  .btn-primary:hover {
    background-color: var(--color-accent-red-hover);
  }

  .btn-secondary {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: colors 0.2s;
    background-color: var(--color-bg-accent);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }

  .btn-secondary:hover {
    background-color: var(--color-bg-hover);
  }

  .header-btn {
    padding: 0.5rem;
    border-radius: 0.75rem;
    transition: colors 0.2s;
    color: var(--color-text-secondary);
  }

  .header-btn:hover {
    background-color: var(--color-bg-accent);
    color: var(--color-accent-green);
  }

  .dark .header-btn:hover {
    color: var(--color-accent-red);
  }

  .history-item {
    border-radius: 0.75rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: var(--color-bg-tertiary);
    border: 1px solid var(--color-border);
  }

  .history-item:hover {
    background-color: var(--color-bg-accent);
  }

  .delete-btn {
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: color 0.2s ease;
    color: var(--color-text-secondary);
  }

  .delete-btn:hover {
    background-color: var(--color-bg-accent);
    color: var(--color-accent-red);
  }

  .word-tag-btn {
    @apply px-2 py-1 rounded-md text-sm transition-colors duration-200;
    border: none;
  }

  .word-tag-btn:hover {
    opacity: 0.8;
  }

  .external-link {
    @apply flex items-center gap-1 text-sm transition-colors duration-200;
    color: var(--color-accent-green);
  }

  .external-link:hover {
    text-decoration: underline;
  }

  .new-chat-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    width: 48px;
    height: 48px;
    background-color: transparent;
    border: 1px solid var(--color-border);
    color: var(--color-text-secondary);
  }

  .new-chat-btn:hover:not(:disabled) {
    background-color: var(--color-bg-accent);
    color: var(--color-accent-green);
  }

  .dark .new-chat-btn:hover:not(:disabled) {
    color: var(--color-accent-red);
  }

  .send-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    width: 48px;
    height: 48px;
    flex-shrink: 0;
    color: #FEFCF5;
  }

  .send-btn:disabled {
    background-color: var(--color-bg-accent);
    cursor: not-allowed;
  }

  .send-btn:not(:disabled) {
    background-color: var(--color-accent-green);
    box-shadow: 0 2px 8px rgba(22, 101, 52, 0.3);
  }

  .send-btn:hover:not(:disabled) {
    background-color: var(--color-accent-green-hover);
  }

  .dark .send-btn:not(:disabled) {
    background-color: var(--color-accent-red);
    box-shadow: 0 2px 8px rgba(143, 188, 143, 0.3);
  }

  .dark .send-btn:hover:not(:disabled) {
    background-color: var(--color-accent-red-hover);
  }

  .clear-history-btn {
    @apply flex items-center gap-2 px-4 py-2;
    transition: none;
  }

  .clear-history-btn:disabled {
    color: var(--color-text-muted);
    cursor: not-allowed;
  }

  .clear-history-btn:not(:disabled) {
    color: var(--color-accent-red);
  }

  .clear-history-btn:hover:not(:disabled) {
    text-decoration: underline;
  }

  .back-to-list-btn {
    @apply flex items-center gap-2 px-4 py-2 rounded-xl transition-colors duration-200;
    background-color: var(--color-bg-accent);
    color: var(--color-text-primary);
  }

  .back-to-list-btn:hover {
    background-color: var(--color-bg-hover);
  }

  .nav-btn {
    @apply p-2 rounded-lg transition-all duration-200;
    background-color: transparent;
    border: 1px solid transparent;
  }

  .nav-btn:disabled {
    color: var(--color-text-muted);
    opacity: 0.5;
  }

  .nav-btn:not(:disabled) {
    color: var(--color-text-secondary);
  }

  .nav-btn:hover:not(:disabled) {
    background-color: var(--color-bg-accent);
    border-color: var(--color-border-accent);
    transform: scale(1.05);
  }

  .suggestion-replacement-btn {
    @apply p-4 rounded-xl text-sm font-medium cursor-pointer transition-all duration-200;
    font-family: Georgia, "Noto Serif SC", serif;
    border: 1px solid transparent;
    background-color: var(--color-bg-accent);
    color: var(--color-text-primary);
    backdrop-filter: blur(5px);
  }

  .suggestion-replacement-btn:hover {
    background-color: var(--color-bg-hover);
    border-color: transparent;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(93, 64, 55, 0.15);
  }

  .dark .suggestion-replacement-btn:hover {
    border-color: transparent;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .selection-menu-btn {
    @apply p-2 rounded-lg transition-colors duration-200;
    background-color: transparent;
  }

  .selection-menu-btn:hover {
    background-color: var(--color-bg-accent);
  }

  .settings-option-btn {
    @apply flex items-center p-4 rounded-xl transition-all duration-200;
    background-color: var(--color-bg-tertiary);
    border: 1px solid var(--color-border);
    cursor: pointer;
  }

  .settings-option-btn:hover {
    background-color: var(--color-bg-accent);
  }

  .save-settings-btn {
    @apply w-full px-6 py-3 rounded-xl transition-colors duration-200;
    color: #FEFCF5;
    font-family: Georgia, "Noto Serif SC", serif;
    letter-spacing: 0.05em;
    font-size: 16px;
    font-weight: 500;
    background-color: var(--color-accent-green);
  }

  .save-settings-btn:hover {
    background-color: var(--color-accent-green-hover);
  }

  .dark .save-settings-btn {
    background-color: var(--color-accent-red);
  }

  .dark .save-settings-btn:hover {
    background-color: var(--color-accent-red-hover);
  }

  .editor-action-btn {
    @apply flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-200;
    background-color: transparent;
    color: var(--color-text-secondary);
  }

  .editor-action-btn:hover {
    background-color: var(--color-bg-accent);
    color: var(--color-accent-green);
  }

  .dark .editor-action-btn:hover {
    color: var(--color-accent-red);
  }

  .ai-response-btn {
    @apply flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-200;
    background-color: var(--color-bg-accent);
    color: var(--color-text-secondary);
  }

  .ai-response-btn:hover {
    background-color: var(--color-bg-hover);
  }

  .analyze-btn {
    @apply fixed bottom-8 right-8 w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200;
    color: #FEFCF5;
    z-index: 20;
  }

  .analyze-btn:disabled {
    background-color: var(--color-bg-accent);
    cursor: not-allowed;
  }

  .analyze-btn:not(:disabled) {
    background-color: var(--color-bg-accent);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .analyze-btn:hover:not(:disabled) {
    background-color: var(--color-accent-red);
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(185, 28, 28, 0.4);
  }

  .dark .analyze-btn:not(:disabled) {
    background-color: var(--color-bg-accent);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .dark .analyze-btn:hover:not(:disabled) {
    background-color: var(--color-accent-red);
    box-shadow: 0 6px 16px rgba(210, 105, 30, 0.4);
  }

  .voice-play-btn {
    @apply flex items-center justify-center rounded-lg transition-all duration-200;
  }

  .voice-play-btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  .voice-play-btn.unsupported {
    background-color: var(--color-bg-accent);
    color: var(--color-text-secondary);
    cursor: not-allowed;
    opacity: 0.5;
  }

  .voice-play-btn.playing {
    background-color: #DC2626;
    color: #FEFCF5;
  }

  .dark .voice-play-btn.playing {
    background-color: #B91C1C;
  }

  .voice-play-btn.default {
    background-color: var(--color-bg-accent);
    color: var(--color-text-secondary);
  }

  .voice-play-btn.default:hover:not(:disabled) {
    background-color: var(--color-accent-green);
    color: #FEFCF5;
  }

  .dark .voice-play-btn.default:hover:not(:disabled) {
    background-color: var(--color-accent-red);
  }

  .themed-button-primary:hover {
    background-color: var(--color-accent-red-hover);
  }

  .themed-button-secondary:hover {
    background-color: var(--color-bg-hover);
  }

  .themed-button-success:hover {
    background-color: var(--color-accent-green-hover);
  }

  .themed-button-ghost:hover {
    background-color: var(--color-bg-accent);
  }

  .stop-btn {
    @apply flex items-center justify-center rounded-lg transition-all duration-200;
    background-color: var(--color-bg-accent);
    color: var(--color-text-secondary);
  }

  .stop-btn:hover {
    background-color: var(--color-bg-hover);
  }

  .clickable-word {
    @apply cursor-pointer hover:underline transition-all duration-200;
    color: inherit;
    text-decoration: none;
    border-radius: 4px;
    padding: 0;
    background-color: transparent;
  }

  .clickable-word:hover {
    background-color: var(--color-suggestion-green-bg);
  }

  /* 边注布局相关样式 */
  .margin-comment-editor {
    @apply flex h-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden relative;
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  .margin-comment-editor.mobile {
    @apply flex-col;
  }

  .editor-main {
    @apply flex-1 flex flex-col;
  }

  .editor-margin {
    @apply border-l border-gray-200 bg-gray-50;
    background-color: var(--color-bg-accent);
    border-color: var(--color-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  .editor-margin.mobile {
    @apply fixed inset-y-0 right-0 w-80 bg-white shadow-xl transform transition-transform duration-300 z-50;
    background-color: var(--color-bg-secondary);
    transition: background-color 0.3s ease, transform 0.3s ease;
  }

  .editor-margin.mobile.open {
    @apply translate-x-0;
  }

  .editor-margin.mobile.closed {
    @apply translate-x-full;
  }

  /* 连接线动画 */
  .connection-line {
    @apply transition-all duration-200 ease-in-out;
  }

  .connection-line.hovered {
    @apply opacity-90;
    filter: drop-shadow(0 0 6px currentColor);
  }

  /* 建议卡片动画 */
  .suggestion-card-margin {
    @apply transition-all duration-300 ease-out;
  }

  .suggestion-card-margin.dismissing {
    @apply opacity-0 scale-95 translate-x-4;
  }

  .suggestion-card-margin.applying {
    @apply opacity-0 scale-95 bg-green-100 border-green-300;
  }

  .suggestion-card-margin.hovered {
    @apply shadow-md scale-105 z-10;
  }

  /* 气泡动画 */
  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes zoom-in {
    from {
      transform: scale(0.95);
    }
    to {
      transform: scale(1);
    }
  }

  .animate-in {
    animation: fade-in 0.2s ease-out, zoom-in 0.2s ease-out;
  }

  /* 建议指示器动画 */
  .suggestion-indicator {
    @apply absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse;
  }

  .suggestion-indicator.grammar {
    @apply bg-red-500;
  }

  .suggestion-indicator.style {
    @apply bg-blue-500;
  }

  .suggestion-indicator.clarity {
    @apply bg-yellow-500;
  }

  /* 自定义滚动条样式 - 恢复细滚动条 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(139, 69, 19, 0.2);
    border-radius: 2px;
    transition: background-color 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(139, 69, 19, 0.4);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(210, 105, 30, 0.2);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(210, 105, 30, 0.4);
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 写作页面几乎不可见的滚动条样式 */
  .editor-scrollbar::-webkit-scrollbar {
    width: 3px;
  }

  .editor-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .editor-scrollbar::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 1.5px;
    transition: all 0.3s ease;
  }

  /* 只有在悬停或滚动时才显示滚动条 */
  .editor-scrollbar:hover::-webkit-scrollbar-thumb,
  .editor-scrollbar:active::-webkit-scrollbar-thumb {
    background-color: rgba(139, 69, 19, 0.15);
  }

  .dark .editor-scrollbar:hover::-webkit-scrollbar-thumb,
  .dark .editor-scrollbar:active::-webkit-scrollbar-thumb {
    background-color: rgba(210, 105, 30, 0.15);
  }

  /* 滚动时显示更明显的滚动条 */
  .editor-scrollbar::-webkit-scrollbar-thumb:active {
    background-color: rgba(139, 69, 19, 0.25);
  }

  .dark .editor-scrollbar::-webkit-scrollbar-thumb:active {
    background-color: rgba(210, 105, 30, 0.25);
  }

  .editor-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 编辑器内部textarea的细滚动条样式 */
  .editor-textarea-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .editor-textarea-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .editor-textarea-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(139, 69, 19, 0.2);
    border-radius: 2px;
    transition: background-color 0.2s ease;
  }

  .editor-textarea-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(139, 69, 19, 0.4);
  }

  .dark .editor-textarea-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .dark .editor-textarea-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(210, 105, 30, 0.2);
  }

  .dark .editor-textarea-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(210, 105, 30, 0.4);
  }

  .editor-textarea-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 日记内容展开/收起动画 */
  .diary-text {
    transition: all 0.3s ease;
  }

  .diary-text:hover {
    opacity: 0.9;
  }

  /* 聊天专用的更隐蔽滚动条 */
  .chat-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
  }

  .chat-scrollbar::-webkit-scrollbar {
    width: 3px;
  }

  .chat-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .chat-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(139, 69, 19, 0.1);
    border-radius: 6px;
    transition: all 0.3s ease;
    opacity: 0;
  }

  .chat-scrollbar:hover::-webkit-scrollbar-thumb {
    background-color: rgba(139, 69, 19, 0.3);
    opacity: 1;
  }

  .chat-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(139, 69, 19, 0.5);
  }

  .dark .chat-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(196, 181, 154, 0.1);
  }

  .dark .chat-scrollbar:hover::-webkit-scrollbar-thumb {
    background-color: rgba(196, 181, 154, 0.3);
    opacity: 1;
  }

  .dark .chat-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(196, 181, 154, 0.5);
  }

  /* 建议气泡专用滚动条样式 */
  .suggestion-bubble::-webkit-scrollbar {
    width: 4px;
  }

  .suggestion-bubble::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  .suggestion-bubble::-webkit-scrollbar-thumb {
    background-color: rgba(230, 215, 184, 0.6);
    border-radius: 10px;
    transition: all 0.2s ease;
  }

  .suggestion-bubble::-webkit-scrollbar-thumb:hover {
    background-color: rgba(230, 215, 184, 0.9);
  }

  .dark .suggestion-bubble::-webkit-scrollbar-thumb {
    background-color: rgba(74, 63, 53, 0.4);
  }

  .dark .suggestion-bubble::-webkit-scrollbar-thumb:hover {
    background-color: rgba(74, 63, 53, 0.7);
  }

  /* 聊天输入框隐藏滚动条样式 */
  .chat-input-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .chat-input-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* 暗色模式下的绿色类覆盖为橙色 */
  .dark .text-green-600 {
    color: #D2691E !important;
  }

  .dark .text-green-700 {
    color: #B8591A !important;
  }

  .dark .text-green-800 {
    color: #A0471A !important;
  }

  .dark .bg-green-50 {
    background-color: rgba(210, 105, 30, 0.1) !important;
  }

  .dark .border-green-200 {
    border-color: rgba(210, 105, 30, 0.3) !important;
  }
}

/* 聊天消息文本样式 - 确保AI和用户消息字体渲染一致 */
.chat-message-text {
  font-family: 'Georgia', 'Noto Serif SC', serif;
  letter-spacing: normal;
  font-feature-settings: normal;
  text-rendering: optimizeLegibility;
}



/* 聊天头像样式 */
.chat-avatar {
  position: relative;
  overflow: hidden;
}

.chat-avatar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chat-avatar:hover::before {
  opacity: 1;
}

/* Alex头像特殊效果 */
.alex-avatar {
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.alex-avatar:hover {
  background-color: var(--color-bg-accent);
  transform: scale(1.02);
}

/* 用户头像特殊效果 */
.user-avatar {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-avatar:hover {
  background-color: var(--color-bg-accent);
  transform: scale(1.02);
}

/* 头像点击效果增强 */
.chat-avatar.cursor-pointer:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.chat-avatar.cursor-pointer:active {
  transform: scale(0.98);
}

/* 个人信息模态框动画 */
.profile-modal-enter {
  opacity: 0;
  transform: scale(0.9);
}

.profile-modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.profile-modal-exit {
  opacity: 1;
  transform: scale(1);
}

.profile-modal-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}

/* 日记组件样式 */
  .diary-section {
    transition: all 0.3s ease;
  }

  .diary-content {
    transition: all 0.3s ease;
  }

  .diary-content:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(93, 64, 55, 0.15);
  }

  .dark .diary-content:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
  }

  .diary-translation {
    transition: all 0.3s ease;
  }

  .diary-navigation {
    transition: all 0.2s ease;
  }

  .diary-navigation:hover {
    transform: scale(1.05);
  }

  /* 日记内容的特殊样式 */
  .diary-content p {
    margin-bottom: 0.75rem;
  }

  .diary-content p:last-child {
    margin-bottom: 0;
  }

  /* 日记日期样式 */
  .diary-date {
    font-variant-numeric: tabular-nums;
  }

  /* 文本截断样式 */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
