import { initializeHybridStorage, STORAGE_MODE, getStorageStatus } from './hybridHistoryService';
import { auth } from '../../config/firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';

/**
 * 历史记录服务初始化器
 * 自动检测用户登录状态并设置相应的存储模式
 */

let isInitialized = false;
let unsubscribeAuth = null;

/**
 * 初始化历史记录服务
 * @param {boolean} autoAuth - 是否自动监听认证状态变化
 */
export const initHistoryService = (autoAuth = true) => {
  if (isInitialized) {
    console.log('历史记录服务已经初始化');
    return;
  }

  console.log('🚀 开始初始化历史记录服务...');

  if (autoAuth) {
    // 监听认证状态变化
    console.log('🔍 开始监听Firebase认证状态变化...');
    unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      console.log('🔐 Firebase认证状态变化:', user ? `用户已登录 (${user.uid})` : '用户未登录');
      if (user) {
        // 用户已登录，切换到Firebase模式
        console.log('👤 用户已登录，切换到Firebase模式:', user.uid);
        console.log('📧 用户邮箱:', user.email);
        console.log('🔑 用户认证状态:', user.emailVerified ? '已验证' : '未验证');
        initializeHybridStorage(STORAGE_MODE.FIREBASE, user.uid);
      } else {
        // 用户未登录，切换到本地模式
        console.log('👤 用户未登录，切换到本地模式');
        initializeHybridStorage(STORAGE_MODE.LOCAL);
      }
    });
  } else {
    // 手动初始化本地模式
    console.log('🔧 手动初始化本地存储模式');
    initializeHybridStorage(STORAGE_MODE.LOCAL);
  }

  isInitialized = true;
  console.log('✅ 历史记录服务初始化完成');
};

/**
 * 手动设置存储模式
 * @param {string} mode - 存储模式
 * @param {string} userId - 用户ID
 */
export const setHistoryStorageMode = (mode, userId = null) => {
  if (!isInitialized) {
    console.warn('历史记录服务尚未初始化，请先调用 initHistoryService()');
    return;
  }

  initializeHybridStorage(mode, userId);
};

/**
 * 获取当前存储状态
 * @returns {Object} 存储状态信息
 */
export const getCurrentStorageStatus = () => {
  if (!isInitialized) {
    return {
      initialized: false,
      mode: 'unknown',
      userId: null,
      canUseFirebase: false,
      isOnline: navigator.onLine
    };
  }

  return {
    initialized: true,
    ...getStorageStatus()
  };
};

/**
 * 清理历史记录服务
 */
export const cleanupHistoryService = () => {
  if (unsubscribeAuth) {
    unsubscribeAuth();
    unsubscribeAuth = null;
  }
  
  isInitialized = false;
  console.log('🧹 历史记录服务已清理');
};

/**
 * 检查服务是否已初始化
 * @returns {boolean} 是否已初始化
 */
export const isHistoryServiceInitialized = () => isInitialized;

/**
 * 强制重新初始化服务
 * @param {boolean} autoAuth - 是否自动监听认证状态变化
 */
export const reinitHistoryService = (autoAuth = true) => {
  cleanupHistoryService();
  initHistoryService(autoAuth);
};

// 导出存储模式常量
export { STORAGE_MODE } from './hybridHistoryService';

// 导出所有混合服务
export {
  hybridChatHistoryService,
  hybridAnalysisService,
  hybridDictionarySearchService,
  hybridWritingHistoryService,
  hybridDiaryHistoryService
} from './hybridHistoryService';

// 导出迁移服务
export { migrationService } from './firebaseHistoryService';
