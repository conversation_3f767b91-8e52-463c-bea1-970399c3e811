import React, { useEffect, useState } from 'react';
import { Zap } from 'lucide-react';
import { useBubbleManager } from '../hooks/useBubbleManager';
import EditorActionBar from './EditorActionBar';
import EditorBottomControls from './EditorBottomControls';
import TextHighlightOverlay from './TextHighlightOverlay';

const VintageTextEditor = ({
  text,
  setText,
  suggestions,
  isAnalyzing,
  onAnalyze,
  hoveredSuggestion,
  setHoveredSuggestion,
  setBubblePosition,
  setActiveBubble,
  isImmersiveMode,
  onShowAIResponse,
  rawAIResponse,
  onShowHistory,
  onShowDictionary,
  onNewDocument,
  isDarkMode
}) => {
  // 使用气泡管理器 Hook
  const bubbleManager = useBubbleManager();
  
  // 语音输入状态
  const [voiceTranscript, setVoiceTranscript] = useState('');

  // 添加事件监听器，处理气泡鼠标进入事件
  useEffect(() => {
    const handleBubbleMouseEnter = () => {
      // 当鼠标进入气泡时，清除任何关闭气泡的超时
      if (bubbleManager) {
        bubbleManager.clearTimeout();
      }
    };

    const handleBubbleMouseLeave = () => {
      // 清除任何可能的超时
      if (bubbleManager) {
        bubbleManager.clearTimeout();
      }
    };

    // 添加全局事件监听器
    document.addEventListener('mouseenter', handleBubbleMouseEnter, true);
    document.addEventListener('mouseleave', handleBubbleMouseLeave, true);

    return () => {
      document.removeEventListener('mouseenter', handleBubbleMouseEnter, true);
      document.removeEventListener('mouseleave', handleBubbleMouseLeave, true);
    };
  }, [bubbleManager]);

  // 处理鼠标进入建议高亮区域
  const handleMouseEnter = (e, suggestion) => {
    setHoveredSuggestion(suggestion.id);
    setActiveBubble(suggestion);

    // 清除任何现有的超时
    if (bubbleManager) {
      bubbleManager.clearTimeout();
      bubbleManager.setActiveSuggestion(suggestion.id);
    }

    // 计算气泡位置
    const rect = e.target.getBoundingClientRect();
    
    setBubblePosition({
      x: rect.left + rect.width / 2 - 192, // 向左偏移192px (max-w-sm的一半)，让气泡居中显示
      y: rect.bottom + 8
    });
  };

  // 处理鼠标离开建议高亮区域
  const handleMouseLeave = () => {
    setHoveredSuggestion(null);

    // 设置延迟关闭气泡，给用户时间移动到气泡上
    if (bubbleManager) {
      bubbleManager.setTimeout(() => {
        setActiveBubble(null);
        bubbleManager.clearActiveSuggestion();
      }, 800); // 增加延迟时间，给用户更多时间移动到气泡上
    }
  };

  // 处理语音转录变化
  const handleVoiceTranscriptChange = (transcript) => {
    setVoiceTranscript(transcript);
  };

  // 处理语音最终转录
  const handleVoiceFinalTranscript = (finalTranscript) => {
    if (finalTranscript.trim()) {
      const newText = text ? `${text} ${finalTranscript}` : finalTranscript;
      setText(newText);
    }
    setVoiceTranscript('');
  };

  // 处理高亮覆盖层点击事件
  const handleHighlightClick = (e) => {
    // 如果点击的不是建议元素，则让事件穿透到编辑器
    if (!e.target.hasAttribute('data-suggestion-id')) {
      e.target.style.pointerEvents = 'none';
      const textarea = e.currentTarget.previousElementSibling;
      if (textarea) {
        textarea.focus();
        // 计算点击位置并设置光标
        const rect = textarea.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        // 简单的光标定位，可以根据需要改进
        textarea.setSelectionRange(textarea.value.length, textarea.value.length);
      }
      setTimeout(() => {
        e.target.style.pointerEvents = 'auto';
      }, 100);
    }
  };

  return (
    <div className="rounded-2xl transition-colors duration-300" style={{
      backgroundColor: isDarkMode ? '#2A241D' : '#FEFCF5',
      padding: '48px',
      height: isImmersiveMode ? 'calc(100vh - 160px)' : 'calc(100vh - 200px)',
      position: 'relative'
    }}>
      {/* 操作栏 */}
      <EditorActionBar
        isDarkMode={isDarkMode}
        rawAIResponse={rawAIResponse}
        onShowDictionary={onShowDictionary}
        onShowHistory={onShowHistory}
        onShowAIResponse={onShowAIResponse}
      />

      {/* 编辑区域 */}
      <div className="relative">
        {/* 文本输入区域 */}
        <textarea
          value={voiceTranscript ? `${text}${voiceTranscript}` : text}
          onChange={(e) => {
            const newText = e.target.value;
            setText(newText);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Tab') {
              e.preventDefault();
              if (!isAnalyzing && text.trim().length >= 10) {
                onAnalyze();
              }
            }
          }}
          placeholder="在这里开始写作... 我会实时为你提供语法检查、风格优化和写作建议。按 Tab 键快速分析，或点击右下角麦克风图标进行语音输入。"
          className="w-full rounded-2xl focus:outline-none resize-none relative z-5 editor-textarea-scrollbar"
          style={{
            fontFamily: 'Georgia, "Noto Serif SC", "Times New Roman", serif',
            fontSize: '20px',
            lineHeight: '2',
            letterSpacing: '0.05em',
            backgroundColor: isDarkMode ? '#2A241D' : '#FFFEF7',
            color: suggestions.length > 0 ? 'transparent' : (isDarkMode ? '#E8DCC6' : '#5D4037'),
            caretColor: isDarkMode ? '#E8DCC6' : '#5D4037',
            padding: '32px',
            height: isImmersiveMode ? 'calc(100vh - 320px)' : 'calc(100vh - 400px)',
            transition: 'background-color 0.3s ease, color 0.3s ease'
          }}
          onFocus={(e) => {
            e.target.style.backgroundColor = isDarkMode ? '#2A241D' : '#FFFDF0';
          }}
          onBlur={(e) => {
            e.target.style.backgroundColor = isDarkMode ? '#2A241D' : '#FFFEF7';
          }}
        />

        {/* 高亮覆盖层 */}
        {suggestions.length > 0 && (
          <TextHighlightOverlay
            text={text}
            suggestions={suggestions}
            hoveredSuggestion={hoveredSuggestion}
            isDarkMode={isDarkMode}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={handleHighlightClick}
          />
        )}

        {/* 底部控制组件 */}
        <EditorBottomControls
          isDarkMode={isDarkMode}
          isAnalyzing={isAnalyzing}
          voiceTranscript={voiceTranscript}
          onNewDocument={onNewDocument}
          onVoiceTranscriptChange={handleVoiceTranscriptChange}
          onVoiceFinalTranscript={handleVoiceFinalTranscript}
        />
      </div>

      {/* 右下角浮动AI分析按钮 */}
      <button
        onClick={onAnalyze}
        disabled={isAnalyzing || text.trim().length < 10}
        className="analyze-btn"
        title={isAnalyzing ? "分析中..." : "AI分析 (Tab)"}
      >
        {isAnalyzing ? (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white" style={{ borderColor: '#FEFCF5' }}></div>
        ) : (
          <Zap
            style={{
              width: '32px',
              height: '32px',
              color: '#FEFCF5',
              fill: 'none',
              stroke: 'currentColor',
              strokeWidth: 2,
              pointerEvents: 'none'
            }}
          />
        )}
      </button>
    </div>
  );
};

export default VintageTextEditor;