// API 配置文件
// 现在API密钥由系统统一管理，存储在Firebase Firestore中

import { getDoubaoApiKey } from '../services/user/userSettingsService.js';

// 获取API密钥的函数 - 从Firebase获取
const getAPIKey = async () => {
  try {
    return await getDoubaoApiKey();
  } catch (error) {
    console.error('获取API密钥失败:', error);
    return '';
  }
};

export const API_CONFIG = {
  // 豆包 API 配置
  doubao: {
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    model: 'doubao-seed-1-6-flash-250615',
    // 动态获取API密钥 - 现在是异步函数
    async getApiKey() {
      return await getAPIKey();
    }
  },
  
  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    maxRetries: 3,  // 最大重试次数
    temperature: 2.0, // AI 创造性参数
    maxTokens: 2000   // 最大返回token数
  }
};

// 检查API密钥是否已配置 - 现在是异步函数
export const isAPIKeyConfigured = async () => {
  try {
    const apiKey = await getAPIKey();
    return apiKey && apiKey.trim().length > 0;
  } catch (error) {
    console.error('检查API密钥配置失败:', error);
    return false;
  }
};

// 获取API配置状态 - 现在是异步函数
export const getAPIStatus = async () => {
  const configured = await isAPIKeyConfigured();
  return {
    configured,
    model: API_CONFIG.doubao.model,
    baseURL: API_CONFIG.doubao.baseURL
  };
};
