// 使用 Free Dictionary API 的服务

const API_URL = 'https://api.dictionaryapi.dev/api/v2/entries/en';

// 查询单词信息
const lookupWord = async (word) => {
  try {
    const response = await fetch(`${API_URL}/${encodeURIComponent(word)}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return { notFound: true };
      }
      throw new Error(`API 请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`查询单词 "${word}" 失败:`, error);
    return null;
  }
};

// 格式化 API 返回的数据
const formatWordData = (apiData) => {
  if (!apiData || apiData.notFound) {
    return {
      word: '',
      phonetics: [],
      meanings: [],
      notFound: true
    };
  }
  
  try {
    const entry = apiData[0];
    
    return {
      word: entry.word || '',
      phonetics: Array.isArray(entry.phonetics) ? entry.phonetics.map(p => ({
        text: p.text || '',
        audio: p.audio || ''
      })) : [],
      meanings: Array.isArray(entry.meanings) ? entry.meanings.map(m => ({
        partOfSpeech: m.partOfSpeech || '',
        definitions: Array.isArray(m.definitions) ? m.definitions.map(d => ({
          definition: d.definition || '',
          example: d.example || '',
          synonyms: Array.isArray(d.synonyms) ? d.synonyms : [],
          antonyms: Array.isArray(d.antonyms) ? d.antonyms : []
        })) : [],
        synonyms: Array.isArray(m.synonyms) ? m.synonyms : [],
        antonyms: Array.isArray(m.antonyms) ? m.antonyms : []
      })) : [],
      notFound: false
    };
  } catch (error) {
    console.error('格式化词典数据时出错:', error);
    return {
      word: '',
      phonetics: [],
      meanings: [],
      notFound: true
    };
  }
};

// 获取单词的详细信息
const getWordDetails = async (word) => {
  const apiData = await lookupWord(word);
  return formatWordData(apiData);
};

// 获取单词的同义词
const getSynonyms = async (word) => {
  const wordData = await getWordDetails(word);
  
  if (wordData.notFound) {
    return [];
  }
  
  const allSynonyms = new Set();
  
  // 收集所有词义中的同义词
  wordData.meanings.forEach(meaning => {
    // 添加词义级别的同义词
    meaning.synonyms.forEach(syn => allSynonyms.add(syn));
    
    // 添加定义级别的同义词
    meaning.definitions.forEach(def => {
      def.synonyms.forEach(syn => allSynonyms.add(syn));
    });
  });
  
  return Array.from(allSynonyms);
};

// 获取单词的反义词
const getAntonyms = async (word) => {
  const wordData = await getWordDetails(word);
  
  if (wordData.notFound) {
    return [];
  }
  
  const allAntonyms = new Set();
  
  // 收集所有词义中的反义词
  wordData.meanings.forEach(meaning => {
    // 添加词义级别的反义词
    meaning.antonyms.forEach(ant => allAntonyms.add(ant));
    
    // 添加定义级别的反义词
    meaning.definitions.forEach(def => {
      def.antonyms.forEach(ant => allAntonyms.add(ant));
    });
  });
  
  return Array.from(allAntonyms);
};

export {
  lookupWord,
  getWordDetails,
  getSynonyms,
  getAntonyms
};