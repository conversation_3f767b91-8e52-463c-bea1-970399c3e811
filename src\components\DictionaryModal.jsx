import React from 'react';
import { X } from 'lucide-react';
import DictionaryLookup from './DictionaryLookup';

const DictionaryModal = ({ isOpen, onClose, word, isDarkMode, dictionaryService }) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    // 只有点击遮罩层本身时才关闭，点击弹窗内容不关闭
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 flex items-center justify-center z-50 transition-colors duration-300" 
      style={{ backgroundColor: 'var(--modal-backdrop)' }}
      onClick={handleBackdropClick}
    >
      <div className="rounded-2xl max-w-2xl w-full mx-8 transition-colors duration-300" style={{
        backgroundColor: 'var(--color-bg-secondary)',
        maxHeight: '80vh',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <div className="flex items-center justify-between transition-colors duration-300" style={{
          padding: '24px 24px 16px 24px',
          borderBottom: '1px solid var(--color-border)'
        }}>
          <h3 className="text-xl font-semibold transition-colors duration-300" style={{
            color: 'var(--color-text-primary)',
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            letterSpacing: '0.05em'
          }}>词典查询</h3>
          <button
            onClick={onClose}
            className="header-btn"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="custom-scrollbar" style={{ 
          flex: '1',
          minHeight: 0,
          overflowY: 'auto',
          padding: '24px 24px 24px 24px'
        }}>
          <DictionaryLookup word={word} isDarkMode={isDarkMode} dictionaryService={dictionaryService} />
        </div>
      </div>
    </div>
  );
};

export default DictionaryModal;