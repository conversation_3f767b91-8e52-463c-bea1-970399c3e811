// ECDICT 集成测试工具
// 用于测试 ECDICT 词典服务的集成效果

import { 
  initializeServices, 
  getWordDetails, 
  searchWords, 
  getDictionaryStats,
  checkServiceStatus 
} from '../services/dictionary/unifiedDictionaryService';

// 测试单词列表
const TEST_WORDS = [
  'hello',
  'world', 
  'beautiful',
  'computer',
  'education',
  'technology',
  'happiness',
  'knowledge',
  'creativity',
  'imagination'
];

// 测试词典服务初始化
const testInitialization = async () => {
  console.log('🧪 测试词典服务初始化...');
  
  try {
    const status = await checkServiceStatus();
    console.log('📊 服务状态:', status);
    
    if (status.initialized) {
      console.log('✅ 词典服务初始化成功');
      return true;
    } else {
      console.log('❌ 词典服务初始化失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 初始化测试失败:', error);
    return false;
  }
};

// 测试单词查询
const testWordLookup = async (word) => {
  console.log(`🔍 测试查询单词: ${word}`);
  
  try {
    const startTime = performance.now();
    const result = await getWordDetails(word);
    const endTime = performance.now();
    
    const queryTime = endTime - startTime;
    
    if (result && !result.notFound) {
      console.log(`✅ 查询成功 (${queryTime.toFixed(2)}ms):`, {
        word: result.word,
        source: result.source,
        hasTranslation: !!result.translation,
        hasPhonetic: !!result.phonetic,
        hasExchange: !!result.exchange,
        collins: result.collins,
        oxford: result.oxford,
        tags: result.tags,
        bnc: result.bnc,
        frq: result.frq
      });
      return true;
    } else {
      console.log(`❌ 查询失败: ${word}`, result);
      return false;
    }
  } catch (error) {
    console.error(`❌ 查询 ${word} 时出错:`, error);
    return false;
  }
};

// 测试模糊搜索
const testFuzzySearch = async (query) => {
  console.log(`🔍 测试模糊搜索: ${query}`);
  
  try {
    const startTime = performance.now();
    const results = await searchWords(query, { limit: 5, fuzzy: true });
    const endTime = performance.now();
    
    const queryTime = endTime - startTime;
    
    console.log(`✅ 搜索完成 (${queryTime.toFixed(2)}ms)，找到 ${results.length} 个结果:`);
    results.forEach((result, index) => {
      console.log(`  ${index + 1}. ${result.word} (${result.source})`);
    });
    
    return results.length > 0;
  } catch (error) {
    console.error(`❌ 搜索 ${query} 时出错:`, error);
    return false;
  }
};

// 测试词典统计信息
const testDictionaryStats = async () => {
  console.log('📊 测试词典统计信息...');
  
  try {
    const stats = await getDictionaryStats();
    console.log('📈 词典统计:', stats);
    
    if (stats.isLoaded) {
      console.log('✅ 词典统计获取成功');
      return true;
    } else {
      console.log('❌ 词典统计获取失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 统计信息测试失败:', error);
    return false;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始 ECDICT 集成测试...');
  console.log('='.repeat(50));
  
  const results = {
    initialization: false,
    wordLookup: 0,
    fuzzySearch: 0,
    dictionaryStats: false
  };
  
  // 测试初始化
  results.initialization = await testInitialization();
  console.log('');
  
  // 测试单词查询
  console.log('🔍 测试单词查询...');
  for (const word of TEST_WORDS) {
    const success = await testWordLookup(word);
    if (success) results.wordLookup++;
    await new Promise(resolve => setTimeout(resolve, 100)); // 避免请求过快
  }
  console.log('');
  
  // 测试模糊搜索
  console.log('🔍 测试模糊搜索...');
  const searchQueries = ['hel', 'beaut', 'tech', 'know', 'creat'];
  for (const query of searchQueries) {
    const success = await testFuzzySearch(query);
    if (success) results.fuzzySearch++;
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  console.log('');
  
  // 测试统计信息
  results.dictionaryStats = await testDictionaryStats();
  console.log('');
  
  // 输出测试结果
  console.log('📋 测试结果汇总:');
  console.log('='.repeat(50));
  console.log(`初始化: ${results.initialization ? '✅ 成功' : '❌ 失败'}`);
  console.log(`单词查询: ${results.wordLookup}/${TEST_WORDS.length} 成功`);
  console.log(`模糊搜索: ${results.fuzzySearch}/${searchQueries.length} 成功`);
  console.log(`统计信息: ${results.dictionaryStats ? '✅ 成功' : '❌ 失败'}`);
  
  const totalTests = 3 + TEST_WORDS.length + searchQueries.length;
  const passedTests = (results.initialization ? 1 : 0) + results.wordLookup + results.fuzzySearch + (results.dictionaryStats ? 1 : 0);
  
  console.log(`\n🎯 总体结果: ${passedTests}/${totalTests} 测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！ECDICT 集成成功！');
  } else {
    console.log('⚠️ 部分测试失败，请检查配置');
  }
  
  return results;
};

// 导出测试函数
export {
  testInitialization,
  testWordLookup,
  testFuzzySearch,
  testDictionaryStats,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testEcdictIntegration = {
    testInitialization,
    testWordLookup,
    testFuzzySearch,
    testDictionaryStats,
    runAllTests
  };
  
  console.log('🧪 ECDICT 集成测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testEcdictIntegration.runAllTests() // 运行所有测试');
  console.log('  - window.testEcdictIntegration.testWordLookup("hello") // 测试单个单词');
  console.log('  - window.testEcdictIntegration.testFuzzySearch("hel") // 测试模糊搜索');
}