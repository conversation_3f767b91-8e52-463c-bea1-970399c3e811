import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { vi } from 'vitest';
import { ttsService } from '../services/ai/ttsService';
import VoicePlayButton from './VoicePlayButton';

// Mock TTS服务
vi.mock('../services/ai/ttsService', () => ({
  ttsService: {
    status: {
      isSupported: true,
      isPlaying: false,
      isPaused: false,
      isStopped: true
    },
    speak: vi.fn(),
    stop: vi.fn(),
    addStatusListener: vi.fn(),
    removeStatusListener: vi.fn()
  }
}));

describe('VoicePlayButton', () => {
  const defaultProps = {
    text: 'Hello world',
    isDarkMode: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // 重置TTS服务状态
    ttsService.status.isSupported = true;
    ttsService.status.isPlaying = false;
    ttsService.status.isPaused = false;
    ttsService.status.isStopped = true;
  });

  describe('基本渲染', () => {
    it('应该渲染播放按钮', () => {
      render(<VoicePlayButton {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('title', '点击播放语音');
    });

    it('应该在不支持TTS时显示禁用状态', () => {
      ttsService.status.isSupported = false;

      render(<VoicePlayButton {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('title', '浏览器不支持语音播放');
    });

    it('应该在没有文本时显示错误', async () => {
      render(<VoicePlayButton text="" isDarkMode={false} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      // 应该不会调用speak
      expect(ttsService.speak).not.toHaveBeenCalled();
    });

    it('应该显示正确的播放图标', () => {
      render(<VoicePlayButton {...defaultProps} />);

      // 查找Play图标（SVG）
      const playIcon = screen.getByRole('button').querySelector('svg');
      expect(playIcon).toBeInTheDocument();
    });
  });

  describe('播放功能', () => {
    it('应该能够开始播放', async () => {
      ttsService.speak.mockResolvedValue();

      render(<VoicePlayButton {...defaultProps} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(ttsService.speak).toHaveBeenCalledWith(
          'Hello world',
          expect.objectContaining({
            lang: 'en-US',
            rate: 0.9,
            pitch: 1.0,
            volume: 1.0
          })
        );
      });
    });

    it('应该能够停止播放', async () => {
      ttsService.speak.mockResolvedValue();

      render(<VoicePlayButton {...defaultProps} />);

      const button = screen.getByRole('button');

      // 开始播放
      fireEvent.click(button);

      // 等待播放状态更新
      await waitFor(() => {
        expect(button).toHaveAttribute('title', '点击停止播放');
      });

      // 再次点击停止
      fireEvent.click(button);

      // 验证按钮状态回到播放状态
      await waitFor(() => {
        expect(button).toHaveAttribute('title', '点击播放语音');
      });
    });

    it('应该在播放时显示停止图标', async () => {
      ttsService.speak.mockImplementation(() => new Promise(() => { })); // 永不resolve，保持播放状态

      render(<VoicePlayButton {...defaultProps} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(button).toHaveAttribute('title', '点击停止播放');
        // 检查是否有Square图标（停止图标）
        const stopIcon = button.querySelector('svg');
        expect(stopIcon).toBeInTheDocument();
      });
    });

    it('应该处理播放错误', async () => {
      const mockError = new Error('播放失败');
      ttsService.speak.mockRejectedValue(mockError);

      const onError = vi.fn();
      render(<VoicePlayButton {...defaultProps} onError={onError} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(onError).toHaveBeenCalledWith('播放失败');
      });
    });
  });

  describe('回调函数', () => {
    it('应该在播放开始时调用onPlayStart', async () => {
      const onPlayStart = vi.fn();
      ttsService.speak.mockResolvedValue();

      render(<VoicePlayButton {...defaultProps} onPlayStart={onPlayStart} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(onPlayStart).toHaveBeenCalledWith('Hello world');
      });
    });

    it('应该在播放结束时调用onPlayEnd', async () => {
      const onPlayEnd = vi.fn();
      ttsService.speak.mockResolvedValue();

      render(<VoicePlayButton {...defaultProps} onPlayEnd={onPlayEnd} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(onPlayEnd).toHaveBeenCalledWith('Hello world');
      });
    });

    it('应该在停止播放时调用onPlayEnd', async () => {
      const onPlayEnd = vi.fn();
      ttsService.speak.mockImplementation(() => new Promise(() => { })); // 永不resolve

      render(<VoicePlayButton {...defaultProps} onPlayEnd={onPlayEnd} />);

      const button = screen.getByRole('button');

      // 开始播放
      fireEvent.click(button);

      // 等待播放状态
      await waitFor(() => {
        expect(button).toHaveAttribute('title', '点击停止播放');
      });

      // 停止播放
      fireEvent.click(button);

      expect(onPlayEnd).toHaveBeenCalledWith('Hello world');
    });
  });

  describe('样式和尺寸', () => {
    it('应该支持不同尺寸', () => {
      const { rerender } = render(<VoicePlayButton {...defaultProps} size="small" />);

      let button = screen.getByRole('button');
      expect(button).toHaveClass('w-8', 'h-8');

      rerender(<VoicePlayButton {...defaultProps} size="medium" />);
      button = screen.getByRole('button');
      expect(button).toHaveClass('w-10', 'h-10');

      rerender(<VoicePlayButton {...defaultProps} size="large" />);
      button = screen.getByRole('button');
      expect(button).toHaveClass('w-12', 'h-12');
    });

    it('应该支持自定义className', () => {
      render(<VoicePlayButton {...defaultProps} className="custom-class" />);

      const container = screen.getByRole('button').parentElement;
      expect(container).toHaveClass('custom-class');
    });

    it('应该在深色模式下应用正确样式', () => {
      render(<VoicePlayButton {...defaultProps} isDarkMode={true} />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });
  });

  describe('状态监听', () => {
    it('应该注册和移除TTS状态监听器', () => {
      const { unmount } = render(<VoicePlayButton {...defaultProps} />);

      expect(ttsService.addStatusListener).toHaveBeenCalled();

      unmount();

      expect(ttsService.removeStatusListener).toHaveBeenCalled();
    });
  });
});
