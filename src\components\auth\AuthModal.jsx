import { <PERSON>, EyeOff, Lock, Mail, User } from 'lucide-react';
import React, { useState } from 'react';
import { signIn, signInWithGoogle, signUp } from '../../services/auth/authService';
import { ThemedButton, ThemedInput, ThemedModal } from '../themed';

const AuthModal = ({ isOpen, onClose, isDarkMode }) => {
  const [isLoginMode, setIsLoginMode] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleAuthAction = async () => {
    if (!email || !password) {
      setError('Email and password cannot be empty.');
      return;
    }
    setIsLoading(true);
    setError('');
    try {
      if (isLoginMode) {
        await signIn(email, password);
      } else {
        await signUp(email, password);
      }
      onClose(); // Close modal on successful login/signup
    } catch (err) {
      // Firebase provides more user-friendly error messages
      let friendlyMessage = err.message;
      if (err.code === 'auth/wrong-password') {
        friendlyMessage = 'Incorrect password. Please try again.';
      } else if (err.code === 'auth/user-not-found') {
        friendlyMessage = 'No account found with this email. Please sign up.';
      } else if (err.code === 'auth/email-already-in-use') {
        friendlyMessage = 'This email is already registered. Please login.';
      }
      setError(friendlyMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError('');
    try {
      await signInWithGoogle();
      onClose(); // Close modal on successful login
    } catch (err) {
      let friendlyMessage = err.message;
      if (err.code === 'auth/popup-closed-by-user') {
        friendlyMessage = 'Sign-in was cancelled. Please try again.';
      } else if (err.code === 'auth/popup-blocked') {
        friendlyMessage = 'Popup was blocked by your browser. Please allow popups and try again.';
      } else if (err.code === 'auth/account-exists-with-different-credential') {
        friendlyMessage = 'An account already exists with this email. Please sign in with your password instead.';
      }
      setError(friendlyMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMode = () => {
    setIsLoginMode(!isLoginMode);
    setError('');
    setEmail('');
    setPassword('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAuthAction();
    }
  };

  if (!isOpen) return null;

  return (
    <ThemedModal isOpen={isOpen} onClose={onClose} isDarkMode={isDarkMode} maxWidth="sm">
      <div className="auth-modal-content" style={{ padding: '0' }}>
        {/* Header Section with Icon */}
        <div
          className="auth-header"
          style={{
            textAlign: 'center',
            padding: '40px 40px 20px 40px',
            background: isDarkMode
              ? 'linear-gradient(135deg, #2A241D 0%, #332B22 100%)'
              : 'linear-gradient(135deg, #FEFCF5 0%, #F5EFE6 100%)',
            borderRadius: '16px 16px 0 0'
          }}
        >
          <div
            className="auth-icon"
            style={{
              width: '64px',
              height: '64px',
              margin: '0 auto 24px auto',
              borderRadius: '20px',
              background: isDarkMode ? '#D2691E' : '#B91C1C',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: isDarkMode
                ? '0 8px 32px rgba(210, 105, 30, 0.3)'
                : '0 8px 32px rgba(185, 28, 28, 0.2)'
            }}
          >
            <User size={32} color="#FEFCF5" />
          </div>

          <h2
            className="auth-title"
            style={{
              fontSize: '28px',
              fontWeight: '700',
              margin: '0 0 8px 0',
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.02em'
            }}
          >
            {isLoginMode ? 'Welcome Back' : 'Join Studybuddy'}
          </h2>

          <p
            style={{
              fontSize: '16px',
              margin: '0',
              color: isDarkMode ? '#C4B59A' : '#8B4513',
              fontFamily: 'Georgia, "Noto Serif SC", serif'
            }}
          >
            {isLoginMode ? 'Sign in to continue your writing journey' : 'Start your English writing adventure'}
          </p>
        </div>

        {/* Form Section */}
        <div style={{ padding: '20px 40px 40px 40px' }}>
          {error && (
            <div
              className="error-message"
              style={{
                backgroundColor: isDarkMode ? 'rgba(210, 105, 30, 0.15)' : 'rgba(185, 28, 28, 0.1)',
                color: isDarkMode ? '#D2691E' : '#B91C1C',
                padding: '16px 20px',
                borderRadius: '12px',
                marginBottom: '24px',
                fontSize: '14px',
                textAlign: 'center',
                border: `1px solid ${isDarkMode ? 'rgba(210, 105, 30, 0.3)' : 'rgba(185, 28, 28, 0.2)'}`,
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}
            >
              {error}
            </div>
          )}

          <div className="form-fields" style={{ marginBottom: '32px' }}>
            {/* Email Input */}
            <div className="input-group" style={{ marginBottom: '20px', position: 'relative' }}>
              <div
                className="input-icon"
                style={{
                  position: 'absolute',
                  left: '20px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  zIndex: 1,
                  color: isDarkMode ? '#C4B59A' : '#8B4513'
                }}
              >
                <Mail size={20} />
              </div>
              <ThemedInput
                type="email"
                placeholder="Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                onKeyPress={handleKeyPress}
                isDarkMode={isDarkMode}
                style={{
                  paddingLeft: '56px',
                  height: '56px',
                  fontSize: '16px',
                  border: `2px solid ${isDarkMode ? '#4A3F35' : '#E5E7EB'}`,
                  transition: 'all 0.3s ease'
                }}
              />
            </div>

            {/* Password Input */}
            <div className="input-group" style={{ position: 'relative' }}>
              <div
                className="input-icon"
                style={{
                  position: 'absolute',
                  left: '20px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  zIndex: 1,
                  color: isDarkMode ? '#C4B59A' : '#8B4513'
                }}
              >
                <Lock size={20} />
              </div>
              <ThemedInput
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyPress={handleKeyPress}
                isDarkMode={isDarkMode}
                style={{
                  paddingLeft: '56px',
                  paddingRight: '56px',
                  height: '56px',
                  fontSize: '16px',
                  border: `2px solid ${isDarkMode ? '#4A3F35' : '#E5E7EB'}`,
                  transition: 'all 0.3s ease'
                }}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                style={{
                  position: 'absolute',
                  right: '20px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  color: isDarkMode ? '#C4B59A' : '#8B4513',
                  padding: '0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>

          {/* Action Button */}
          <div className="action-section" style={{ marginBottom: '16px' }}>
            <ThemedButton
              onClick={handleAuthAction}
              isLoading={isLoading}
              variant="primary"
              style={{
                width: '100%',
                height: '56px',
                fontSize: '16px',
                fontWeight: '600',
                borderRadius: '12px',
                background: isDarkMode
                  ? 'linear-gradient(135deg, #D2691E 0%, #B8591A 100%)'
                  : 'linear-gradient(135deg, #B91C1C 0%, #991B1B 100%)',
                border: 'none',
                boxShadow: isDarkMode
                  ? '0 4px 16px rgba(210, 105, 30, 0.3)'
                  : '0 4px 16px rgba(185, 28, 28, 0.2)',
                transition: 'all 0.3s ease',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                opacity: isLoading ? 0.7 : 1
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div
                    style={{
                      width: '20px',
                      height: '20px',
                      border: '2px solid rgba(255, 255, 255, 0.3)',
                      borderTop: '2px solid #FEFCF5',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}
                  />
                  {isLoginMode ? 'Signing In...' : 'Creating Account...'}
                </div>
              ) : (
                isLoginMode ? 'Sign In' : 'Create Account'
              )}
            </ThemedButton>
          </div>


          {/* Google Sign In Button */}
          <div className="google-signin-section" style={{ marginBottom: '16px' }}>
            <ThemedButton
              onClick={handleGoogleSignIn}
              isLoading={isLoading}
              variant="secondary"
              style={{
                width: '100%',
                height: '56px',
                fontSize: '16px',
                fontWeight: '600',
                borderRadius: '12px',
                background: isDarkMode ? '#2A241D' : '#FEFCF5',
                border: `2px solid ${isDarkMode ? '#4A3F35' : '#E5E7EB'}`,
                color: isDarkMode ? '#E8DCC6' : '#5D4037',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                transition: 'all 0.3s ease',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                opacity: isLoading ? 0.7 : 1
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div
                    style={{
                      width: '20px',
                      height: '20px',
                      border: '2px solid rgba(0, 0, 0, 0.1)',
                      borderTop: `2px solid ${isDarkMode ? '#D2691E' : '#B91C1C'}`,
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}
                  />
                  Signing in with Google...
                </div>
              ) : (
                <>
                  {/* Google Icon */}
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    style={{ flexShrink: 0 }}
                  >
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Continue with Google
                </>
              )}
            </ThemedButton>
          </div>

          {/* Toggle Mode Section */}
          <div className="toggle-section" style={{ textAlign: 'center' }}>
            <p
              style={{
                margin: '0 0 8px 0',
                fontSize: '14px',
                color: isDarkMode ? '#8B7D6B' : '#8B4513',
                fontFamily: 'Georgia, "Noto Serif SC", serif'
              }}
            >
              {isLoginMode ? "Don't have an account?" : 'Already have an account?'}
            </p>
            <button
              onClick={toggleMode}
              className="toggle-button"
              style={{
                background: 'none',
                border: 'none',
                color: isDarkMode ? '#D2691E' : '#B91C1C',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                padding: '8px 16px',
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = isDarkMode ? 'rgba(210, 105, 30, 0.1)' : 'rgba(185, 28, 28, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
              }}
            >
              {isLoginMode ? 'Sign Up' : 'Sign In'}
            </button>
          </div>
        </div>
      </div>

      {/* Add CSS animation for loading spinner */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .input-group input:focus {
          border-color: ${isDarkMode ? '#D2691E' : '#B91C1C'} !important;
          box-shadow: 0 0 0 3px ${isDarkMode ? 'rgba(210, 105, 30, 0.1)' : 'rgba(185, 28, 28, 0.1)'} !important;
          outline: none !important;
        }

        .action-section button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: ${isDarkMode
            ? '0 8px 24px rgba(210, 105, 30, 0.4)'
            : '0 8px 24px rgba(185, 28, 28, 0.3)'} !important;
        }

        .action-section button:active:not(:disabled) {
          transform: translateY(0px);
        }

        .google-signin-section button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: ${isDarkMode
            ? '0 8px 24px rgba(0, 0, 0, 0.3)'
            : '0 8px 24px rgba(0, 0, 0, 0.1)'} !important;
        }

        .google-signin-section button:active:not(:disabled) {
          transform: translateY(0px);
        }
      `}</style>
    </ThemedModal>
  );
};

export default AuthModal;
