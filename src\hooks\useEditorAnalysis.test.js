import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import { useEditorAnalysis } from './useEditorAnalysis';

// Mock useAIAnalysis hook
vi.mock('./useAIAnalysis', () => ({
  default: vi.fn()
}));

import useAIAnalysis from './useAIAnalysis';

describe('useEditorAnalysis', () => {
  const mockUser = { uid: '123', email: '<EMAIL>' };
  const mockSetShowAuthModal = vi.fn();
  const mockAnalyzeText = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    useAIAnalysis.mockReturnValue({
      isAnalyzing: false,
      suggestions: [],
      setSuggestions: vi.fn(),
      analyzeText: mockAnalyzeText,
      rawAIResponse: 'Mock AI response'
    });
  });

  it('should return AI analysis state and handlers', () => {
    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    expect(result.current.isAnalyzing).toBe(false);
    expect(result.current.suggestions).toEqual([]);
    expect(result.current.rawAIResponse).toBe('Mock AI response');
    expect(typeof result.current.setSuggestions).toBe('function');
    expect(typeof result.current.handleAnalyze).toBe('function');
  });

  it('should show alert and auth modal when user is not logged in', async () => {
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    const { result } = renderHook(() => useEditorAnalysis(null, mockSetShowAuthModal));

    await act(async () => {
      await result.current.handleAnalyze('Some text');
    });

    expect(alertSpy).toHaveBeenCalledWith('请先登录以使用AI分析功能');
    expect(mockSetShowAuthModal).toHaveBeenCalledWith(true);
    expect(mockAnalyzeText).not.toHaveBeenCalled();

    alertSpy.mockRestore();
  });

  it('should show alert when text is empty', async () => {
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    await act(async () => {
      await result.current.handleAnalyze('');
    });

    expect(alertSpy).toHaveBeenCalledWith('请输入一些文本再进行分析');
    expect(mockAnalyzeText).not.toHaveBeenCalled();

    alertSpy.mockRestore();
  });

  it('should show alert when text is only whitespace', async () => {
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    await act(async () => {
      await result.current.handleAnalyze('   ');
    });

    expect(alertSpy).toHaveBeenCalledWith('请输入一些文本再进行分析');
    expect(mockAnalyzeText).not.toHaveBeenCalled();

    alertSpy.mockRestore();
  });

  it('should call analyzeText when user is logged in and text is valid', async () => {
    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));
    const text = 'Valid text for analysis';

    await act(async () => {
      await result.current.handleAnalyze(text);
    });

    expect(mockAnalyzeText).toHaveBeenCalledWith(text, mockUser.uid);
  });

  it('should handle analyzeText success', async () => {
    mockAnalyzeText.mockResolvedValue();
    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    await act(async () => {
      await result.current.handleAnalyze('Valid text');
    });

    expect(mockAnalyzeText).toHaveBeenCalled();
  });

  it('should handle analyzeText error with usage limit message', async () => {
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const error = new Error('使用量已达上限');
    mockAnalyzeText.mockRejectedValue(error);
    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    await act(async () => {
      await result.current.handleAnalyze('Valid text');
    });

    expect(consoleSpy).toHaveBeenCalledWith('分析失败:', error);
    expect(alertSpy).toHaveBeenCalledWith('使用量已达上限');

    alertSpy.mockRestore();
    consoleSpy.mockRestore();
  });

  it('should handle analyzeText error with generic message', async () => {
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const error = new Error('Network error');
    mockAnalyzeText.mockRejectedValue(error);
    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    await act(async () => {
      await result.current.handleAnalyze('Valid text');
    });

    expect(consoleSpy).toHaveBeenCalledWith('分析失败:', error);
    expect(alertSpy).toHaveBeenCalledWith('分析失败，请稍后重试');

    alertSpy.mockRestore();
    consoleSpy.mockRestore();
  });

  it('should return analyzing state from useAIAnalysis', () => {
    useAIAnalysis.mockReturnValue({
      isAnalyzing: true,
      suggestions: [],
      setSuggestions: vi.fn(),
      analyzeText: mockAnalyzeText,
      rawAIResponse: 'Mock AI response'
    });

    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    expect(result.current.isAnalyzing).toBe(true);
  });

  it('should return suggestions from useAIAnalysis', () => {
    const mockSuggestions = [
      { id: '1', text: 'suggestion 1' },
      { id: '2', text: 'suggestion 2' }
    ];
    useAIAnalysis.mockReturnValue({
      isAnalyzing: false,
      suggestions: mockSuggestions,
      setSuggestions: vi.fn(),
      analyzeText: mockAnalyzeText,
      rawAIResponse: 'Mock AI response'
    });

    const { result } = renderHook(() => useEditorAnalysis(mockUser, mockSetShowAuthModal));

    expect(result.current.suggestions).toEqual(mockSuggestions);
  });
});
