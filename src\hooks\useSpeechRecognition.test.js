import { act, renderHook } from '@testing-library/react';
import { vi } from 'vitest';
import { useSpeechRecognition } from './useSpeechRecognition';

// Mock SpeechRecognition API
const mockSpeechRecognition = {
  start: vi.fn(),
  stop: vi.fn(),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  continuous: false,
  interimResults: false,
  lang: '',
  maxAlternatives: 1,
  onresult: null,
  onerror: null,
  onstart: null,
  onend: null
};

const mockSpeechRecognitionConstructor = vi.fn(() => mockSpeechRecognition);

// Setup global mocks
global.SpeechRecognition = mockSpeechRecognitionConstructor;
global.webkitSpeechRecognition = mockSpeechRecognitionConstructor;

describe('useSpeechRecognition', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset mock implementation
    mockSpeechRecognition.start.mockClear();
    mockSpeechRecognition.stop.mockClear();
    mockSpeechRecognition.onresult = null;
    mockSpeechRecognition.onerror = null;
    mockSpeechRecognition.onstart = null;
    mockSpeechRecognition.onend = null;
  });

  describe('初始化', () => {
    it('应该返回正确的初始状态', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      expect(result.current.isListening).toBe(false);
      expect(result.current.transcript).toBe('');
      expect(result.current.error).toBeNull();
      expect(result.current.isSupported).toBe(false);
    });

    it('应该提供所有必要的方法', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      expect(typeof result.current.startListening).toBe('function');
      expect(typeof result.current.stopListening).toBe('function');
      expect(typeof result.current.clearTranscript).toBe('function');
      expect(typeof result.current.getFinalTranscript).toBe('function');
      expect(typeof result.current.checkSupport).toBe('function');
    });
  });

  describe('浏览器支持检查', () => {
    it('应该检测到支持的浏览器', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
      });

      // 在测试环境中，我们mock了SpeechRecognition，所以应该被检测为支持
      expect(result.current.isSupported).toBe(true);
    });

    it('应该检测到不支持的浏览器', () => {
      // 临时移除SpeechRecognition支持
      const originalSpeechRecognition = global.SpeechRecognition;
      const originalWebkitSpeechRecognition = global.webkitSpeechRecognition;
      delete global.SpeechRecognition;
      delete global.webkitSpeechRecognition;

      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        const supported = result.current.checkSupport();
        expect(supported).toBe(false);
        expect(result.current.isSupported).toBe(false);
      });

      // 恢复原始值
      global.SpeechRecognition = originalSpeechRecognition;
      global.webkitSpeechRecognition = originalWebkitSpeechRecognition;
    });
  });

  describe('语音识别控制', () => {
    it('应该能够开始语音识别', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      expect(mockSpeechRecognitionConstructor).toHaveBeenCalled();
      expect(mockSpeechRecognition.start).toHaveBeenCalled();
    });

    it('应该能够停止语音识别', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 模拟开始状态
      act(() => {
        if (mockSpeechRecognition.onstart) {
          mockSpeechRecognition.onstart();
        }
      });

      act(() => {
        result.current.stopListening();
      });

      expect(mockSpeechRecognition.stop).toHaveBeenCalled();
    });

    it('应该正确配置SpeechRecognition', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      expect(mockSpeechRecognition.continuous).toBe(true);
      expect(mockSpeechRecognition.interimResults).toBe(true);
      expect(mockSpeechRecognition.lang).toBe('en-US');
      expect(mockSpeechRecognition.maxAlternatives).toBe(1);
    });

    it('应该在不支持时显示错误', () => {
      // 临时移除SpeechRecognition支持
      const originalSpeechRecognition = global.SpeechRecognition;
      const originalWebkitSpeechRecognition = global.webkitSpeechRecognition;
      delete global.SpeechRecognition;
      delete global.webkitSpeechRecognition;

      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.startListening();
      });

      expect(result.current.error).toBe('您的浏览器不支持语音识别功能');

      // 恢复原始值
      global.SpeechRecognition = originalSpeechRecognition;
      global.webkitSpeechRecognition = originalWebkitSpeechRecognition;
    });
  });

  describe('语音识别事件处理', () => {
    it('应该处理onstart事件', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      act(() => {
        if (mockSpeechRecognition.onstart) {
          mockSpeechRecognition.onstart();
        }
      });

      expect(result.current.isListening).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it('应该处理onend事件', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      act(() => {
        if (mockSpeechRecognition.onend) {
          mockSpeechRecognition.onend();
        }
      });

      expect(result.current.isListening).toBe(false);
    });

    it('应该处理onresult事件', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 模拟语音识别结果
      const mockEvent = {
        resultIndex: 0,
        results: [
          {
            0: { transcript: 'hello world' },
            isFinal: true
          }
        ]
      };

      act(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult(mockEvent);
        }
      });

      expect(result.current.transcript).toBe('hello world');
    });

    it('应该处理临时结果', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 模拟临时结果
      const mockEvent = {
        resultIndex: 0,
        results: [
          {
            0: { transcript: 'hello' },
            isFinal: false
          }
        ]
      };

      act(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult(mockEvent);
        }
      });

      expect(result.current.transcript).toBe('hello');
    });

    it('应该处理多个结果', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 第一个最终结果
      const mockEvent1 = {
        resultIndex: 0,
        results: [
          {
            0: { transcript: 'hello ' },
            isFinal: true
          }
        ]
      };

      act(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult(mockEvent1);
        }
      });

      // 第二个临时结果
      const mockEvent2 = {
        resultIndex: 1,
        results: [
          {
            0: { transcript: 'hello ' },
            isFinal: true
          },
          {
            0: { transcript: 'world' },
            isFinal: false
          }
        ]
      };

      act(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult(mockEvent2);
        }
      });

      expect(result.current.transcript).toBe('hello world');
    });
  });

  describe('错误处理', () => {
    it('应该处理no-speech错误', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      act(() => {
        if (mockSpeechRecognition.onerror) {
          mockSpeechRecognition.onerror({ error: 'no-speech' });
        }
      });

      expect(result.current.error).toBe('没有检测到语音，请重试');
      expect(result.current.isListening).toBe(false);
    });

    it('应该处理audio-capture错误', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      act(() => {
        if (mockSpeechRecognition.onerror) {
          mockSpeechRecognition.onerror({ error: 'audio-capture' });
        }
      });

      expect(result.current.error).toBe('无法访问麦克风，请检查权限设置');
    });

    it('应该处理not-allowed错误', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      act(() => {
        if (mockSpeechRecognition.onerror) {
          mockSpeechRecognition.onerror({ error: 'not-allowed' });
        }
      });

      expect(result.current.error).toBe('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
    });

    it('应该处理network错误', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      act(() => {
        if (mockSpeechRecognition.onerror) {
          mockSpeechRecognition.onerror({ error: 'network' });
        }
      });

      expect(result.current.error).toBe('网络错误，请检查网络连接');
    });

    it('应该处理未知错误', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      act(() => {
        if (mockSpeechRecognition.onerror) {
          mockSpeechRecognition.onerror({ error: 'unknown-error' });
        }
      });

      expect(result.current.error).toBe('语音识别出错: unknown-error');
    });

    it('应该处理启动失败', () => {
      mockSpeechRecognition.start.mockImplementation(() => {
        throw new Error('Start failed');
      });

      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      expect(result.current.error).toBe('启动语音识别失败，请重试');
    });
  });

  describe('转录文本管理', () => {
    it('应该能够清除转录文本', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 添加一些转录文本
      const mockEvent = {
        resultIndex: 0,
        results: [
          {
            0: { transcript: 'hello world' },
            isFinal: true
          }
        ]
      };

      act(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult(mockEvent);
        }
      });

      expect(result.current.transcript).toBe('hello world');

      act(() => {
        result.current.clearTranscript();
      });

      expect(result.current.transcript).toBe('');
    });

    it('应该能够获取最终转录文本', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 添加最终转录文本
      const mockEvent = {
        resultIndex: 0,
        results: [
          {
            0: { transcript: '  hello world  ' },
            isFinal: true
          }
        ]
      };

      act(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult(mockEvent);
        }
      });

      const finalTranscript = result.current.getFinalTranscript();
      expect(finalTranscript).toBe('hello world'); // 应该去除前后空格
    });

    it('应该在开始新的识别时清除之前的转录', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 添加一些转录文本
      const mockEvent = {
        resultIndex: 0,
        results: [
          {
            0: { transcript: 'old text' },
            isFinal: true
          }
        ]
      };

      act(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult(mockEvent);
        }
      });

      expect(result.current.transcript).toBe('old text');

      // 停止并重新开始
      act(() => {
        result.current.stopListening();
      });

      act(() => {
        result.current.startListening();
      });

      expect(result.current.transcript).toBe('');
    });
  });

  describe('状态管理', () => {
    it('应该防止重复启动', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.startListening();
      });

      // 模拟已经在监听状态
      act(() => {
        if (mockSpeechRecognition.onstart) {
          mockSpeechRecognition.onstart();
        }
      });

      const startCallCount = mockSpeechRecognition.start.mock.calls.length;

      act(() => {
        result.current.startListening();
      });

      // 不应该再次调用start
      expect(mockSpeechRecognition.start.mock.calls.length).toBe(startCallCount);
    });

    it('应该防止在未监听时停止', () => {
      const { result } = renderHook(() => useSpeechRecognition());

      act(() => {
        result.current.checkSupport();
        result.current.stopListening();
      });

      expect(mockSpeechRecognition.stop).not.toHaveBeenCalled();
    });
  });
});
