import { useState } from 'react';
import { analyzeText as analyzeTextService } from '../services/ai/aiService';
import { generateUniqueId } from '../utils/idGenerator';

// 解析AI响应，提取具体建议并找到对应位置
const parseAIResponse = (aiResponse, originalText) => {
  const suggestions = [];
  let suggestionId = 1;

  console.log('Parsing AI response:', aiResponse);

  // 常见的错误词汇映射
  const commonErrors = {
    'i am': { correct: 'I am', category: 'grammar', explanation: '第一人称代词"I"应该大写' },
    'aloser': { correct: 'a loser', category: 'grammar', explanation: '应该分开写成两个单词' },
    'gloser': { correct: 'closer', category: 'grammar', explanation: '拼写错误，正确拼写是"closer"' },
    'recieve': { correct: 'receive', category: 'grammar', explanation: '拼写错误，正确拼写是"receive"' },
    'alot': { correct: 'a lot', category: 'grammar', explanation: '"a lot"应该分开写成两个单词' },
    'cant': { correct: "can't", category: 'grammar', explanation: '缩写形式需要使用撇号' },
    'dont': { correct: "don't", category: 'grammar', explanation: '缩写形式需要使用撇号' },
    'wont': { correct: "won't", category: 'grammar', explanation: '缩写形式需要使用撇号' },
    'im': { correct: "I'm", category: 'grammar', explanation: '缩写形式需要使用撇号，且"I"需要大写' },
    'ive': { correct: "I've", category: 'grammar', explanation: '缩写形式需要使用撇号，且"I"需要大写' },
    'how could i': { correct: 'How could I', category: 'grammar', explanation: '句首单词和第一人称代词"I"应该大写' },
    'how can i': { correct: 'How can I', category: 'grammar', explanation: '句首单词和第一人称代词"I"应该大写' },
    'reach my dream': { correct: 'achieve my dream', category: 'style', explanation: '"reach a dream"是不自然的表达，建议使用"achieve/realize a dream"' }
  };

  // 首先检查常见错误
  const textLower = originalText.toLowerCase();
  Object.keys(commonErrors).forEach(errorWord => {
    const index = textLower.indexOf(errorWord);
    if (index !== -1) {
      const error = commonErrors[errorWord];
      suggestions.push({
        id: `ai_suggestion_${suggestionId++}`,
        category: error.category,
        type: 'ai_suggestion',
        original: originalText.substring(index, index + errorWord.length),
        suggestion: error.correct,
        explanation: error.explanation,
        detailedAnalysis: `${error.category === 'grammar' ? '语法' : '风格'}问题：${error.explanation}`,
        positions: [{ start: index, end: index + errorWord.length }],
        highlightText: originalText.substring(index, index + errorWord.length),
        canApply: true,
        replacement: error.correct
      });
    }
  });

  // 然后查找AI响应中提到的其他具体词汇和短语
  const patterns = [
    /"([^"]+)"/g,  // 匹配英文双引号中的内容
    /"([^"]+)"/g,  // 匹配中文引号
    /「([^」]+)」/g,  // 匹配日式引号
    /原文[：:] ?([^，。,.;；]+)/g,  // 匹配"原文："后面的内容
    /错误[：:] ?([^，。,.;；]+)/g,  // 匹配"错误："后面的内容
    /问题[：:] ?([^，。,.;；]+)/g,  // 匹配"问题："后面的内容
    /应该(?:写成|改为|使用)[：:] ?([^，。,.;；]+)/g,  // 匹配"应该写成/改为/使用："后面的内容
    /将 ?"?([^"，。,.;；]+)"? ?改为/g,  // 匹配"将X改为"的模式
    /把 ?"?([^"，。,.;；]+)"? ?改为/g,  // 匹配"把X改为"的模式
    /"?([^"，。,.;；]{3,30})"? ?应(?:该|当)(?:写成|改为|使用)/g  // 匹配"X应该写成/改为/使用"的模式
  ];

  const foundIssues = new Set();

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(aiResponse)) !== null) {
      const issueText = match[1];
      const issueTextLower = issueText.toLowerCase();

      // 跳过已经处理过的常见错误
      if (commonErrors[issueTextLower]) continue;

      // 在原文中查找这个问题文本
      const index = originalText.indexOf(issueText);
      if (index !== -1 && !foundIssues.has(issueTextLower)) {
        foundIssues.add(issueTextLower);

        // 提取相关上下文
        const context = extractRelevantContext(aiResponse, issueText);

        // 确定建议类型和具体建议
        let category = 'clarity';
        let suggestionText = '查看AI建议';
        let replacement = null;

        // 根据上下文内容确定类别和建议
        if (context.includes('语法') || context.includes('拼写') || context.includes('时态') ||
          context.includes('单词') || context.includes('错误') || context.includes('应该')) {
          category = 'grammar';

          // 尝试从上下文中提取具体的修改建议
          const correctionMatch = context.match(/应该是?["""']?([^"""'，。,.;；]+)["""']?|改为["""']?([^"""'，。,.;；]+)["""']?|正确的是["""']?([^"""'，。,.;；]+)["""']?|写作["""']?([^"""'，。,.;；]+)["""']?|写成["""']?([^"""'，。,.;；]+)["""']?|使用["""']?([^"""'，。,.;；]+)["""']?/);

          if (correctionMatch) {
            // 找到第一个非undefined的匹配组
            const correction = correctionMatch.slice(1).find(match => match !== undefined);
            if (correction) {
              suggestionText = correction;
              replacement = correction;
            }
          } else {
            // 如果没有匹配到具体的修改建议，默认为语法修正建议
            suggestionText = '语法修正建议';
          }
        } else if (context.includes('风格') || context.includes('表达') || context.includes('清晰')) {
          category = 'style';
          suggestionText = '风格改进建议';
        } else if (context.includes('词汇') || context.includes('用词') || context.includes('单词选择')) {
          category = 'vocabulary';
          suggestionText = '词汇建议';
        }

        // 创建建议对象
        suggestions.push({
          id: `ai_suggestion_${suggestionId++}`,
          category,
          type: 'ai_suggestion',
          original: issueText,
          suggestion: suggestionText,
          explanation: context,
          detailedAnalysis: context,
          positions: [{ start: index, end: index + issueText.length }],
          highlightText: issueText,
          canApply: replacement !== null,
          replacement
        });
      }
    }
  });

  // 如果没有找到任何建议，尝试使用更通用的方法提取建议
  if (suggestions.length === 0) {
    // 尝试提取句子级别的建议
    const sentencePatterns = [
      /句子 [""]([^"""]+)[""] 应该/g,
      /句子 [""]([^"""]+)[""] 可以/g,
      /[""]([^"""]+)[""] 这个句子/g
    ];

    sentencePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(aiResponse)) !== null) {
        const sentenceText = match[1];
        const index = originalText.indexOf(sentenceText);

        if (index !== -1 && !foundIssues.has(sentenceText)) {
          foundIssues.add(sentenceText);

          // 提取相关上下文
          const context = extractRelevantContext(aiResponse, sentenceText);

          suggestions.push({
            id: `ai_suggestion_${suggestionId++}`,
            category: 'clarity',
            type: 'ai_suggestion',
            original: sentenceText,
            suggestion: '句子结构建议',
            explanation: context,
            detailedAnalysis: context,
            positions: [{ start: index, end: index + sentenceText.length }],
            highlightText: sentenceText,
            canApply: false,
            replacement: null
          });
        }
      }
    });
  }

  // 如果仍然没有找到建议，尝试分析整个文本
  if (suggestions.length === 0 && originalText.trim().length > 0) {
    // 将整个AI响应作为一个整体建议
    suggestions.push({
      id: `ai_suggestion_${suggestionId++}`,
      category: 'style',
      type: 'ai_suggestion',
      original: originalText,
      suggestion: 'AI写作建议',
      explanation: aiResponse,
      detailedAnalysis: aiResponse,
      positions: [{ start: 0, end: originalText.length }],
      highlightText: originalText,
      canApply: false,
      replacement: null
    });
  }

  return suggestions;
};

// 从AI响应中提取与特定问题相关的上下文
const extractRelevantContext = (aiResponse, issueText) => {
  // 尝试找到包含问题文本的段落
  const paragraphs = aiResponse.split(/\n\s*\n/);

  for (const paragraph of paragraphs) {
    if (paragraph.includes(issueText)) {
      return paragraph.trim();
    }
  }

  // 如果找不到特定段落，返回包含问题文本的句子及其前后句子
  const sentences = aiResponse.split(/[.!?。！？]+/);
  const relevantSentences = [];

  for (let i = 0; i < sentences.length; i++) {
    if (sentences[i].includes(issueText)) {
      // 添加前一句（如果有）
      if (i > 0) relevantSentences.push(sentences[i - 1].trim());

      // 添加当前句
      relevantSentences.push(sentences[i].trim());

      // 添加后一句（如果有）
      if (i < sentences.length - 1) relevantSentences.push(sentences[i + 1].trim());

      break;
    }
  }

  if (relevantSentences.length > 0) {
    return relevantSentences.join('。 ');
  }

  // 如果上述方法都找不到相关上下文，返回完整的AI响应
  return aiResponse;
};

// 自定义钩子，用于处理AI分析
const useAIAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [rawAIResponse, setRawAIResponse] = useState('');
  const [error, setError] = useState(null);

  // 分析文本函数 - 使用双阶段AI调用
  const analyzeText = async (text, userId = null) => {
    if (!text || text.trim() === '') {
      setSuggestions([]);
      setRawAIResponse('');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      // 使用双阶段AI服务
      const result = await analyzeTextService(text, userId);

      // 设置原始AI响应
      setRawAIResponse(result.rawAnalysis);

      // 处理结构化建议
      const structuredSuggestions = result.suggestions.map((suggestion, index) => {
        // 查找原文中的位置
        const position = text.indexOf(suggestion.original);

        return {
          id: `ai_suggestion_${generateUniqueId()}_${index}`,
          category: suggestion.category || 'style',
          type: 'ai_suggestion',
          original: suggestion.original,
          suggestion: suggestion.replacement || '查看AI建议',
          explanation: suggestion.explanation || '',
          detailedAnalysis: suggestion.explanation || '',
          positions: position !== -1 ? [{
            start: position,
            end: position + suggestion.original.length
          }] : [],
          highlightText: suggestion.original,
          canApply: !!suggestion.replacement,
          replacement: suggestion.replacement
        };
      });

      // 过滤掉没有找到位置的建议
      const validSuggestions = structuredSuggestions.filter(s => s.positions.length > 0);

      setSuggestions(validSuggestions);
      console.log('AI分析完成，找到建议:', validSuggestions.length);
    } catch (err) {
      console.error('分析文本时出错:', err);
      setError(err.message);
      setRawAIResponse('');
      setSuggestions([]);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return {
    isAnalyzing,
    suggestions,
    setSuggestions,
    rawAIResponse,
    error,
    analyzeText
  };
};

export default useAIAnalysis;
export { parseAIResponse, extractRelevantContext };