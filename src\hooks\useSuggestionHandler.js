import { useState } from 'react';
import { autoSaveWritingText } from '../services/writing/writingTextService';

export const useSuggestionHandler = (text, setText, suggestions, setSuggestions) => {
  const [appliedSuggestions, setAppliedSuggestions] = useState(new Set());

  // 应用建议
  const applySuggestion = (suggestion) => {
    if (!suggestion.canApply || appliedSuggestions.has(suggestion.id)) {
      return;
    }

    let newText = text;
    let lengthDiff = 0; // 记录文本长度变化

    if (suggestion.type === 'capitalization') {
      // 处理大小写修正 - 从后往前替换避免位置偏移
      const sortedPositions = [...suggestion.positions].sort((a, b) => b.start - a.start);
      sortedPositions.forEach(pos => {
        const before = newText.substring(0, pos.start);
        const after = newText.substring(pos.end);
        const originalLength = pos.end - pos.start;
        newText = before + suggestion.replacement + after;
        lengthDiff += suggestion.replacement.length - originalLength;
      });
    } else if (suggestion.type === 'spelling') {
      // 处理拼写错误修正
      const pos = suggestion.positions[0];
      const before = newText.substring(0, pos.start);
      const after = newText.substring(pos.end);
      const originalLength = pos.end - pos.start;
      newText = before + suggestion.replacement + after;
      lengthDiff = suggestion.replacement.length - originalLength;
    } else if (suggestion.type === 'punctuation' && suggestion.isAppend) {
      // 处理句尾标点添加
      newText = text.trim() + suggestion.replacement;
      lengthDiff = suggestion.replacement.length;
    } else if (suggestion.positions && suggestion.positions.length > 0) {
      // 处理一般的文本替换
      const pos = suggestion.positions[0]; // 取第一个位置
      const before = newText.substring(0, pos.start);
      const after = newText.substring(pos.end);
      const originalLength = pos.end - pos.start;
      newText = before + suggestion.replacement + after;
      lengthDiff = suggestion.replacement.length - originalLength;
    }

    // 更新文本
    setText(newText);

    // 标记建议为已应用
    setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));

    // 延迟移除已应用的建议，但保留其他有效建议
    setTimeout(() => {
      let finalSuggestions = [];
      setSuggestions(prev => {
        const newSuggestions = prev.filter(s => {
          // 移除当前应用的建议
          if (s.id === suggestion.id) return false;

          // 对于其他建议，只检查是否与当前应用的建议直接重叠
          if (s.positions && s.positions.length > 0 && suggestion.positions && suggestion.positions.length > 0) {
            const pos = s.positions[0];
            const appliedPos = suggestion.positions[0];

            // 只移除与当前应用建议直接重叠的建议
            const isOverlapping = (pos.start >= appliedPos.start && pos.start < appliedPos.end) ||
                                 (pos.end > appliedPos.start && pos.end <= appliedPos.end) ||
                                 (pos.start <= appliedPos.start && pos.end >= appliedPos.end);

            if (isOverlapping) {
              return false;
            }

            // 对于位置在应用建议之后的建议，调整其位置
            if (pos.start >= appliedPos.end) {
              // 更新建议的位置信息以反映文本变化
              s.positions = s.positions.map(p => ({
                start: p.start + lengthDiff,
                end: p.end + lengthDiff
              }));
            }
          }

          return true;
        });
        
        finalSuggestions = newSuggestions;
        return newSuggestions;
      });

      // 在 setSuggestions 更新之后，使用最新的状态来保存
      autoSaveWritingText(newText, finalSuggestions);

      // 清理已应用建议的记录
      setAppliedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(suggestion.id);
        return newSet;
      });
    }, 1500);
  };

  // 应用所有可应用的建议
  const applyAllSuggestions = () => {
    const applicableSuggestions = suggestions.filter(s =>
      s.canApply && !appliedSuggestions.has(s.id)
    );

    if (applicableSuggestions.length === 0) return;

    // 按位置从后往前排序，避免位置偏移问题
    const sortedSuggestions = applicableSuggestions.sort((a, b) => {
      const aPos = a.positions?.[0]?.start || 0;
      const bPos = b.positions?.[0]?.start || 0;
      return bPos - aPos;
    });

    let newText = text;
    const appliedIds = new Set();

    sortedSuggestions.forEach(suggestion => {
      if (suggestion.type === 'capitalization') {
        const sortedPositions = [...suggestion.positions].sort((a, b) => b.start - a.start);
        sortedPositions.forEach(pos => {
          const before = newText.substring(0, pos.start);
          const after = newText.substring(pos.end);
          newText = before + suggestion.replacement + after;
        });
      } else if (suggestion.type === 'punctuation' && suggestion.isAppend) {
        newText = newText.trim() + suggestion.replacement;
      } else if (suggestion.positions && suggestion.positions.length > 0) {
        const pos = suggestion.positions[0];
        const before = newText.substring(0, pos.start);
        const after = newText.substring(pos.end);
        newText = before + suggestion.replacement + after;
      }
      appliedIds.add(suggestion.id);
    });

    setText(newText);
    setAppliedSuggestions(prev => new Set([...prev, ...appliedIds]));

    // 延迟移除已应用的建议
    setTimeout(() => {
      let finalSuggestions = [];
      // 移除已应用的建议，保留其他建议
      setSuggestions(prev => {
        const newSuggestions = prev.filter(s => {
          // 移除已应用的建议
          return !appliedIds.has(s.id);
        });
        finalSuggestions = newSuggestions;
        return newSuggestions;
      });

      // 更新 localStorage 中的建议
      autoSaveWritingText(newText, finalSuggestions);

      setAppliedSuggestions(new Set());
    }, 1500);
  };

  // 忽略建议
  const dismissSuggestion = (suggestion) => {
    setSuggestions(prev => {
      const newSuggestions = prev.filter(s => s.id !== suggestion.id);
      // 更新 localStorage 中的建议
      autoSaveWritingText(text, newSuggestions);
      return newSuggestions;
    });
  };

  return {
    appliedSuggestions,
    applySuggestion,
    applyAllSuggestions,
    dismissSuggestion
  };
};
