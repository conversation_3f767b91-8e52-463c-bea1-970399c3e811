// 测试 "nothing" 单词查询
// 验证 ECDICT 数据加载和查询功能

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

const testNothingWord = async () => {
  console.log('🧪 测试 "nothing" 单词查询...');
  console.log('='.repeat(50));
  
  try {
    const startTime = performance.now();
    const result = await getWordDetails('nothing', 'ecdict');
    const endTime = performance.now();
    
    const queryTime = endTime - startTime;
    
    console.log(`⏱️ 查询时间: ${queryTime.toFixed(2)}ms`);
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log('📖 数据源:', result.source);
      console.log('🔤 单词:', result.word);
      
      if (result.phonetic) {
        console.log('🔊 音标:', result.phonetic);
      }
      
      if (result.translation) {
        console.log('🇨🇳 中文释义:', result.translation);
      }
      
      if (result.definition) {
        console.log('🇺🇸 英文释义:', result.definition);
      }
      
      if (result.tags && result.tags.length > 0) {
        console.log('🏷️ 考试标签:', result.tags.join(', '));
      }
      
      if (result.collins > 0) {
        console.log('⭐ 柯林斯星级:', result.collins);
      }
      
      if (result.oxford) {
        console.log('📚 牛津3000: 是');
      }
      
      if (result.bnc > 0) {
        console.log('📊 BNC词频:', result.bnc);
      }
      
      if (result.frq > 0) {
        console.log('📈 当代词频:', result.frq);
      }
      
      if (result.exchange && Object.keys(result.exchange).length > 0) {
        console.log('🔄 词形变化:', result.exchange);
      }
      
      return true;
    } else {
      console.log('❌ 查询失败');
      console.log('📝 错误信息:', result?.error || '未找到该单词');
      return false;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return false;
  }
};

// 测试其他常见单词
const testCommonWords = async () => {
  console.log('\n🧪 测试其他常见单词...');
  console.log('='.repeat(50));
  
  const commonWords = ['hello', 'world', 'good', 'bad', 'beautiful', 'love'];
  const results = {};
  
  for (const word of commonWords) {
    try {
      const result = await getWordDetails(word, 'ecdict');
      const success = result && !result.notFound;
      
      console.log(`${success ? '✅' : '❌'} ${word}: ${success ? result.source : '未找到'}`);
      
      results[word] = success;
    } catch (error) {
      console.log(`❌ ${word}: 查询出错 - ${error.message}`);
      results[word] = false;
    }
  }
  
  const successCount = Object.values(results).filter(r => r).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n📊 测试结果: ${successCount}/${totalCount} 成功`);
  
  return results;
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始 ECDICT 数据修复验证...');
  console.log('='.repeat(60));
  
  try {
    // 测试 "nothing" 单词
    const nothingResult = await testNothingWord();
    
    // 测试其他常见单词
    const commonResults = await testCommonWords();
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    if (nothingResult) {
      console.log('✅ "nothing" 单词查询修复成功！');
    } else {
      console.log('❌ "nothing" 单词查询仍有问题');
    }
    
    return {
      nothingResult,
      commonResults
    };
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testNothingWord,
  testCommonWords,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testNothingWord = {
    testNothingWord,
    testCommonWords,
    runAllTests
  };
  
  console.log('🧪 "nothing" 单词测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testNothingWord.runAllTests() // 运行所有测试');
  console.log('  - window.testNothingWord.testNothingWord() // 测试 "nothing" 单词');
  console.log('  - window.testNothingWord.testCommonWords() // 测试常见单词');
}