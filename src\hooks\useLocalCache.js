import { useState, useEffect, useCallback } from 'react';
import localCacheService from '../services/cache/localCacheService';

/**
 * 本地缓存管理Hook
 * 提供缓存数据的获取、保存和同步功能
 */
export const useLocalCache = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [syncStatus, setSyncStatus] = useState('idle');
  const [cacheStats, setCacheStats] = useState(null);

  // 更新缓存统计信息
  const updateCacheStats = useCallback(() => {
    const stats = localCacheService.getCacheStats();
    setCacheStats(stats);
  }, []);

  // 监听同步状态变化
  useEffect(() => {
    const handleSyncStatus = (status) => {
      setSyncStatus(status);
      if (status === 'completed' || status === 'failed') {
        updateCacheStats();
      }
    };

    localCacheService.addSyncListener(handleSyncStatus);
    updateCacheStats();

    return () => {
      localCacheService.removeSyncListener(handleSyncStatus);
    };
  }, [updateCacheStats]);

  /**
   * 获取聊天历史
   */
  const getChatHistory = useCallback(async (limit = 50, forceRefresh = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await localCacheService.getChatHistory(limit, forceRefresh);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 获取AI分析历史
   */
  const getAnalysisHistory = useCallback(async (limit = 50, forceRefresh = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await localCacheService.getAnalysisHistory(limit, forceRefresh);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 获取字典搜索历史
   */
  const getDictionarySearchHistory = useCallback(async (limit = 20, forceRefresh = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await localCacheService.getDictionarySearchHistory(limit, forceRefresh);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 获取写作历史
   */
  const getWritingHistory = useCallback(async (limit = 50, forceRefresh = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await localCacheService.getWritingHistory(limit, forceRefresh);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 获取日记历史
   */
  const getDiaryHistory = useCallback(async (limit = 30, forceRefresh = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await localCacheService.getDiaryHistory(limit, forceRefresh);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 保存数据
   */
  const saveData = useCallback(async (type, data) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await localCacheService.saveData(type, data);
      updateCacheStats();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [updateCacheStats]);

  /**
   * 手动触发同步
   */
  const triggerSync = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await localCacheService.triggerBackgroundSync();
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 清除指定类型的缓存
   */
  const clearCache = useCallback((type) => {
    localCacheService.invalidateCache(type);
    updateCacheStats();
  }, [updateCacheStats]);

  /**
   * 清除所有缓存
   */
  const clearAllCache = useCallback(() => {
    localCacheService.clearAllCache();
    updateCacheStats();
  }, [updateCacheStats]);

  /**
   * 启动后台同步
   */
  const startBackgroundSync = useCallback(() => {
    localCacheService.startBackgroundSync();
  }, []);

  /**
   * 停止后台同步
   */
  const stopBackgroundSync = useCallback(() => {
    localCacheService.stopBackgroundSync();
  }, []);

  return {
    // 数据获取方法
    getChatHistory,
    getAnalysisHistory,
    getDictionarySearchHistory,
    getWritingHistory,
    getDiaryHistory,
    
    // 数据保存方法
    saveData,
    
    // 同步控制
    triggerSync,
    startBackgroundSync,
    stopBackgroundSync,
    
    // 缓存管理
    clearCache,
    clearAllCache,
    
    // 状态信息
    isLoading,
    error,
    syncStatus,
    cacheStats,
    
    // 工具方法
    updateCacheStats
  };
};

/**
 * 专门用于聊天历史的Hook
 */
export const useChatCache = () => {
  const cache = useLocalCache();
  
  return {
    getChatHistory: cache.getChatHistory,
    saveChatSession: (data) => cache.saveData('chat', data),
    isLoading: cache.isLoading,
    error: cache.error,
    syncStatus: cache.syncStatus
  };
};

/**
 * 专门用于AI分析历史的Hook
 */
export const useAnalysisCache = () => {
  const cache = useLocalCache();
  
  return {
    getAnalysisHistory: cache.getAnalysisHistory,
    saveAnalysis: (data) => cache.saveData('analysis', data),
    isLoading: cache.isLoading,
    error: cache.error,
    syncStatus: cache.syncStatus
  };
};

/**
 * 专门用于字典搜索历史的Hook
 */
export const useDictionaryCache = () => {
  const cache = useLocalCache();
  
  return {
    getSearchHistory: cache.getDictionarySearchHistory,
    saveSearchTerm: (term) => cache.saveData('dictionary', term),
    isLoading: cache.isLoading,
    error: cache.error,
    syncStatus: cache.syncStatus
  };
};

/**
 * 专门用于写作历史的Hook
 */
export const useWritingCache = () => {
  const cache = useLocalCache();
  
  return {
    getWritingHistory: cache.getWritingHistory,
    saveWriting: (data) => cache.saveData('writing', data),
    isLoading: cache.isLoading,
    error: cache.error,
    syncStatus: cache.syncStatus
  };
};

/**
 * 专门用于日记历史的Hook
 */
export const useDiaryCache = () => {
  const cache = useLocalCache();
  
  return {
    getDiaryHistory: cache.getDiaryHistory,
    saveDiary: (data) => cache.saveData('diary', data),
    isLoading: cache.isLoading,
    error: cache.error,
    syncStatus: cache.syncStatus
  };
};
