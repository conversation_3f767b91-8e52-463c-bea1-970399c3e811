import React from 'react';
import { X } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

const AIResponseSidebar = ({
  isOpen,
  onClose,
  content,
  isDarkMode
}) => {
  if (!isOpen) return null;

  // 预处理内容，移除"分析与改进建议"标题
  const preprocessContent = (content) => {
    if (!content) return '';
    return content.replace(/^分析与改进建议\s*$/m, '').trim();
  };

  const processedContent = preprocessContent(content);

  // 自定义渲染器
  const components = {
    h1: ({ node, ...props }) => (
      <h1
        style={{
          color: isDarkMode ? '#E8DCC6' : '#5D4037',
          fontWeight: 'bold',
          fontSize: '1.3em',
          marginTop: '1em',
          marginBottom: '0.5em'
        }}
        {...props}
      />
    ),
    h2: ({ node, ...props }) => (
      <h2
        style={{
          color: isDarkMode ? '#E8DCC6' : '#5D4037',
          fontWeight: 'bold',
          fontSize: '1.1em',
          marginTop: '1.5em',
          marginBottom: '0.8em',
          backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.4)' : 'rgba(240, 230, 210, 0.6)',
          padding: '8px 16px',
          borderRadius: '12px',
          display: 'inline-block',
          backdropFilter: 'blur(5px)',
          border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.6)' : 'rgba(230, 215, 184, 0.8)'}`
        }}
        {...props}
      />
    ),
    h3: ({ node, ...props }) => (
      <h3
        style={{
          color: isDarkMode ? '#E8DCC6' : '#5D4037',
          fontWeight: 'bold',
          fontSize: '1em',
          marginTop: '1.2em',
          marginBottom: '0.6em',
          backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.3)' : 'rgba(240, 230, 210, 0.5)',
          padding: '6px 12px',
          borderRadius: '10px',
          display: 'inline-block',
          backdropFilter: 'blur(5px)',
          border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.5)' : 'rgba(230, 215, 184, 0.7)'}`
        }}
        {...props}
      />
    ),
    p: ({ node, children, ...props }) => {
      const text = children.toString();
      const isHeadingParagraph =
        text.includes('拼写与标点问题') ||
        text.includes('语法与表达问题') ||
        text.includes('风格优化');

      if (text.trim() === '分析与改进建议') {
        return null;
      }

      if (isHeadingParagraph) {
        return (
          <p
            style={{
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontWeight: 'bold',
              fontSize: '1.1em',
              marginTop: '1.5em',
              marginBottom: '0.8em',
              backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.4)' : 'rgba(240, 230, 210, 0.6)',
              padding: '8px 16px',
              borderRadius: '12px',
              display: 'inline-block',
              backdropFilter: 'blur(5px)',
              border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.6)' : 'rgba(230, 215, 184, 0.8)'}`
            }}
            {...props}
          >
            {children}
          </p>
        );
      }

      return (
        <p 
          style={{ 
            marginTop: '0.8em', 
            marginBottom: '0.8em',
            fontSize: '14px',
            lineHeight: '1.6',
            opacity: 0.9
          }} 
          {...props}
        >
          {children}
        </p>
      );
    },
    li: ({ node, ...props }) => (
      <li
        style={{
          marginTop: '0.4em',
          marginBottom: '0.4em',
          fontSize: '14px',
          lineHeight: '1.5',
          paddingLeft: '0.5em',
          opacity: 0.9
        }}
        {...props}
      />
    )
  };

  return (
    <>
      {/* 背景遮罩 */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-20 transition-opacity duration-300"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.1)'
          }}
          onClick={onClose}
        />
      )}
      
      {/* 侧边栏 */}
      <div 
        className="fixed right-4 top-4 z-30 transition-all duration-300 ease-in-out"
        style={{
          width: 'min(400px, 40vw)',
          minWidth: '320px',
          height: 'calc(100vh - 32px)',
          backgroundColor: isDarkMode ? '#2A241D' : '#FEFCF5',
          borderRadius: '20px',
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`,
          transform: isOpen ? 'translateX(0) scale(1)' : 'translateX(100%) scale(0.95)',
          opacity: isOpen ? 1 : 0,
          boxShadow: isDarkMode 
            ? '0 20px 60px rgba(0, 0, 0, 0.4), 0 8px 24px rgba(0, 0, 0, 0.3)' 
            : '0 20px 60px rgba(93, 64, 55, 0.15), 0 8px 24px rgba(93, 64, 55, 0.1)',
          backdropFilter: 'blur(10px)',
          overflow: 'hidden'
        }}
      >
      {/* 头部 */}
      <div 
        className="flex items-center justify-between p-6 border-b"
        style={{
          borderColor: isDarkMode ? 'rgba(74, 63, 53, 0.3)' : 'rgba(230, 215, 184, 0.5)',
          backgroundColor: 'transparent',
          backdropFilter: 'blur(10px)'
        }}
      >
        <h3 
          className="text-lg font-semibold"
          style={{
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            fontFamily: 'Georgia, "Noto Serif SC", serif'
          }}
        >
          分析与改进建议
        </h3>
        <button
          onClick={onClose}
          className="p-2 rounded-full transition-all duration-200 hover:scale-110"
          style={{
            color: isDarkMode ? '#C4B59A' : '#8B4513',
            backgroundColor: isDarkMode ? 'rgba(74, 63, 53, 0.3)' : 'rgba(240, 230, 210, 0.5)',
            backdropFilter: 'blur(10px)',
            border: `1px solid ${isDarkMode ? 'rgba(74, 63, 53, 0.5)' : 'rgba(230, 215, 184, 0.7)'}`
          }}
          title="关闭建议面板"
        >
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* 内容区域 */}
      <div 
        className="flex-1 overflow-y-auto p-6"
        style={{
          height: 'calc(100vh - 140px)',
          scrollbarWidth: 'thin',
          scrollbarColor: isDarkMode ? 'rgba(74, 63, 53, 0.5) transparent' : 'rgba(230, 215, 184, 0.5) transparent'
        }}
      >
        <div 
          className="prose prose-sm max-w-none"
          style={{
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            fontFamily: 'Georgia, "Noto Serif SC", serif'
          }}
        >
          <ReactMarkdown components={components}>{processedContent}</ReactMarkdown>
        </div>
      </div>

      <style>{`
        /* 自定义滚动条样式 */
        div::-webkit-scrollbar {
          width: 4px;
        }
        
        div::-webkit-scrollbar-track {
          background: transparent;
          border-radius: 10px;
        }
        
        div::-webkit-scrollbar-thumb {
          background: ${isDarkMode ? 'rgba(74, 63, 53, 0.4)' : 'rgba(230, 215, 184, 0.6)'};
          border-radius: 10px;
          transition: all 0.2s ease;
        }
        
        div::-webkit-scrollbar-thumb:hover {
          background: ${isDarkMode ? 'rgba(74, 63, 53, 0.7)' : 'rgba(230, 215, 184, 0.9)'};
        }
      `}</style>
      </div>
    </>
  );
};

export default AIResponseSidebar;