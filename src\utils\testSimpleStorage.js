/**
 * 测试简化存储服务
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试存储服务
export const testSimpleStorage = async () => {
  console.log('🧪 开始测试简化存储服务...');
  
  // 初始化服务
  simpleStorageService.init('test-user-123');
  
  // 测试聊天历史
  console.log('📝 测试聊天历史...');
  const chatData = {
    messages: [
      { id: 1, type: 'user', content: 'Hello', timestamp: new Date() },
      { id: 2, type: 'ai', content: 'Hi there!', timestamp: new Date() }
    ],
    sessionTitle: 'Test Chat'
  };
  
  const savedChat = simpleStorageService.saveChatSession(chatData.messages, chatData.sessionTitle);
  console.log('✅ 聊天历史保存成功:', savedChat.id);
  
  const retrievedChats = simpleStorageService.getChatHistory();
  console.log('✅ 聊天历史获取成功:', retrievedChats.length, '条记录');
  
  // 测试AI分析历史
  console.log('📝 测试AI分析历史...');
  const analysisData = {
    text: 'This is a test text',
    rawAnalysis: 'Raw analysis result',
    analysis: { grammar: [], vocabulary: [], suggestions: [] }
  };
  
  const savedAnalysis = simpleStorageService.saveAnalysis(
    analysisData.text,
    analysisData.rawAnalysis,
    analysisData.analysis
  );
  console.log('✅ AI分析历史保存成功:', savedAnalysis.id);
  
  const retrievedAnalyses = simpleStorageService.getAnalysisHistory();
  console.log('✅ AI分析历史获取成功:', retrievedAnalyses.length, '条记录');
  
  // 测试统计信息
  const stats = simpleStorageService.getStats();
  console.log('📊 存储统计:', stats);
  
  // 测试Firebase同步
  console.log('🔄 测试Firebase同步...');
  try {
    await simpleStorageService.manualSync();
    console.log('✅ Firebase同步测试完成');
  } catch (error) {
    console.log('⚠️ Firebase同步测试失败（可能是网络问题）:', error.message);
  }
  
  // 测试删除
  console.log('🗑️ 测试删除功能...');
  const deleteResult = simpleStorageService.deleteChatSession(savedChat.id);
  console.log('✅ 删除结果:', deleteResult);
  
  const finalStats = simpleStorageService.getStats();
  console.log('📊 最终统计:', finalStats);
  
  console.log('🎉 简化存储服务测试完成！');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testSimpleStorage = testSimpleStorage;
}
