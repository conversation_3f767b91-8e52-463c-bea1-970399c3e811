import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import App from './App';

// Mock all the components and services
vi.mock('./pages/EditorPage', () => ({
  default: () => <div data-testid="editor-page">EditorPage</div>
}));

vi.mock('./pages/ChatPage', () => ({
  default: () => <div data-testid="chat-page">ChatPage</div>
}));

vi.mock('./components/VintageApiConfigModal', () => ({
  default: () => <div data-testid="api-config-modal">VintageApiConfigModal</div>
}));

vi.mock('./components/DictionaryModal', () => ({
  default: () => <div data-testid="dictionary-modal">DictionaryModal</div>
}));

vi.mock('./demo/LoginDemo', () => ({
  default: () => <div data-testid="login-demo">LoginDemo</div>
}));

vi.mock('./services/auth/authService', () => ({
  onAuthChange: vi.fn(() => vi.fn()), // 返回 unsubscribe 函数
  logout: vi.fn()
}));

vi.mock('./services/history/initHistoryService', () => ({
  initHistoryService: vi.fn(),
  cleanupHistoryService: vi.fn()
}));

vi.mock('./services/storage/unifiedStorageService', () => ({
  default: {
    init: vi.fn()
  }
}));

vi.mock('./context/AppContext', () => ({
  AppProvider: ({ children }) => <div data-testid="app-provider">{children}</div>,
  useAppContext: vi.fn(() => ({
    state: {
      currentPage: 'editor',
      isDarkMode: false,
      showApiConfig: false,
      showDictionary: false,
      lookupWord: '',
      autoPlayTTS: true,
      aiResponse: null,
      suggestions: [],
      isAnalyzing: false,
      user: null
    },
    dispatch: vi.fn()
  }))
}));

// Mock utility files
vi.mock('./utils/consoleTests', () => ({}));
vi.mock('./utils/debugFirebaseService', () => ({}));
vi.mock('./utils/testFirebaseImport', () => ({}));
vi.mock('./utils/testDeleteFunction', () => ({}));
vi.mock('./utils/debugDeleteIssue', () => ({}));
vi.mock('./utils/clearCacheData', () => ({}));
vi.mock('./utils/clearFirebaseData', () => ({}));
vi.mock('./utils/fixDuplicateKeys', () => ({}));
vi.mock('./utils/testAllHistoryServices', () => ({}));

describe('App', () => {
  it('应该渲染AppProvider', () => {
    render(<App />);
    
    expect(screen.getByTestId('app-provider')).toBeInTheDocument();
  });

  it('应该渲染MainContent组件', () => {
    render(<App />);
    
    // MainContent 应该被渲染在 AppProvider 内部
    const appProvider = screen.getByTestId('app-provider');
    expect(appProvider).toBeInTheDocument();
  });
});
