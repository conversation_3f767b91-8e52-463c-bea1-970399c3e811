# 本地缓存同步功能集成指南

## 概述

本地缓存同步功能通过智能缓存策略和增量同步机制，显著提升应用的数据加载速度和用户体验。该功能支持离线优先、自动同步和智能缓存失效。

## 核心特性

### 🚀 性能优化
- **智能缓存**：基于时间戳的缓存失效机制
- **增量同步**：只同步变更的数据，减少网络请求
- **离线优先**：优先使用本地缓存，提升响应速度
- **后台同步**：自动在后台同步数据，不影响用户操作

### 🔄 同步策略
- **自动同步**：用户登录时自动同步数据
- **手动同步**：支持用户手动触发同步
- **网络感知**：根据网络状态调整同步策略
- **错误恢复**：网络错误时自动回退到本地存储

### 📊 状态监控
- **实时状态**：显示同步状态和网络连接状态
- **缓存统计**：显示缓存大小和同步时间
- **用户反馈**：提供清晰的同步进度反馈

## 快速开始

### 1. 基本使用

```javascript
import { useChatCache } from '../hooks/useLocalCache';

function ChatHistoryComponent() {
  const { getChatHistory, saveChatSession, isLoading, syncStatus } = useChatCache();
  const [history, setHistory] = useState([]);

  useEffect(() => {
    // 获取聊天历史（自动使用缓存）
    getChatHistory(50).then(setHistory);
  }, []);

  const handleSaveSession = async (sessionData) => {
    try {
      await saveChatSession(sessionData);
      // 保存成功后会自动更新缓存
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  return (
    <div>
      {isLoading && <div>加载中...</div>}
      {syncStatus === 'syncing' && <div>同步中...</div>}
      {/* 渲染聊天历史 */}
    </div>
  );
}
```

### 2. 添加状态指示器

```javascript
import CacheStatusIndicator from '../components/CacheStatusIndicator';

function Header() {
  return (
    <div className="header">
      <h1>English Assistant</h1>
      <CacheStatusIndicator isDarkMode={isDarkMode} />
    </div>
  );
}
```

### 3. 手动同步控制

```javascript
import { useLocalCache } from '../hooks/useLocalCache';

function SettingsPage() {
  const { triggerSync, clearAllCache, cacheStats } = useLocalCache();

  return (
    <div>
      <button onClick={triggerSync}>手动同步</button>
      <button onClick={clearAllCache}>清除缓存</button>
      <div>缓存大小: {cacheStats?.totalSize} bytes</div>
    </div>
  );
}
```

## 详细集成步骤

### 步骤1：替换现有的数据获取调用

**旧代码：**
```javascript
// 直接调用Firebase服务
import { hybridChatHistoryService } from '../services/history/hybridHistoryService';

const history = await hybridChatHistoryService.getChatHistory(50);
```

**新代码：**
```javascript
// 使用缓存服务
import { useChatCache } from '../hooks/useLocalCache';

const { getChatHistory } = useChatCache();
const history = await getChatHistory(50); // 自动使用缓存
```

### 步骤2：更新组件以使用缓存Hook

```javascript
// 聊天历史组件示例
import React, { useState, useEffect } from 'react';
import { useChatCache } from '../hooks/useLocalCache';

const ChatHistoryModal = ({ isOpen, onClose }) => {
  const { getChatHistory, isLoading, error } = useChatCache();
  const [history, setHistory] = useState([]);

  useEffect(() => {
    if (isOpen) {
      loadHistory();
    }
  }, [isOpen]);

  const loadHistory = async () => {
    try {
      const data = await getChatHistory(50);
      setHistory(data);
    } catch (err) {
      console.error('加载历史失败:', err);
    }
  };

  return (
    <div className={`modal ${isOpen ? 'open' : ''}`}>
      {isLoading && <div className="loading">加载中...</div>}
      {error && <div className="error">加载失败: {error}</div>}
      {/* 渲染历史列表 */}
    </div>
  );
};
```

### 步骤3：添加缓存状态指示器

```javascript
// 在主要页面添加状态指示器
import CacheStatusIndicator from '../components/CacheStatusIndicator';

const ChatPage = () => {
  return (
    <div className="chat-page">
      <header className="chat-header">
        <h1>English Chat</h1>
        <div className="header-actions">
          <CacheStatusIndicator isDarkMode={isDarkMode} />
          <button>设置</button>
        </div>
      </header>
      {/* 聊天内容 */}
    </div>
  );
};
```

### 步骤4：初始化缓存服务

```javascript
// 在应用启动时初始化
import localCacheService from '../services/cache/localCacheService';

const App = () => {
  useEffect(() => {
    // 启动后台同步
    localCacheService.startBackgroundSync();
    
    // 清理过期缓存
    localCacheService.cleanupExpiredCache();
    
    return () => {
      // 应用卸载时停止后台同步
      localCacheService.stopBackgroundSync();
    };
  }, []);

  return (
    <div className="app">
      {/* 应用内容 */}
    </div>
  );
};
```

## 高级用法

### 1. 自定义缓存策略

```javascript
import localCacheService, { CACHE_CONFIG } from '../services/cache/localCacheService';

// 修改缓存过期时间
CACHE_CONFIG.EXPIRY_TIMES.CHAT_HISTORY = 10 * 60 * 1000; // 10分钟

// 强制刷新特定数据
const freshData = await localCacheService.getChatHistory(50, true);
```

### 2. 监听同步状态

```javascript
import { useLocalCache } from '../hooks/useLocalCache';

const DataSyncComponent = () => {
  const { syncStatus, cacheStats } = useLocalCache();

  useEffect(() => {
    if (syncStatus === 'completed') {
      // 同步完成后的处理
      console.log('数据同步完成');
    }
  }, [syncStatus]);

  return (
    <div>
      <div>同步状态: {syncStatus}</div>
      <div>缓存条目: {cacheStats?.itemCount}</div>
    </div>
  );
};
```

### 3. 批量数据操作

```javascript
import { useLocalCache } from '../hooks/useLocalCache';

const BatchOperations = () => {
  const { getChatHistory, getAnalysisHistory, getWritingHistory } = useLocalCache();

  const loadAllData = async () => {
    // 并行加载所有数据
    const [chatHistory, analysisHistory, writingHistory] = await Promise.all([
      getChatHistory(50),
      getAnalysisHistory(50),
      getWritingHistory(50)
    ]);

    return { chatHistory, analysisHistory, writingHistory };
  };

  return (
    <button onClick={loadAllData}>
      加载所有数据
    </button>
  );
};
```

## 性能优化建议

### 1. 合理设置缓存过期时间

```javascript
// 根据数据更新频率调整过期时间
const CACHE_CONFIG = {
  EXPIRY_TIMES: {
    CHAT_HISTORY: 5 * 60 * 1000,      // 聊天历史更新频繁，短缓存
    ANALYSIS_HISTORY: 30 * 60 * 1000,  // 分析历史相对稳定，长缓存
    DICTIONARY_SEARCH: 60 * 60 * 1000, // 搜索历史很少变化，最长缓存
  }
};
```

### 2. 使用分页加载

```javascript
// 对于大量数据，使用分页加载
const loadChatHistory = async (page = 1, pageSize = 20) => {
  const offset = (page - 1) * pageSize;
  return await getChatHistory(pageSize, false);
};
```

### 3. 预加载重要数据

```javascript
// 在应用启动时预加载重要数据
useEffect(() => {
  const preloadData = async () => {
    await Promise.all([
      getChatHistory(20),      // 预加载最近20条聊天
      getAnalysisHistory(10),  // 预加载最近10条分析
    ]);
  };
  
  preloadData();
}, []);
```

## 错误处理

### 1. 网络错误处理

```javascript
const { getChatHistory, error } = useChatCache();

useEffect(() => {
  const loadData = async () => {
    try {
      const data = await getChatHistory(50);
      setHistory(data);
    } catch (err) {
      if (err.message.includes('网络')) {
        // 网络错误，显示离线提示
        setError('网络连接失败，显示缓存数据');
      } else {
        // 其他错误
        setError('加载失败，请重试');
      }
    }
  };
  
  loadData();
}, []);
```

### 2. 缓存清理

```javascript
// 定期清理过期缓存
useEffect(() => {
  const cleanupInterval = setInterval(() => {
    localCacheService.cleanupExpiredCache();
  }, 5 * 60 * 1000); // 每5分钟清理一次

  return () => clearInterval(cleanupInterval);
}, []);
```

## 测试和调试

### 1. 启用调试模式

```javascript
// 在控制台启用详细日志
localStorage.setItem('debug_cache', 'true');

// 查看缓存统计
console.log('缓存统计:', localCacheService.getCacheStats());
```

### 2. 测试离线功能

```javascript
// 模拟离线状态
window.dispatchEvent(new Event('offline'));

// 测试缓存功能
const cachedData = await getChatHistory(50);
console.log('离线数据:', cachedData);
```

### 3. 性能监控

```javascript
// 监控加载时间
const startTime = performance.now();
const data = await getChatHistory(50);
const endTime = performance.now();
console.log(`加载时间: ${endTime - startTime}ms`);
```

## 注意事项

1. **数据一致性**：缓存数据可能与服务器数据不同步，重要操作建议强制刷新
2. **存储限制**：localStorage有大小限制，注意控制缓存数据量
3. **隐私安全**：敏感数据不应存储在localStorage中
4. **浏览器兼容性**：确保目标浏览器支持localStorage API

## 故障排除

### 常见问题

1. **缓存不生效**
   - 检查缓存键名是否正确
   - 确认缓存过期时间设置
   - 验证数据格式是否正确

2. **同步失败**
   - 检查网络连接状态
   - 验证Firebase配置
   - 查看控制台错误信息

3. **性能问题**
   - 调整缓存过期时间
   - 减少缓存数据量
   - 优化数据加载策略

### 调试技巧

```javascript
// 查看所有缓存数据
Object.keys(localStorage).forEach(key => {
  if (key.startsWith('cache_')) {
    console.log(key, localStorage.getItem(key));
  }
});

// 清除所有缓存
localCacheService.clearAllCache();
```
