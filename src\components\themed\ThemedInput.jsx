import React from 'react';

const ThemedInput = ({
  className = '',
  style = {},
  size = 'md',
  isDarkMode = false,
  ...props
}) => {

  // 基础样式
  const baseStyles = {
    backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
    color: 'var(--color-text-primary)',
    border: `2px solid ${isDarkMode ? '#4A3F35' : '#E5E7EB'}`,
    borderRadius: '12px',
    fontFamily: 'Georgia, "Noto Serif SC", serif',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    outline: 'none',
    width: '100%',
    boxSizing: 'border-box',
  };

  // 尺寸样式
  const sizeStyles = {
    sm: {
      padding: '12px 16px',
      fontSize: '14px',
    },
    md: {
      padding: '16px 20px',
      fontSize: '16px',
    },
    lg: {
      padding: '20px 24px',
      fontSize: '18px',
    }
  };

  // 合并样式
  const finalStyles = {
    ...baseStyles,
    ...sizeStyles[size],
    ...style,
  };

  // 焦点事件处理
  const handleFocus = (e) => {
    e.target.style.backgroundColor = isDarkMode ? '#3A2F26' : '#FFFFFF';
    e.target.style.borderColor = isDarkMode ? '#D2691E' : '#B91C1C';
    e.target.style.boxShadow = isDarkMode
      ? '0 0 0 3px rgba(210, 105, 30, 0.15)'
      : '0 0 0 3px rgba(185, 28, 28, 0.15)';
    if (props.onFocus) props.onFocus(e);
  };

  const handleBlur = (e) => {
    e.target.style.backgroundColor = isDarkMode ? '#332B22' : '#FFFEF7';
    e.target.style.borderColor = isDarkMode ? '#4A3F35' : '#E5E7EB';
    e.target.style.boxShadow = 'none';
    if (props.onBlur) props.onBlur(e);
  };

  const handleMouseEnter = (e) => {
    if (e.target !== document.activeElement) {
      e.target.style.borderColor = isDarkMode ? '#5A4F45' : '#D1D5DB';
      e.target.style.backgroundColor = isDarkMode ? '#3A2F26' : '#FFFFFF';
    }
  };

  const handleMouseLeave = (e) => {
    if (e.target !== document.activeElement) {
      e.target.style.borderColor = isDarkMode ? '#4A3F35' : '#E5E7EB';
      e.target.style.backgroundColor = isDarkMode ? '#332B22' : '#FFFEF7';
    }
  };

  return (
    <input
      className={`themed-input ${className}`}
      style={{
        ...finalStyles,
        '::placeholder': {
          color: isDarkMode ? '#8B7D6B' : '#9CA3AF',
          opacity: 1
        }
      }}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    />
  );
};

export default ThemedInput;