/**
 * 控制台测试工具
 * 提供简单的测试功能，无需复杂的导入
 */

import { generateUniqueId, generateSimpleId, generateTimestampId, generateIncrementId } from './idGenerator';

/**
 * 测试ID生成器的唯一性
 */
const testIdGenerator = () => {
  console.log('🧪 开始测试ID生成器...');
  
  try {
    const ids = new Set();
    const count = 100;
    
    for (let i = 0; i < count; i++) {
      const id = generateUniqueId();
      if (ids.has(id)) {
        console.log('❌ 发现重复ID:', id);
        return false;
      }
      ids.add(id);
    }
    
    console.log('✅ ID生成器测试通过，生成了', count, '个唯一ID');
    return true;
  } catch (error) {
    console.error('❌ ID生成器测试失败:', error);
    return false;
  }
};

/**
 * 测试缓存功能
 */
const testCache = () => {
  console.log('🧪 开始测试缓存功能...');
  
  try {
    const testData = { message: 'Hello World', timestamp: Date.now() };
    const cacheKey = 'test_cache_' + Date.now();
    
    // 设置缓存
    localStorage.setItem(cacheKey, JSON.stringify(testData));
    
    // 获取缓存
    const retrieved = JSON.parse(localStorage.getItem(cacheKey) || '{}');
    
    if (JSON.stringify(retrieved) === JSON.stringify(testData)) {
      console.log('✅ 缓存功能测试通过');
      localStorage.removeItem(cacheKey);
      return true;
    } else {
      console.log('❌ 缓存功能测试失败');
      localStorage.removeItem(cacheKey);
      return false;
    }
  } catch (error) {
    console.error('❌ 缓存功能测试失败:', error);
    return false;
  }
};

/**
 * 测试重复键问题
 */
const testDuplicateKeys = () => {
  console.log('🧪 开始测试重复键问题...');
  
  try {
    const records = [];
    const count = 50;
    
    // 生成多个记录
    for (let i = 0; i < count; i++) {
      records.push({
        id: generateUniqueId(),
        text: `测试记录 ${i}`,
        timestamp: new Date().toISOString()
      });
    }
    
    // 检查是否有重复ID
    const ids = records.map(r => r.id);
    const uniqueIds = new Set(ids);
    
    if (ids.length === uniqueIds.size) {
      console.log('✅ 重复键测试通过，所有记录都有唯一ID');
      return true;
    } else {
      console.log('❌ 发现重复ID，记录数:', ids.length, '唯一ID数:', uniqueIds.size);
      return false;
    }
  } catch (error) {
    console.error('❌ 重复键测试失败:', error);
    return false;
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('🚀 开始运行所有测试...');
  console.log('='.repeat(50));
  
  const tests = [
    { name: 'ID生成器', test: testIdGenerator },
    { name: '缓存功能', test: testCache },
    { name: '重复键检查', test: testDuplicateKeys }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    console.log(`\n📋 运行测试: ${name}`);
    try {
      const result = test();
      if (result) {
        passedTests++;
        console.log(`✅ ${name} 测试通过`);
      } else {
        console.log(`❌ ${name} 测试失败`);
      }
    } catch (error) {
      console.error(`❌ ${name} 测试出错:`, error);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
  
  return passedTests === totalTests;
};

/**
 * 检查当前存储状态
 */
const checkStorageStatus = () => {
  console.log('🔍 检查当前存储状态...');
  
  try {
    const storageKeys = Object.keys(localStorage);
    const cacheKeys = storageKeys.filter(key => key.startsWith('cache_'));
    
    console.log('📊 存储统计:');
    console.log(`  总localStorage条目: ${storageKeys.length}`);
    console.log(`  缓存条目: ${cacheKeys.length}`);
    
    if (cacheKeys.length > 0) {
      console.log('📋 缓存条目列表:');
      cacheKeys.forEach(key => {
        const data = localStorage.getItem(key);
        const size = data ? data.length : 0;
        console.log(`  - ${key}: ${size} 字符`);
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ 检查存储状态失败:', error);
    return false;
  }
};

/**
 * 清除所有缓存
 */
const clearAllCache = () => {
  console.log('🧹 清除所有缓存...');
  
  try {
    const storageKeys = Object.keys(localStorage);
    const cacheKeys = storageKeys.filter(key => key.startsWith('cache_'));
    
    cacheKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`✅ 已清除: ${key}`);
    });
    
    console.log(`🎉 已清除 ${cacheKeys.length} 个缓存条目`);
    return true;
  } catch (error) {
    console.error('❌ 清除缓存失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.consoleTests = {
    testIdGenerator,
    testCache,
    testDuplicateKeys,
    runAllTests,
    checkStorageStatus,
    clearAllCache
  };
  
  console.log('🔧 控制台测试工具已加载到 window.consoleTests');
  console.log('💡 使用方法:');
  console.log('  - await window.consoleTests.runAllTests()');
  console.log('  - window.consoleTests.checkStorageStatus()');
  console.log('  - window.consoleTests.clearAllCache()');
}
