/**
 * 测试Firebase同步功能
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试Firebase同步
export const testFirebaseSync = async () => {
  console.log('🧪 开始测试Firebase同步功能...');
  
  // 模拟用户登录
  const testUserId = 'test_user_' + Date.now();
  simpleStorageService.init(testUserId);
  
  console.log('👤 测试用户ID:', testUserId);
  
  // 测试保存聊天数据
  console.log('💬 测试保存聊天数据...');
  const testChat = {
    id: generateUniqueId(),
    messages: [
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: 'Hi there!' }
    ],
    sessionTitle: 'Test Chat',
    timestamp: new Date().toISOString(),
    userId: testUserId
  };
  
  const savedChat = simpleStorageService.saveChatSession(testChat);
  console.log('✅ 聊天数据保存成功:', savedChat.id);
  
  // 测试保存分析数据
  console.log('📝 测试保存分析数据...');
  const testAnalysis = {
    id: generateUniqueId(),
    text: 'This is a test text for analysis',
    rawAnalysis: 'Test analysis result',
    analysis: {
      suggestions: ['suggestion1', 'suggestion2'],
      score: 85
    },
    timestamp: new Date().toISOString(),
    userId: testUserId
  };
  
  const savedAnalysis = simpleStorageService.saveAnalysisHistory(testAnalysis);
  console.log('✅ 分析数据保存成功:', savedAnalysis.id);
  
  // 测试手动同步
  console.log('🔄 测试手动同步到Firebase...');
  try {
    await simpleStorageService.manualSync();
    console.log('✅ Firebase同步测试完成');
  } catch (error) {
    console.log('⚠️ Firebase同步测试失败（可能是网络问题）:', error.message);
  }
  
  // 测试从Firebase同步
  console.log('🔄 测试从Firebase同步数据...');
  try {
    const syncedChat = await simpleStorageService.syncFromFirebase('CHAT_HISTORY');
    console.log('✅ 从Firebase同步聊天数据:', syncedChat.length, '条记录');
    
    const syncedAnalysis = await simpleStorageService.syncFromFirebase('ANALYSIS_HISTORY');
    console.log('✅ 从Firebase同步分析数据:', syncedAnalysis.length, '条记录');
  } catch (error) {
    console.log('⚠️ 从Firebase同步失败（可能是网络问题）:', error.message);
  }
  
  // 显示统计信息
  const stats = simpleStorageService.getStats();
  console.log('📊 最终统计:', stats);
  
  console.log('🎉 Firebase同步功能测试完成！');
};

// 生成唯一ID的简单函数
function generateUniqueId() {
  return 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testFirebaseSync = testFirebaseSync;
}
