import React, { useState, useEffect } from 'react';
import { db } from '../config/firebaseConfig';
import { collection, getDocs, query, orderBy, limit, getDocsFromCache, getDocsFromServer } from 'firebase/firestore';

const RecentImagesGrid = ({ isDarkMode, onImageClick }) => {
  const [recentImages, setRecentImages] = useState([]);

  // Firebase缓存优先获取图像函数
  const getImagesWithCacheFirst = async (limitCount = 20) => {
    try {
      const diariesRef = collection(db, 'diaries');
      const q = query(diariesRef, orderBy('timestamp', 'desc'), limit(limitCount));
      
      // 首先尝试从缓存获取
      try {
        const cacheSnapshot = await getDocsFromCache(q);
        if (!cacheSnapshot.empty) {
          const cachedDiaries = cacheSnapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              date: data.date || '',
              english: data.english || '',
              chinese: data.chinese || '',
              mood: data.mood || 'neutral',
              weather: data.weather || 'unknown',
              imageUrl: data.imageUrl || null,
              imageStatus: data.imageStatus || 'pending',
              imageGeneratedAt: data.imageGeneratedAt || null,
              timestamp: data.timestamp?.toDate?.() || new Date(data.timestamp),
              type: data.type || 'auto_generated',
              createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt)
            };
          });
          
          const imagesWithUrl = cachedDiaries
            .filter(diary => diary.imageUrl && diary.imageStatus === 'completed')
            .map(diary => ({
              diaryId: diary.id,
              url: diary.imageUrl,
              timestamp: diary.imageGeneratedAt?.toDate?.() || new Date(diary.timestamp)
            }))
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, 6);
          
          console.log('⚡ 从Firebase缓存快速加载图像:', imagesWithUrl.length);
          return { images: imagesWithUrl, fromCache: true };
        }
      } catch (cacheError) {
        console.log('📱 图像缓存为空，从服务器获取...');
      }
      
      // 如果缓存为空，从服务器获取
      const serverSnapshot = await getDocsFromServer(q);
      const serverDiaries = serverSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          date: data.date || '',
          english: data.english || '',
          chinese: data.chinese || '',
          mood: data.mood || 'neutral',
          weather: data.weather || 'unknown',
          imageUrl: data.imageUrl || null,
          imageStatus: data.imageStatus || 'pending',
          imageGeneratedAt: data.imageGeneratedAt || null,
          timestamp: data.timestamp?.toDate?.() || new Date(data.timestamp),
          type: data.type || 'auto_generated',
          createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt)
        };
      });
      
      const imagesWithUrl = serverDiaries
        .filter(diary => diary.imageUrl && diary.imageStatus === 'completed')
        .map(diary => ({
          diaryId: diary.id,
          url: diary.imageUrl,
          timestamp: diary.imageGeneratedAt?.toDate?.() || new Date(diary.timestamp)
        }))
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 6);
      
      console.log('🌐 从Firebase服务器获取图像:', imagesWithUrl.length);
      return { images: imagesWithUrl, fromCache: false };
      
    } catch (error) {
      console.error('Firebase图像获取失败:', error);
      throw error;
    }
  };

  const loadImages = async () => {
    try {
      // 优先从localStorage快速加载
      const savedImages = localStorage.getItem('generated_images');
      if (savedImages) {
        const imagesData = JSON.parse(savedImages);
        const imageArray = Object.entries(imagesData)
          .map(([diaryId, data]) => {
            let timestamp = data.timestamp || Date.now();
            if (typeof timestamp === 'string') {
              timestamp = new Date(timestamp).getTime();
            }
            return {
              diaryId,
              url: data.url,
              timestamp
            };
          })
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, 6);

        if (imageArray.length > 0) {
          setRecentImages(imageArray);
          console.log('⚡ 从localStorage快速加载图像:', imageArray.length);
          
          // 在后台尝试从Firebase缓存获取更新数据
          setTimeout(async () => {
            try {
              const { images: firebaseImages, fromCache } = await getImagesWithCacheFirst(20);
              if (firebaseImages.length > 0) {
                setRecentImages(firebaseImages);
                console.log('✅ Firebase后台同步图像完成:', firebaseImages.length, fromCache ? '(来自缓存)' : '(来自服务器)');
              }
            } catch (error) {
              console.warn('Firebase后台同步图像失败，继续使用本地数据');
            }
          }, 2000);
          return;
        }
      }
      
      // 如果没有本地图像，使用Firebase缓存优先策略
      try {
        const { images: firebaseImages, fromCache } = await getImagesWithCacheFirst(20);
        if (firebaseImages.length > 0) {
          setRecentImages(firebaseImages);
          console.log('✅ Firebase图像加载完成:', firebaseImages.length, fromCache ? '(来自缓存)' : '(来自服务器)');
        }
      } catch (firebaseError) {
        console.warn('Firebase图像获取失败');
      }
      
    } catch (error) {
      console.error('加载图像失败:', error);
      setRecentImages([]);
    }
  };

  useEffect(() => {
    loadImages();

    // 监听localStorage变化
    const handleStorageChange = (e) => {
      if (e.key === 'generated_images') {
        loadImages();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // 定期刷新（防止同页面内的更新不被监听到）
    const interval = setInterval(loadImages, 2000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  if (recentImages.length === 0) {
    return (
      <div
        className="text-center py-12 px-4"
        style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
      >
        <div
          className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
          style={{
            backgroundColor: isDarkMode ? '#2D2520' : '#FAF7F0',
            border: `2px dashed ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
          }}
        >
          <div className="text-2xl">🌿</div>
        </div>
        <div className="text-sm opacity-75 mb-1">还没有AI生成的图像</div>
        <div className="text-xs opacity-50">生成日记时会自动创建精美图像</div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-3 gap-3">
      {recentImages.map((image, index) => (
        <div
          key={image.diaryId}
          className="group relative aspect-square rounded-xl overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
          style={{
            backgroundColor: isDarkMode ? '#2D2520' : '#FAF7F0',
            boxShadow: isDarkMode
              ? '0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.2)'
              : '0 2px 8px rgba(139, 69, 19, 0.1), 0 1px 3px rgba(139, 69, 19, 0.05)',
            border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
          }}
          onClick={() => onImageClick(image.diaryId)}
        >
          <img
            src={image.url}
            alt={`AI生成图像 ${index + 1}`}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />

          {/* 悬停时的遮罩层 */}
          <div
            className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          />

          {/* 悬停时的提示文字 */}
          <div
            className="absolute bottom-2 left-2 right-2 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center"
            style={{
              textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',
              fontFamily: 'Georgia, serif'
            }}
          >
            View Journal
          </div>

          {/* 图片序号标识 */}
          {index === 0 && (
            <div
              className="absolute top-2 right-2 px-1.5 py-0.5 rounded-full flex items-center justify-center text-xs font-medium"
              style={{
                backgroundColor: isDarkMode ? '#D2691E' : '#166534',
                color: '#FEFCF5',
                fontSize: '9px',
                fontFamily: 'Arial, sans-serif',
                fontWeight: 'bold'
              }}
            >
              NEW
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default RecentImagesGrid;
