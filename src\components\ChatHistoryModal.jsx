import React, { useState, useEffect } from 'react';
import { X, MessageCircle, Trash2, Calendar, Hash } from 'lucide-react';
import simpleStorageService from '../services/storage/simpleStorageService';
import ConfirmDialog from './ConfirmDialog';
import { useConfirmDialog } from '../hooks/useConfirmDialog';

const ChatHistoryModal = ({ isOpen, onClose, onSelectSession, isDarkMode }) => {
  const [history, setHistory] = useState([]);
  const [selectedSession, setSelectedSession] = useState(null);
  const { dialogState, showConfirm, hideConfirm } = useConfirmDialog();

  useEffect(() => {
    if (isOpen) {
      loadHistory();
    }
  }, [isOpen]);

  const loadHistory = () => {
    const chatHistory = simpleStorageService.getChatHistory();
    setHistory(chatHistory);
  };

  const handleDeleteSession = (sessionId, e) => {
    e.stopPropagation(); // 防止触发选择事件
    showConfirm({
      title: '删除聊天记录',
      message: '确定要删除这个聊天记录吗？',
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: () => {
        simpleStorageService.deleteChatSession(sessionId);
        loadHistory();
      }
    });
  };

  const handleClearAll = () => {
    showConfirm({
      title: '清空聊天记录',
      message: '确定要清空所有聊天记录吗？此操作不可恢复。',
      confirmText: '清空',
      cancelText: '取消',
      type: 'danger',
      onConfirm: () => {
        simpleStorageService.clearChatHistory();
        loadHistory();
      }
    });
  };

  const handleSelectSession = (session) => {
    onSelectSession(session);
    onClose();
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return '今天';
    } else if (diffDays === 2) {
      return '昨天';
    } else if (diffDays <= 7) {
      return `${diffDays - 1}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.6)' : 'rgba(93, 64, 55, 0.4)' }}
      onClick={handleBackdropClick}
    >
      <div 
        className="rounded-2xl max-w-2xl w-full mx-8 transition-colors duration-300"
        style={{
          backgroundColor: isDarkMode ? '#332B22' : '#FEFCF5',
          maxHeight: '80vh',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between" style={{
          padding: '24px 24px 16px 24px',
          borderBottom: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
        }}>
          <div className="flex items-center gap-3">
            <MessageCircle 
              className="w-6 h-6" 
              style={{ color: isDarkMode ? '#D2691E' : '#166534' }} 
            />
            <h3 className="text-xl font-semibold transition-colors duration-300" style={{
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.05em'
            }}>
              聊天历史记录
            </h3>
            {history.length > 0 && (
              <button
                onClick={handleClearAll}
                className="delete-btn ml-2"
                title="清空所有记录"
              >
                <Trash2 className="w-5 h-5" />
              </button>
            )}
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onClose}
              className="header-btn"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div 
          className="custom-scrollbar"
          style={{ 
            flex: '1',
            minHeight: 0,
            overflowY: 'auto',
            padding: '16px 24px 24px 24px'
          }}
        >
          {history.length === 0 ? (
            <div className="text-center py-12">
              <MessageCircle 
                className="w-16 h-16 mx-auto mb-4 opacity-50" 
                style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}
              />
              <p style={{
                color: isDarkMode ? '#C4B59A' : '#8B4513',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                fontSize: '16px'
              }}>
                还没有聊天记录
              </p>
              <p style={{
                color: isDarkMode ? '#A0937D' : '#A0937D',
                fontFamily: 'Georgia, "Noto Serif SC", serif',
                fontSize: '14px',
                marginTop: '8px'
              }}>
                开始与AI聊天后，记录会自动保存在这里
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {history.map((session) => (
                <div
                  key={session.id}
                  onClick={() => handleSelectSession(session)}
                  className="history-item group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 style={{
                        color: isDarkMode ? '#E8DCC6' : '#5D4037',
                        fontFamily: 'Georgia, "Noto Serif SC", serif',
                        fontSize: '16px',
                        fontWeight: '500',
                        marginBottom: '8px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {session.title || session.sessionTitle || '未命名对话'}
                      </h4>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }} />
                          <span style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}>
                            {formatDate(session.timestamp)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Hash className="w-4 h-4" style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }} />
                          <span style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}>
                            {session.messageCount || (session.messages ? session.messages.length : 0)} 条消息
                          </span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={(e) => handleDeleteSession(session.id, e)}
                      className="delete-btn ml-3 opacity-0 group-hover:opacity-100"
                      title="删除此记录"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 自定义确认对话框 */}
      <ConfirmDialog
        isOpen={dialogState.isOpen}
        onClose={hideConfirm}
        onConfirm={dialogState.onConfirm}
        title={dialogState.title}
        message={dialogState.message}
        confirmText={dialogState.confirmText}
        cancelText={dialogState.cancelText}
        type={dialogState.type}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

export default ChatHistoryModal;