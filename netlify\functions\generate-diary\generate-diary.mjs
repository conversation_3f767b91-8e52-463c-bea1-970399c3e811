// AI Diary Generator Function
import axios from 'axios';
import admin from 'firebase-admin';

export default async (request, context) => {
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type'
  };

  try {
    const now = new Date();
    const dateString = now.toISOString().split('T')[0];
    
    console.log('开始生成日记，日期:', dateString);
    
    // 1. 生成AI日记内容
    const diaryContent = await generateAIDiary(now);
    console.log('日记内容生成完成');
    
    // 2. 保存日记到Firebase（不包含图像）
    const savedDiary = await saveDiaryToFirebase(diaryContent);
    console.log('日记已保存到Firebase，文档ID:', savedDiary.id);
    
    // 3. 异步启动AI绘画生成（不阻塞主流程）
    generateAIImage(diaryContent.english).then(async (imageUrl) => {
      if (imageUrl) {
        console.log('AI绘画生成成功:', imageUrl);
        // 图像生成完成后，更新Firebase中的图像URL
        try {
          // 使用正确的文档ID（Firebase自动生成的ID）
          const correctDocId = savedDiary.id;
          console.log('准备更新图像URL，文档ID:', correctDocId);
          await updateDiaryImage(correctDocId, imageUrl);
          console.log('图像URL已更新到Firebase');
        } catch (updateError) {
          console.error('更新图像URL失败:', updateError);
        }
      }
    }).catch(async (imageError) => {
      console.error('AI绘画生成失败:', imageError);
      // 即使图像生成失败，也不影响日记功能
    });
    
    // 4. 立即返回响应（不等待图像生成）
    return new Response(JSON.stringify({
      success: true,
      message: '日记生成成功，AI绘画正在后台生成中...',
      date: dateString,
      content: diaryContent,
      diaryId: savedDiary.id,
      hasImage: false,  // 暂时标记为没有图像
      imageStatus: 'generating'  // 图像状态：生成中
    }), {
      status: 200,
      headers
    });
    
  } catch (error) {
    console.error('生成日记失败:', error);
    return new Response(JSON.stringify({
      error: '生成日记失败',
      details: error.message
    }), {
      status: 500,
      headers
    });
  }
};

async function generateAIDiary(date) {
  try {
    // 使用豆包1.6模型的提示词
    const systemPrompt = `You are Alex, a botanist and nature photographer researching in Costa Rica's cloud forest. Write a 150-250 word English diary entry describing your discoveries and feelings.

IMPORTANT REQUIREMENTS:
- Start directly describing today's experiences - do NOT include date titles or time markers
- Content should be vivid and interesting, demonstrating professional botanical knowledge
- Use first person perspective, as if writing a personal diary
- Include specific plant species discovered, observation details, personal feelings, etc.
- Language should be natural and fluent, reflecting love for nature and professional expertise
- Always include relevant emojis related to plants 🌿, animals 🐦, or nature photography 📸

OUTPUT FORMAT - CRITICAL:
Your output MUST contain ONLY 3 elements in this exact order:
1. YOUR ACTUAL ENGLISH DIARY CONTENT (what you want to write in the diary)
2. EXACTLY "---" ON A SEPARATE LINE (just those three characters, nothing else)
3. YOUR ACTUAL CHINESE TRANSLATION (faithful translation of your English diary)

ENFORCEMENT RULES:
- English section: ONLY pure English diary content, NO Chinese text
- Separator: ONLY "---" on a separate line, nothing else
- Chinese section: ONLY the translation of the English diary
- NEVER include "English content here" or similar labels
- NEVER mix languages within sections`;

    const userPrompt = `请写一篇关于今天在云雾森林中的发现和体验的日记内容。记住：直接开始描述经历，不要写日期。`;

    const response = await axios.post('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
      model: 'doubao-seed-1-6-flash-250615',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 2.0,
      max_tokens: 800,
      thinking: { type: "disabled" }
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.DOUBAO_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const content = response.data.choices[0].message.content;
    console.log('豆包AI生成的日记内容:', content);
    
    // 解析豆包返回的内容（包含英文和中文，用---分隔）
    if (content.includes('---')) {
      const parts = content.split('---');
      if (parts.length >= 2) {
        const english = parts[0].trim();
        const chinese = parts[1].trim();
        
        // 根据内容推断心情和天气
        const mood = inferMood(english);
        const weather = inferWeather(english);
        
        return {
          english: english,
          chinese: chinese,
          mood: mood,
          weather: weather
        };
      }
    }
    
    // 如果解析失败，返回默认内容
    return {
      english: content || "Today was wonderful...",
      chinese: "今天很美好...",
      mood: "happy",
      weather: "sunny"
    };
    
  } catch (error) {
    console.error('豆包AI调用失败:', error);
    return {
      english: "Today I reflected on life...",
      chinese: "今天我思考了人生...",
      mood: "thoughtful",
      weather: "partly_cloudy"
    };
  }
}

// 生成AI绘画
async function generateAIImage(diaryContent) {
  try {
    console.log('开始生成AI绘画...');
    
    // 从日记中提取核心主题，并生成复古科学插图风格的提示词
    const getScientificIllustrationPrompt = (diaryText) => {
      const text = diaryText.toLowerCase();
      // 定义可能的动植物关键词
      const keywords = [
        'orchid', 'heliconia', 'bromeliad', 'fern', 'moss', 'vine', 'flower', 'leaf', 'tree', 'fungi', 'mushroom',
        'hummingbird', 'bird', 'butterfly', 'bee', 'insect', 'frog', 'lizard', 'monkey', 'sloth', 'toucan', 'parrot', 'spider', 'ant', 'beetle', 'moth', 'dragonfly', 'cricket', 'grasshopper', 'caterpillar', 'snake', 'gecko'
      ];

      let mainSubject = 'a fascinating jungle species'; // 默认主题
      let subjectLabel = 'Flora & Fauna'; // 默认标签

      // 查找第一个匹配的关键词作为主要主题
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          mainSubject = keyword;
          subjectLabel = keyword.charAt(0).toUpperCase() + keyword.slice(1);
          break;
        }
      }

      // 套用复古科学插图模板
      const prompt = `A vintage scientific illustration of ${mainSubject}, in a sepia tone, with detailed linework and shading, labeled "${subjectLabel}" and "Costa Rica", resembling an antique geology textbook plate, elegant minimalistic layout, 19th-century lithograph engraving style.`;

      return prompt;
    };

    const prompt = getScientificIllustrationPrompt(diaryContent);
    console.log('AI绘画提示词:', prompt);

    const response = await axios.post('https://api.302.ai/v1/chat/completions', {
      model: 'gpt-4o-image-generation',
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt
            }
          ]
        }
      ],
      temperature: 2.0,
      stream: false
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.IMAGE_GENERATION_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.choices && response.data.choices[0] && response.data.choices[0].message && response.data.choices[0].message.content) {
      const content = response.data.choices[0].message.content;
      console.log('图像生成API响应成功');

      // 从302.ai的markdown格式响应中提取图像URL
      const markdownMatches = content.match(/!\[.*?\]\((https:\/\/file\.302ai\.cn\/gpt\/imgs\/[^)]+)\)/g);

      if (markdownMatches && markdownMatches.length > 0) {
        // 提取第一个图像URL
        const urlMatch = markdownMatches[0].match(/\((https:\/\/file\.302ai\.cn\/gpt\/imgs\/[^)]+)\)/);
        if (urlMatch) {
          let imageUrl = urlMatch[1];

          // 清理URL，只保留到.png为止
          const pngIndex = imageUrl.indexOf('.png');
          if (pngIndex !== -1) {
            imageUrl = imageUrl.substring(0, pngIndex + 4);
          }

          console.log('成功生成AI绘画:', imageUrl);
          return imageUrl;
        }
      }

      // 如果markdown匹配失败，尝试直接匹配URL
      const directMatch = content.match(/https:\/\/file\.302ai\.cn\/gpt\/imgs\/[^\s)&]+\.png/);
      if (directMatch) {
        console.log('通过直接匹配找到图像URL:', directMatch[0]);
        return directMatch[0];
      }
    }

    console.error('无法从API响应中提取图像URL');
    return null;

  } catch (error) {
    console.error('AI绘画生成失败:', error);
    return null;
  }
}

// 根据英文内容推断心情
function inferMood(englishText) {
  const text = englishText.toLowerCase();
  if (text.includes('amazing') || text.includes('wonderful') || text.includes('excited')) return 'excited';
  if (text.includes('peaceful') || text.includes('calm') || text.includes('serene')) return 'peaceful';
  if (text.includes('curious') || text.includes('discovered') || text.includes('learned')) return 'curious';
  if (text.includes('challenging') || text.includes('difficult') || text.includes('struggled')) return 'challenged';
  return 'thoughtful';
}

// 根据英文内容推断天气
function inferWeather(englishText) {
  const text = englishText.toLowerCase();
  if (text.includes('mist') || text.includes('fog') || text.includes('cloudy')) return 'misty';
  if (text.includes('sunny') || text.includes('bright') || text.includes('warm')) return 'sunny';
  if (text.includes('rain') || text.includes('wet') || text.includes('drizzle')) return 'rainy';
  if (text.includes('wind') || text.includes('breeze')) return 'windy';
  return 'partly_cloudy';
}

// 更新日记中的图像URL
async function updateDiaryImage(diaryId, imageUrl) {
  try {
    const db = admin.firestore();
    await db.collection('diaries').doc(diaryId).update({
      imageUrl: imageUrl,
      imageGeneratedAt: admin.firestore.Timestamp.fromDate(new Date()),
      imageStatus: 'completed'
    });
    console.log('图像URL更新成功:', diaryId, imageUrl);
  } catch (error) {
    console.error('更新图像URL失败:', error);
    throw error;
  }
}

async function saveDiaryToFirebase(diaryContent) {
  try {
    // 调试信息
    console.log('Firebase配置信息:');
    console.log('Project ID:', process.env.FIREBASE_PROJECT_ID);
    console.log('Client Email:', process.env.FIREBASE_CLIENT_EMAIL);
    console.log('Private Key length:', process.env.FIREBASE_PRIVATE_KEY?.length);
    console.log('Private Key starts with:', process.env.FIREBASE_PRIVATE_KEY?.substring(0, 50));
    
    // 初始化 Firebase Admin SDK
    if (!admin.apps.length) {
      try {
        // 从环境变量中获取私钥
        let privateKey = process.env.FIREBASE_PRIVATE_KEY;
        
        console.log('原始私钥长度:', privateKey.length);
        console.log('原始私钥开始:', privateKey.substring(0, 50));
        
        // 检查是否是 Base64 编码的私钥
        if (privateKey.startsWith('LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t')) {
          console.log('检测到 Base64 编码的私钥，进行解码...');
          // 解码 Base64 私钥
          privateKey = Buffer.from(privateKey, 'base64').toString('ascii');
          console.log('Base64 解码后的私钥长度:', privateKey.length);
          console.log('Base64 解码后的私钥开始:', privateKey.substring(0, 50));
        }
        
        // 处理私钥格式 - 确保换行符正确
        if (privateKey.includes('\\n')) {
          // 如果私钥包含转义的换行符，替换为真实的换行符
          privateKey = privateKey.replace(/\\n/g, '\n');
        }
        
        console.log('处理后的私钥长度:', privateKey.length);
        console.log('处理后的私钥开始:', privateKey.substring(0, 100));
        console.log('处理后的私钥结束:', privateKey.substring(privateKey.length - 100));
        
        admin.initializeApp({
          credential: admin.credential.cert({
            projectId: process.env.FIREBASE_PROJECT_ID,
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
            privateKey: privateKey
          }),
          databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com`
        });
        
        console.log('Firebase Admin SDK 初始化成功');
      } catch (error) {
        console.error('Firebase Admin SDK 初始化失败:', error);
        throw error;
      }
    }

    const now = new Date();
    const dateString = now.toISOString().split('T')[0];
    
    // 准备保存的数据 - 直接保存各个字段，便于前端读取
    const diaryData = {
      date: dateString,
      timestamp: admin.firestore.Timestamp.fromDate(now),
      english: diaryContent.english,
      chinese: diaryContent.chinese,
      mood: diaryContent.mood,
      weather: diaryContent.weather,
      createdAt: admin.firestore.Timestamp.fromDate(now),
      type: 'auto_generated',
      // 如果生成了图像，保存图像URL
      ...(diaryContent.imageUrl && { imageUrl: diaryContent.imageUrl })
    };
    
    // 保存到 Firestore
    const db = admin.firestore();
    const docRef = await db.collection('diaries').add(diaryData);
    
    console.log('日记已保存到Firestore，文档ID:', docRef.id);
    // 返回Firebase自动生成的文档ID和日记数据
    return { 
      id: docRef.id,  // 这是Firebase自动生成的唯一ID
      ...diaryData 
    };
    
  } catch (error) {
    console.error('保存到Firestore失败:', error);
    console.log('日记内容（未保存）:', diaryContent);
  }
}
