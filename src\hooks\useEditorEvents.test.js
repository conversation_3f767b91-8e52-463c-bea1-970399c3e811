import { renderHook } from '@testing-library/react';
import { vi } from 'vitest';
import { useEditorEvents } from './useEditorEvents';

// Mock useSuggestionHandler hook
vi.mock('./useSuggestionHandler', () => ({
  useSuggestionHandler: vi.fn()
}));

import { useSuggestionHandler } from './useSuggestionHandler';

describe('useEditorEvents', () => {
  const mockText = 'Test text';
  const mockSetText = vi.fn();
  const mockSuggestions = [{ id: '1', text: 'suggestion' }];
  const mockSetSuggestions = vi.fn();
  const mockDispatch = vi.fn();

  const mockApplySuggestion = vi.fn();
  const mockDismissSuggestion = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    useSuggestionHandler.mockReturnValue({
      applySuggestion: mockApplySuggestion,
      dismissSuggestion: mockDismissSuggestion
    });
  });

  it('should return suggestion handlers and event handlers', () => {
    const { result } = renderHook(() => 
      useEditorEvents(mockText, mockSetText, mockSuggestions, mockSetSuggestions, mockDispatch)
    );

    expect(result.current.applySuggestion).toBe(mockApplySuggestion);
    expect(result.current.dismissSuggestion).toBe(mockDismissSuggestion);
    expect(typeof result.current.handleLookupWord).toBe('function');
    expect(typeof result.current.handleShowApiConfig).toBe('function');
    expect(typeof result.current.handleShowDictionary).toBe('function');
    expect(typeof result.current.handleSwitchToChat).toBe('function');
  });

  it('should call useSuggestionHandler with correct parameters', () => {
    renderHook(() => 
      useEditorEvents(mockText, mockSetText, mockSuggestions, mockSetSuggestions, mockDispatch)
    );

    expect(useSuggestionHandler).toHaveBeenCalledWith(
      mockText,
      mockSetText,
      mockSuggestions,
      mockSetSuggestions
    );
  });

  it('should handle lookup word request', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    const { result } = renderHook(() => 
      useEditorEvents(mockText, mockSetText, mockSuggestions, mockSetSuggestions, mockDispatch)
    );
    const word = 'test';

    result.current.handleLookupWord(word);

    expect(consoleSpy).toHaveBeenCalledWith('EditorPage: 词典被请求，单词:', word);
    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'SHOW_DICTIONARY',
      payload: true,
      word: word
    });

    consoleSpy.mockRestore();
  });

  it('should handle show API config request', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    const { result } = renderHook(() => 
      useEditorEvents(mockText, mockSetText, mockSuggestions, mockSetSuggestions, mockDispatch)
    );

    result.current.handleShowApiConfig();

    expect(consoleSpy).toHaveBeenCalledWith('EditorPage: 设置按钮被点击');
    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'SHOW_API_CONFIG',
      payload: true
    });

    consoleSpy.mockRestore();
  });

  it('should handle show dictionary request', () => {
    const { result } = renderHook(() => 
      useEditorEvents(mockText, mockSetText, mockSuggestions, mockSetSuggestions, mockDispatch)
    );

    result.current.handleShowDictionary();

    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'SHOW_DICTIONARY',
      payload: true
    });
  });

  it('should handle switch to chat request', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    const { result } = renderHook(() => 
      useEditorEvents(mockText, mockSetText, mockSuggestions, mockSetSuggestions, mockDispatch)
    );

    result.current.handleSwitchToChat();

    expect(consoleSpy).toHaveBeenCalledWith('EdgeNavigationArrow: 切换到聊天模式');
    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'SET_CURRENT_PAGE',
      payload: 'chat'
    });

    consoleSpy.mockRestore();
  });

  it('should return the same functions on re-render', () => {
    const { result, rerender } = renderHook(() => 
      useEditorEvents(mockText, mockSetText, mockSuggestions, mockSetSuggestions, mockDispatch)
    );

    const firstRender = {
      applySuggestion: result.current.applySuggestion,
      dismissSuggestion: result.current.dismissSuggestion,
      handleLookupWord: result.current.handleLookupWord,
      handleShowApiConfig: result.current.handleShowApiConfig,
      handleShowDictionary: result.current.handleShowDictionary,
      handleSwitchToChat: result.current.handleSwitchToChat
    };

    rerender();

    // Note: Functions may be recreated on re-render due to useCallback dependencies
    // We'll just verify they exist and are functions
    expect(typeof result.current.applySuggestion).toBe('function');
    expect(typeof result.current.dismissSuggestion).toBe('function');
    expect(typeof result.current.handleLookupWord).toBe('function');
    expect(typeof result.current.handleShowApiConfig).toBe('function');
    expect(typeof result.current.handleShowDictionary).toBe('function');
    expect(typeof result.current.handleSwitchToChat).toBe('function');
  });
});
