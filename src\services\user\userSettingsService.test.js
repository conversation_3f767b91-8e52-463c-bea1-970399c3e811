import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock Firebase functions
vi.mock('firebase/firestore');
vi.mock('../../config/firebaseConfig', () => ({
  db: {}
}));

import {
    getUserSettings,
    initializeUserSettings,
    saveChatSettings,
    saveThemeSettings,
    saveVoiceSettings,
    updateUserSettings
} from './userSettingsService';

describe('userSettingsService', () => {
  const mockUserId = 'test-user-123';
  const mockUserDocRef = { id: mockUserId };

  beforeEach(async () => {
    vi.clearAllMocks();
    const { doc } = await import('firebase/firestore');
    vi.mocked(doc).mockReturnValue(mockUserDocRef);
  });

  describe('getUserSettings', () => {
    it('应该返回现有用户设置', async () => {
      const mockSettings = {
        theme: 'dark',
        autoPlayTTS: true,
        aiResponseSound: false,
        autoShowTranslation: true,
        autoShowSuggestion: false,
        requestsUsedToday: 5,
        lastRequestDate: '2024-01-01',
        isPremiumUser: false
      };

      const { getDoc } = await import('firebase/firestore');
      vi.mocked(getDoc).mockResolvedValue({
        exists: () => true,
        data: () => mockSettings
      });

      const result = await getUserSettings(mockUserId);

      expect(result).toEqual(mockSettings);
    });

    it('应该为不存在用户创建默认设置', async () => {
      const { getDoc, setDoc } = await import('firebase/firestore');
      vi.mocked(getDoc).mockResolvedValue({
        exists: () => false
      });

      const result = await getUserSettings(mockUserId);

      expect(vi.mocked(setDoc)).toHaveBeenCalledWith(
        mockUserDocRef,
        expect.objectContaining({
          theme: 'light',
          autoPlayTTS: false,
          aiResponseSound: true,
          autoShowTranslation: true,
          autoShowSuggestion: false,
          requestsUsedToday: 0,
          isPremiumUser: false,
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
          lastRequestDate: expect.any(String)
        })
      );
    });

    it('应该处理错误', async () => {
      const { getDoc } = await import('firebase/firestore');
      vi.mocked(getDoc).mockRejectedValue(new Error('Firebase error'));

      await expect(getUserSettings(mockUserId)).rejects.toThrow('获取用户设置失败: Firebase error');
    });

    it('应该验证用户ID', async () => {
      await expect(getUserSettings('')).rejects.toThrow('用户ID不能为空');
      await expect(getUserSettings(null)).rejects.toThrow('用户ID不能为空');
      await expect(getUserSettings(undefined)).rejects.toThrow('用户ID不能为空');
    });
  });

  describe('saveChatSettings', () => {
    it('应该保存聊天设置', async () => {
      const { updateDoc } = await import('firebase/firestore');
      const autoShowTranslation = true;
      const autoShowSuggestion = false;

      await saveChatSettings(mockUserId, autoShowTranslation, autoShowSuggestion);

      expect(vi.mocked(updateDoc)).toHaveBeenCalledWith(
        mockUserDocRef,
        {
          autoShowTranslation,
          autoShowSuggestion,
          updatedAt: expect.any(String)
        }
      );
    });

    it('应该处理保存错误', async () => {
      const { updateDoc } = await import('firebase/firestore');
      vi.mocked(updateDoc).mockRejectedValue(new Error('Update failed'));

      await expect(saveChatSettings(mockUserId, true, false)).rejects.toThrow('保存聊天设置失败: 更新用户设置失败: Update failed');
    });

    it('应该验证用户ID', async () => {
      await expect(saveChatSettings('', true, false)).rejects.toThrow('用户ID不能为空');
    });
  });

  describe('saveThemeSettings', () => {
    it('应该保存主题设置', async () => {
      const { updateDoc } = await import('firebase/firestore');
      vi.mocked(updateDoc).mockResolvedValue();
      const theme = 'dark';

      await saveThemeSettings(mockUserId, theme);

      expect(vi.mocked(updateDoc)).toHaveBeenCalledWith(
        mockUserDocRef,
        {
          theme,
          updatedAt: expect.any(String)
        }
      );
    });

    it('应该处理保存错误', async () => {
      const { updateDoc } = await import('firebase/firestore');
      vi.mocked(updateDoc).mockRejectedValue(new Error('Update failed'));

      await expect(saveThemeSettings(mockUserId, 'dark')).rejects.toThrow('保存主题设置失败: 更新用户设置失败: Update failed');
    });
  });

  describe('saveVoiceSettings', () => {
    it('应该保存语音设置', async () => {
      const { updateDoc } = await import('firebase/firestore');
      vi.mocked(updateDoc).mockResolvedValue();
      const autoPlayTTS = true;
      const aiResponseSound = false;

      await saveVoiceSettings(mockUserId, autoPlayTTS, aiResponseSound);

      expect(vi.mocked(updateDoc)).toHaveBeenCalledWith(
        mockUserDocRef,
        {
          autoPlayTTS,
          aiResponseSound,
          updatedAt: expect.any(String)
        }
      );
    });

    it('应该处理保存错误', async () => {
      const { updateDoc } = await import('firebase/firestore');
      vi.mocked(updateDoc).mockRejectedValue(new Error('Update failed'));

      await expect(saveVoiceSettings(mockUserId, true, false)).rejects.toThrow('保存语音设置失败: 更新用户设置失败: Update failed');
    });
  });

  describe('updateUserSettings', () => {
    it('应该更新用户设置', async () => {
      const { updateDoc } = await import('firebase/firestore');
      vi.mocked(updateDoc).mockResolvedValue();
      const settings = {
        theme: 'dark',
        autoPlayTTS: true
      };

      await updateUserSettings(mockUserId, settings);

      expect(vi.mocked(updateDoc)).toHaveBeenCalledWith(
        mockUserDocRef,
        {
          ...settings,
          updatedAt: expect.any(String)
        }
      );
    });

    it('应该处理更新错误', async () => {
      const { updateDoc } = await import('firebase/firestore');
      vi.mocked(updateDoc).mockRejectedValue(new Error('Update failed'));

      await expect(updateUserSettings(mockUserId, {})).rejects.toThrow('更新用户设置失败: Update failed');
    });
  });

  describe('initializeUserSettings', () => {
    it('应该初始化新用户设置', async () => {
      const { getDoc, setDoc } = await import('firebase/firestore');
      vi.mocked(getDoc).mockResolvedValue({
        exists: () => false
      });
      vi.mocked(setDoc).mockResolvedValue();
      const email = '<EMAIL>';

      await initializeUserSettings(mockUserId, email);

      expect(vi.mocked(setDoc)).toHaveBeenCalledWith(
        mockUserDocRef,
        expect.objectContaining({
          email,
          theme: 'light',
          autoPlayTTS: false,
          aiResponseSound: true,
          requestsUsedToday: 0,
          isPremiumUser: false,
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
          lastRequestDate: expect.any(String)
        })
      );
    });

    it('应该处理初始化错误', async () => {
      const { getDoc, setDoc } = await import('firebase/firestore');
      vi.mocked(getDoc).mockResolvedValue({
        exists: () => false
      });
      vi.mocked(setDoc).mockRejectedValue(new Error('Init failed'));

      await expect(initializeUserSettings(mockUserId, '<EMAIL>')).rejects.toThrow('初始化用户设置失败: 更新用户设置失败: Update failed');
    });
  });

  describe('设置默认值', () => {
    it('应该包含正确的默认设置', async () => {
      const { getDoc, setDoc } = await import('firebase/firestore');
      vi.mocked(getDoc).mockRejectedValue(new Error('Init failed'));

      await expect(getUserSettings(mockUserId)).rejects.toThrow('获取用户设置失败: Init failed');
    });
  });
});
