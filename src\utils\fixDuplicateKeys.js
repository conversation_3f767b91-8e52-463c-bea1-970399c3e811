/**
 * 修复重复键问题
 * 从Firebase获取真实数据并重新分配唯一ID
 */

import { auth } from '../config/firebaseConfig';
import { generateUniqueId } from './idGenerator';

/**
 * 修复AI分析历史的重复键问题
 */
export const fixAnalysisHistoryKeys = async () => {
  console.log('🔧 开始修复AI分析历史的重复键问题...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    console.log('👤 用户ID:', user.uid);
    
    // 导入Firebase服务
    const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
    
    // 获取Firebase中的真实数据
    console.log('📥 从Firebase获取AI分析历史...');
    const firebaseData = await aiAnalysisService.getAnalysisHistory(user.uid, 100);
    console.log(`📊 从Firebase获取到 ${firebaseData.length} 条记录`);
    
    if (firebaseData.length === 0) {
      console.log('ℹ️ 没有数据需要修复');
      return true;
    }
    
    // 检查是否有重复键问题
    const duplicateKeys = [];
    const keyCounts = {};
    
    firebaseData.forEach((record, index) => {
      const key = record.id || index;
      if (keyCounts[key]) {
        keyCounts[key]++;
        duplicateKeys.push({ key, index, record });
      } else {
        keyCounts[key] = 1;
      }
    });
    
    console.log(`🔍 发现 ${duplicateKeys.length} 个重复键问题`);
    
    if (duplicateKeys.length === 0) {
      console.log('✅ 没有重复键问题，无需修复');
      return true;
    }
    
    // 显示重复键详情
    duplicateKeys.forEach(({ key, index, record }) => {
      console.log(`⚠️ 重复键: ${key}, 索引: ${index}, 文本: ${record.text?.substring(0, 50)}...`);
    });
    
    // 重新分配唯一ID
    console.log('🔄 开始重新分配唯一ID...');
    const updatedData = firebaseData.map((record, index) => {
      // 检查是否需要重新分配ID
      const needsNewId = duplicateKeys.some(dup => dup.index === index) || 
                        !record.id || 
                        /^\d{13}$/.test(record.id.toString());
      
      if (needsNewId) {
        const newId = generateUniqueId();
        console.log(`🆔 重新分配ID: ${record.id} -> ${newId}`);
        return {
          ...record,
          id: newId,
          originalId: record.id, // 保留原始ID作为参考
          updatedAt: new Date().toISOString()
        };
      }
      
      return record;
    });
    
    // 保存更新后的数据到Firebase
    console.log('💾 保存更新后的数据到Firebase...');
    let successCount = 0;
    let errorCount = 0;
    
    for (const record of updatedData) {
      try {
        if (record.originalId) {
          // 删除旧记录
          await aiAnalysisService.deleteAnalysis(user.uid, record.originalId);
          console.log(`🗑️ 删除旧记录: ${record.originalId}`);
        }
        
        // 保存新记录
        await aiAnalysisService.saveAnalysis(record, user.uid);
        console.log(`✅ 保存新记录: ${record.id}`);
        successCount++;
        
        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`❌ 处理记录失败: ${record.id}`, error);
        errorCount++;
      }
    }
    
    console.log(`📊 修复完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`);
    
    // 清理本地缓存
    console.log('🧹 清理本地缓存...');
    const cacheKey = 'cache_analysis_history';
    localStorage.removeItem(cacheKey);
    console.log('✅ 本地缓存已清理');
    
    return successCount > 0;
    
  } catch (error) {
    console.error('❌ 修复AI分析历史重复键失败:', error);
    return false;
  }
};

/**
 * 修复聊天历史的重复键问题
 */
export const fixChatHistoryKeys = async () => {
  console.log('🔧 开始修复聊天历史的重复键问题...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    console.log('👤 用户ID:', user.uid);
    
    // 导入Firebase服务
    const { chatHistoryService } = await import('../services/history/firebaseHistoryService');
    
    // 获取Firebase中的真实数据
    console.log('📥 从Firebase获取聊天历史...');
    const firebaseData = await chatHistoryService.getChatHistory(user.uid, 100);
    console.log(`📊 从Firebase获取到 ${firebaseData.length} 条记录`);
    
    if (firebaseData.length === 0) {
      console.log('ℹ️ 没有数据需要修复');
      return true;
    }
    
    // 检查是否有重复键问题
    const duplicateKeys = [];
    const keyCounts = {};
    
    firebaseData.forEach((record, index) => {
      const key = record.id || index;
      if (keyCounts[key]) {
        keyCounts[key]++;
        duplicateKeys.push({ key, index, record });
      } else {
        keyCounts[key] = 1;
      }
    });
    
    console.log(`🔍 发现 ${duplicateKeys.length} 个重复键问题`);
    
    if (duplicateKeys.length === 0) {
      console.log('✅ 没有重复键问题，无需修复');
      return true;
    }
    
    // 重新分配唯一ID
    console.log('🔄 开始重新分配唯一ID...');
    const updatedData = firebaseData.map((record, index) => {
      const needsNewId = duplicateKeys.some(dup => dup.index === index) || 
                        !record.id || 
                        /^\d{13}$/.test(record.id.toString());
      
      if (needsNewId) {
        const newId = generateUniqueId();
        console.log(`🆔 重新分配ID: ${record.id} -> ${newId}`);
        return {
          ...record,
          id: newId,
          originalId: record.id,
          updatedAt: new Date().toISOString()
        };
      }
      
      return record;
    });
    
    // 保存更新后的数据
    console.log('💾 保存更新后的数据到Firebase...');
    let successCount = 0;
    let errorCount = 0;
    
    for (const record of updatedData) {
      try {
        if (record.originalId) {
          await chatHistoryService.deleteChatSession(user.uid, record.originalId);
          console.log(`🗑️ 删除旧记录: ${record.originalId}`);
        }
        
        await chatHistoryService.saveChatSession(record, user.uid);
        console.log(`✅ 保存新记录: ${record.id}`);
        successCount++;
        
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`❌ 处理记录失败: ${record.id}`, error);
        errorCount++;
      }
    }
    
    console.log(`📊 修复完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`);
    
    // 清理本地缓存
    console.log('🧹 清理本地缓存...');
    const cacheKey = 'cache_chat_history';
    localStorage.removeItem(cacheKey);
    console.log('✅ 本地缓存已清理');
    
    return successCount > 0;
    
  } catch (error) {
    console.error('❌ 修复聊天历史重复键失败:', error);
    return false;
  }
};

/**
 * 修复所有历史记录的重复键问题
 */
export const fixAllHistoryKeys = async () => {
  console.log('🔧 开始修复所有历史记录的重复键问题...');
  
  const results = {
    analysis: false,
    chat: false,
    dictionary: false,
    writing: false,
    diary: false
  };
  
  try {
    // 修复AI分析历史
    console.log('\n📝 修复AI分析历史...');
    results.analysis = await fixAnalysisHistoryKeys();
    
    // 修复聊天历史
    console.log('\n💬 修复聊天历史...');
    results.chat = await fixChatHistoryKeys();
    
    // 修复其他历史记录类型
    console.log('\n📚 修复其他历史记录...');
    // 这里可以添加其他类型的修复逻辑
    
    console.log('\n📊 修复结果汇总:', results);
    
    const successCount = Object.values(results).filter(Boolean).length;
    console.log(`✅ 成功修复 ${successCount} 种历史记录类型`);
    
    return results;
    
  } catch (error) {
    console.error('❌ 修复所有历史记录失败:', error);
    return results;
  }
};

/**
 * 检查重复键问题
 */
export const checkDuplicateKeys = async () => {
  console.log('🔍 检查重复键问题...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    const { aiAnalysisService, chatHistoryService } = await import('../services/history/firebaseHistoryService');
    
    // 检查AI分析历史
    const analysisData = await aiAnalysisService.getAnalysisHistory(user.uid, 100);
    const analysisDuplicates = analysisData.filter(record => 
      !record.id || /^\d{13}$/.test(record.id.toString())
    );
    
    // 检查聊天历史
    const chatData = await chatHistoryService.getChatHistory(user.uid, 100);
    const chatDuplicates = chatData.filter(record => 
      !record.id || /^\d{13}$/.test(record.id.toString())
    );
    
    const results = {
      analysis: {
        total: analysisData.length,
        duplicates: analysisDuplicates.length,
        duplicateIds: analysisDuplicates.map(r => r.id)
      },
      chat: {
        total: chatData.length,
        duplicates: chatDuplicates.length,
        duplicateIds: chatDuplicates.map(r => r.id)
      }
    };
    
    console.log('📊 重复键检查结果:', results);
    
    const totalDuplicates = results.analysis.duplicates + results.chat.duplicates;
    console.log(`🔍 总共发现 ${totalDuplicates} 个重复键问题`);
    
    return results;
    
  } catch (error) {
    console.error('❌ 检查重复键失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.fixKeys = {
    fixAnalysisHistoryKeys,
    fixChatHistoryKeys,
    fixAllHistoryKeys,
    checkDuplicateKeys
  };
  
  console.log('🔧 重复键修复工具已加载到 window.fixKeys');
  console.log('💡 使用方法:');
  console.log('  - await window.fixKeys.checkDuplicateKeys() // 检查重复键问题');
  console.log('  - await window.fixKeys.fixAnalysisHistoryKeys() // 修复AI分析历史');
  console.log('  - await window.fixKeys.fixChatHistoryKeys() // 修复聊天历史');
  console.log('  - await window.fixKeys.fixAllHistoryKeys() // 修复所有历史记录');
}
