import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { vi } from 'vitest';
import DictionaryLookup from './DictionaryLookup';

// Mock dictionary service
vi.mock('../services/dictionary/unifiedDictionaryService', () => ({
  getWordDetails: vi.fn()
}));

vi.mock('../services/writing/historyService', () => ({
  getSearchHistory: vi.fn(),
  addSearchToHistory: vi.fn(),
  clearSearchHistory: vi.fn(),
  getWordDetails: vi.fn()
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  Search: () => <div data-testid="search-icon" />,
  Volume2: () => <div data-testid="volume-icon" />,
  ExternalLink: () => <div data-testid="external-link-icon" />,
  Trash2: () => <div data-testid="trash-icon" />
}));

// Mock Audio API
global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(),
  pause: vi.fn(),
  currentTime: 0,
  duration: 0
}));

describe('DictionaryLookup', () => {
  const defaultProps = {
    word: '',
    onClose: vi.fn(),
    isDarkMode: false,
    dictionaryService: 'free'
  };

  const mockWordData = {
    word: 'hello',
    phonetics: [
      { text: '/həˈloʊ/', audio: 'https://example.com/hello.mp3' }
    ],
    meanings: [
      {
        partOfSpeech: 'noun',
        definitions: [
          {
            definition: 'A greeting',
            example: 'She said hello to everyone.'
          }
        ]
      }
    ]
  };

  beforeEach(async () => {
    vi.clearAllMocks();

    // Setup default mocks
    const { getSearchHistory, addSearchToHistory, clearSearchHistory } = await import('../services/writing/historyService');
    const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');

    vi.mocked(getSearchHistory).mockReturnValue([]);
    vi.mocked(addSearchToHistory).mockImplementation(() => { });
    vi.mocked(clearSearchHistory).mockImplementation(() => { });
    vi.mocked(getWordDetails).mockResolvedValue(mockWordData);
  });

  describe('基本渲染', () => {
    it('应该渲染搜索输入框', () => {
      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      expect(searchInput).toBeInTheDocument();
    });

    it('应该渲染搜索按钮', () => {
      render(<DictionaryLookup {...defaultProps} />);

      const searchButton = screen.getByTitle('搜索');
      expect(searchButton).toBeInTheDocument();
    });

    it('应该在有初始单词时显示该单词', () => {
      render(<DictionaryLookup {...defaultProps} word="hello" />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      expect(searchInput.value).toBe('hello');
    });
  });

  describe('搜索功能', () => {
    it('应该能够搜索单词', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'hello' } });
      fireEvent.submit(searchInput.closest('form'));

      await waitFor(() => {
        expect(vi.mocked(getWordDetails)).toHaveBeenCalledWith('hello', 'free');
      });
    });

    it('应该在按Enter时搜索', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'hello' } });
      fireEvent.submit(searchInput.closest('form'));

      await waitFor(() => {
        expect(vi.mocked(getWordDetails)).toHaveBeenCalledWith('hello', 'free');
      });
    });

    it('应该显示搜索结果', async () => {
      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'hello' } });
      fireEvent.submit(searchInput.closest('form'));

      await waitFor(() => {
        expect(screen.getByText('hello')).toBeInTheDocument();
        expect(screen.getByText('/həˈloʊ/')).toBeInTheDocument();
        expect(screen.getByText((content, element) => {
          return element?.textContent === '1. A greeting';
        })).toBeInTheDocument();
        expect(screen.getByText('noun')).toBeInTheDocument();
      });
    });

    it('应该在搜索时显示加载状态', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');
      // Mock a delayed response
      vi.mocked(getWordDetails).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockWordData), 100))
      );

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'hello' } });
      fireEvent.submit(searchInput.closest('form'));

      // 应该显示加载状态（spinner）
      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();

      // 等待搜索完成
      await waitFor(() => {
        expect(screen.getByText('hello')).toBeInTheDocument();
      });
    });

    it('应该处理搜索错误', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');
      vi.mocked(getWordDetails).mockRejectedValue(new Error('Network error'));

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'hello' } });
      fireEvent.submit(searchInput.closest('form'));

      await waitFor(() => {
        expect(screen.getByText('无法查询该单词，请检查网络连接或稍后再试')).toBeInTheDocument();
      });
    });

    it('应该在有初始单词时自动搜索', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');

      render(<DictionaryLookup {...defaultProps} word="hello" />);

      await waitFor(() => {
        expect(vi.mocked(getWordDetails)).toHaveBeenCalledWith('hello', 'free');
      });
    });
  });

  describe('历史功能', () => {
    it('应该显示搜索历史', async () => {
      const { getSearchHistory } = await import('../services/writing/historyService');
      const mockHistory = ['hello', 'world', 'test'];
      vi.mocked(getSearchHistory).mockReturnValue(mockHistory);

      render(<DictionaryLookup {...defaultProps} />);

      expect(screen.getByText('搜索历史')).toBeInTheDocument();
      mockHistory.forEach(word => {
        expect(screen.getByText(word)).toBeInTheDocument();
      });
    });

    it('应该能够点击历史记录进行搜索', async () => {
      const { getSearchHistory, getWordDetails } = await import('../services/writing/historyService');
      const mockHistory = ['hello'];
      vi.mocked(getSearchHistory).mockReturnValue(mockHistory);

      render(<DictionaryLookup {...defaultProps} />);

      const historyItem = screen.getByText('hello');
      fireEvent.click(historyItem);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      expect(searchInput.value).toBe('hello');
    });

    it('应该能够清除历史记录', async () => {
      const { getSearchHistory, clearSearchHistory } = await import('../services/writing/historyService');
      const mockHistory = ['hello', 'world'];
      vi.mocked(getSearchHistory).mockReturnValue(mockHistory);

      render(<DictionaryLookup {...defaultProps} />);

      const clearButton = screen.getByText('清空');
      fireEvent.click(clearButton);

      expect(vi.mocked(clearSearchHistory)).toHaveBeenCalled();
    });

    it('应该在成功搜索后添加到历史记录', async () => {
      const { addSearchToHistory } = await import('../services/writing/historyService');

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'hello' } });
      fireEvent.submit(searchInput.closest('form'));

      await waitFor(() => {
        expect(vi.mocked(addSearchToHistory)).toHaveBeenCalledWith('hello');
      });
    });
  });

  describe('音频播放', () => {
    it('应该显示播放按钮', async () => {
      render(<DictionaryLookup {...defaultProps} word="hello" />);

      await waitFor(() => {
        const playButton = screen.getByTitle('播放发音');
        expect(playButton).toBeInTheDocument();
      });
    });

    it('应该能够播放音频', async () => {
      render(<DictionaryLookup {...defaultProps} word="hello" />);

      await waitFor(() => {
        const playButton = screen.getByTitle('播放发音');
        fireEvent.click(playButton);

        expect(global.Audio).toHaveBeenCalledWith('https://example.com/hello.mp3');
      });
    });
  });

  describe('样式和主题', () => {
    it('应该在深色模式下应用正确样式', () => {
      render(<DictionaryLookup {...defaultProps} isDarkMode={true} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      expect(searchInput).toBeInTheDocument();
      // 深色模式的样式通过CSS变量应用，这里主要测试不会出错
    });

    it('应该在浅色模式下应用正确样式', () => {
      render(<DictionaryLookup {...defaultProps} isDarkMode={false} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      expect(searchInput).toBeInTheDocument();
      // 浅色模式的样式通过CSS变量应用，这里主要测试不会出错
    });

    it('应该在输入框获得焦点时改变样式', () => {
      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.focus(searchInput);
      // 样式变化通过内联样式应用，这里主要测试不会出错
      // 注意：在测试环境中focus可能不会完全模拟真实行为

      fireEvent.blur(searchInput);
      // 测试失去焦点也不会出错
    });
  });

  describe('边界情况', () => {
    it('应该处理空搜索', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: '   ' } }); // 空白字符
      fireEvent.submit(searchInput.closest('form'));

      // 空搜索不应该调用API
      expect(vi.mocked(getWordDetails)).not.toHaveBeenCalled();
    });

    it('应该处理未找到单词的情况', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');
      vi.mocked(getWordDetails).mockResolvedValue({ notFound: true });

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'nonexistentword' } });
      fireEvent.submit(searchInput.closest('form'));

      await waitFor(() => {
        expect(screen.getByText('未找到单词 "nonexistentword"')).toBeInTheDocument();
      });
    });

    it('应该处理没有音频的音标', async () => {
      const { getWordDetails } = await import('../services/dictionary/unifiedDictionaryService');
      const wordDataWithoutAudio = {
        ...mockWordData,
        phonetics: [{ text: '/həˈloʊ/' }] // 没有audio字段
      };
      vi.mocked(getWordDetails).mockResolvedValue(wordDataWithoutAudio);

      render(<DictionaryLookup {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('输入要查询的单词...');
      fireEvent.change(searchInput, { target: { value: 'hello' } });
      fireEvent.submit(searchInput.closest('form'));

      await waitFor(() => {
        expect(screen.getByText('/həˈloʊ/')).toBeInTheDocument();
        // 没有音频时不应该显示播放按钮
        expect(screen.queryByTitle('播放发音')).not.toBeInTheDocument();
      });
    });
  });
});
