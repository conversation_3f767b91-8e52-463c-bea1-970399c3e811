import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import EditorActionBar from './EditorActionBar';

// Mock DateTimeWeather component
vi.mock('./DateTimeWeather', () => ({
  default: function MockDateTimeWeather({ isDarkMode }) {
    return <div data-testid="date-time-weather">DateTimeWeather {isDarkMode ? 'dark' : 'light'}</div>;
  }
}));

describe('EditorActionBar', () => {
  const defaultProps = {
    isDarkMode: false,
    rawAIResponse: null,
    onShowDictionary: vi.fn(),
    onShowHistory: vi.fn(),
    onShowAIResponse: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render DateTimeWeather component', () => {
    render(<EditorActionBar {...defaultProps} />);
    
    expect(screen.getByTestId('date-time-weather')).toBeInTheDocument();
  });

  it('should render dictionary button', () => {
    render(<EditorActionBar {...defaultProps} />);
    
    const dictionaryButton = screen.getByTitle('词典查询');
    expect(dictionaryButton).toBeInTheDocument();
  });

  it('should render history button', () => {
    render(<EditorActionBar {...defaultProps} />);
    
    const historyButton = screen.getByTitle('历史记录');
    expect(historyButton).toBeInTheDocument();
  });

  it('should not render AI response button when no rawAIResponse', () => {
    render(<EditorActionBar {...defaultProps} />);
    
    const aiResponseButton = screen.queryByTitle('分析与改进建议');
    expect(aiResponseButton).not.toBeInTheDocument();
  });

  it('should render AI response button when rawAIResponse exists', () => {
    render(<EditorActionBar {...defaultProps} rawAIResponse="Some AI response" />);
    
    const aiResponseButton = screen.getByTitle('分析与改进建议');
    expect(aiResponseButton).toBeInTheDocument();
  });

  it('should call onShowDictionary when dictionary button is clicked', () => {
    const onShowDictionary = vi.fn();
    render(<EditorActionBar {...defaultProps} onShowDictionary={onShowDictionary} />);
    
    const dictionaryButton = screen.getByTitle('词典查询');
    fireEvent.click(dictionaryButton);
    
    expect(onShowDictionary).toHaveBeenCalledTimes(1);
  });

  it('should call onShowHistory when history button is clicked', () => {
    const onShowHistory = vi.fn();
    render(<EditorActionBar {...defaultProps} onShowHistory={onShowHistory} />);
    
    const historyButton = screen.getByTitle('历史记录');
    fireEvent.click(historyButton);
    
    expect(onShowHistory).toHaveBeenCalledTimes(1);
  });

  it('should call onShowAIResponse when AI response button is clicked', () => {
    const onShowAIResponse = vi.fn();
    render(
      <EditorActionBar 
        {...defaultProps} 
        rawAIResponse="Some AI response"
        onShowAIResponse={onShowAIResponse}
      />
    );
    
    const aiResponseButton = screen.getByTitle('分析与改进建议');
    fireEvent.click(aiResponseButton);
    
    expect(onShowAIResponse).toHaveBeenCalledTimes(1);
  });

  it('should pass isDarkMode to DateTimeWeather', () => {
    render(<EditorActionBar {...defaultProps} isDarkMode={true} />);
    
    expect(screen.getByText('DateTimeWeather dark')).toBeInTheDocument();
  });

  it('should apply correct styles for dark mode', () => {
    render(<EditorActionBar {...defaultProps} isDarkMode={true} />);
    
    // 检查按钮图标颜色（通过style属性）
    const dictionaryButton = screen.getByTitle('词典查询');
    const icon = dictionaryButton.querySelector('svg');
    expect(icon).toHaveStyle('color: #D2691E');
  });

  it('should apply correct styles for light mode', () => {
    render(<EditorActionBar {...defaultProps} isDarkMode={false} />);
    
    // 检查按钮图标颜色（通过style属性）
    const dictionaryButton = screen.getByTitle('词典查询');
    const icon = dictionaryButton.querySelector('svg');
    expect(icon).toHaveStyle('color: #166534');
  });
});
