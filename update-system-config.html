<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新系统配置</title>
    <style>
        body {
            font-family: monospace;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin-top: 10px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 11px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 更新系统配置</h1>
        
        <button onclick="readCurrentConfig()">读取当前配置</button>
        <button onclick="updateToNewStructure()">更新为新结构</button>
        <button onclick="verifyNewConfig()">验证新配置</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="result" class="result">点击按钮开始操作...</div>
    </div>

    <script type="module">
        let resultDiv = document.getElementById('result');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            let prefix = '📝';
            if (type === 'error') prefix = '❌';
            else if (type === 'success') prefix = '✅';
            else if (type === 'warning') prefix = '⚠️';
            
            resultDiv.innerHTML += `[${timestamp}] ${prefix} ${message}\n`;
            
            if (type === 'error') {
                resultDiv.className = 'result error';
            } else if (type === 'success') {
                resultDiv.className = 'result success';
            } else if (type === 'warning') {
                resultDiv.className = 'result warning';
            }
            
            console.log(message);
        }
        
        window.clearResults = () => {
            resultDiv.innerHTML = '准备开始操作...\n';
            resultDiv.className = 'result';
        };
        
        window.readCurrentConfig = async () => {
            try {
                log('📖 读取当前系统配置...');
                
                const { db } = await import('./src/config/firebaseConfig.js');
                const { doc, getDoc } = await import('firebase/firestore');
                
                const configDocRef = doc(db, 'systemConfig', 'main');
                const configDoc = await getDoc(configDocRef);
                
                if (configDoc.exists()) {
                    const data = configDoc.data();
                    log('✅ 当前配置读取成功:', 'success');
                    log(JSON.stringify(data, null, 2));
                    
                    // 分析配置结构
                    log('🔍 配置结构分析:');
                    log(`   - 旧结构 maxRequestsPerDay: ${data.maxRequestsPerDay ? '存在' : '不存在'}`);
                    log(`   - 新结构 freeUserLimits: ${data.freeUserLimits ? '存在' : '不存在'}`);
                    log(`   - 新结构 paidUserLimits: ${data.paidUserLimits ? '存在' : '不存在'}`);
                    
                    if (data.maxRequestsPerDay && !data.freeUserLimits) {
                        log('⚠️ 检测到旧配置结构，需要更新', 'warning');
                    } else if (data.freeUserLimits && data.paidUserLimits) {
                        log('✅ 配置结构已是最新版本', 'success');
                    }
                    
                } else {
                    log('❌ 系统配置文档不存在', 'error');
                }
                
            } catch (error) {
                log(`❌ 读取配置失败: ${error.message}`, 'error');
            }
        };
        
        window.updateToNewStructure = async () => {
            try {
                log('🔄 开始更新系统配置结构...');
                
                const { db } = await import('./src/config/firebaseConfig.js');
                const { doc, setDoc } = await import('firebase/firestore');
                
                // 新的配置结构
                const newConfig = {
                    doubaoApiKey: '5f480627-1927-49b3-8dc4-0e3f47a75a99',
                    // 免费版限制
                    freeUserLimits: {
                        chatRequestsPerDay: 10,      // 每日AI对话次数
                        writingAnalysisPerDay: 5     // 每日写作纠错次数
                    },
                    // 付费版配置
                    paidUserLimits: {
                        basicPlan: {
                            price: 20,                 // ¥20/月
                            chatRequestsPerDay: -1,    // 无限次对话 (-1表示无限制)
                            writingAnalysisPerDay: -1  // 无限次写作纠错
                        }
                    },
                    enablePaymentSystem: false, // 支付系统开关
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                log('📝 新配置结构:');
                log(JSON.stringify(newConfig, null, 2));
                
                const configDocRef = doc(db, 'systemConfig', 'main');
                await setDoc(configDocRef, newConfig);
                
                log('✅ 系统配置更新成功！', 'success');
                log('🎯 新配置特性:');
                log(`   - AI对话限制: ${newConfig.freeUserLimits.chatRequestsPerDay}次/天`);
                log(`   - 写作纠错限制: ${newConfig.freeUserLimits.writingAnalysisPerDay}次/天`);
                log(`   - 基础版价格: ${newConfig.paidUserLimits.basicPlan.price}元/月`);
                log(`   - 付费版: 无限制使用`);
                
            } catch (error) {
                log(`❌ 更新配置失败: ${error.message}`, 'error');
            }
        };
        
        window.verifyNewConfig = async () => {
            try {
                log('🔍 验证新配置结构...');
                
                const { getSystemConfig } = await import('./src/services/user/userSettingsService.js');
                
                const config = await getSystemConfig();
                
                if (config) {
                    log('✅ 配置获取成功', 'success');
                    
                    // 验证新结构
                    const hasApiKey = !!config.doubaoApiKey;
                    const hasFreeUserLimits = !!config.freeUserLimits;
                    const hasPaidUserLimits = !!config.paidUserLimits;
                    const hasChatLimit = !!config.freeUserLimits?.chatRequestsPerDay;
                    const hasWritingLimit = !!config.freeUserLimits?.writingAnalysisPerDay;
                    const hasBasicPlan = !!config.paidUserLimits?.basicPlan;
                    
                    log('🔍 结构验证结果:');
                    log(`   - API Key: ${hasApiKey ? '✅' : '❌'}`);
                    log(`   - 免费用户限制: ${hasFreeUserLimits ? '✅' : '❌'}`);
                    log(`   - 付费用户限制: ${hasPaidUserLimits ? '✅' : '❌'}`);
                    log(`   - 聊天限制配置: ${hasChatLimit ? '✅' : '❌'}`);
                    log(`   - 写作限制配置: ${hasWritingLimit ? '✅' : '❌'}`);
                    log(`   - 基础版配置: ${hasBasicPlan ? '✅' : '❌'}`);
                    
                    if (hasApiKey && hasFreeUserLimits && hasPaidUserLimits && hasChatLimit && hasWritingLimit && hasBasicPlan) {
                        log('🎉 新配置结构验证通过！', 'success');
                        log('✅ 系统配置测试现在应该能通过了');
                        return true;
                    } else {
                        log('❌ 配置结构验证失败', 'error');
                        return false;
                    }
                } else {
                    log('❌ 无法获取配置', 'error');
                    return false;
                }
                
            } catch (error) {
                log(`❌ 验证失败: ${error.message}`, 'error');
                return false;
            }
        };
        
        // 页面加载后自动读取当前配置
        window.addEventListener('load', () => {
            log('📱 配置更新工具已加载');
            
            setTimeout(() => {
                window.readCurrentConfig();
            }, 1000);
        });
    </script>
</body>
</html>
