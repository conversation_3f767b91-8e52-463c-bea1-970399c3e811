import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ConfirmDialog from './ConfirmDialog';

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  AlertTriangle: () => <div data-testid="alert-triangle-icon">AlertTriangle</div>,
  X: () => <div data-testid="close-icon">X</div>
}));

describe('ConfirmDialog', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    onConfirm: vi.fn(),
    title: '确认操作',
    message: '你确定要执行此操作吗？',
    confirmText: '确定',
    cancelText: '取消',
    isDarkMode: false,
    type: 'warning'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该渲染对话框当isOpen为true时', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    expect(screen.getByText('确认操作')).toBeInTheDocument();
    expect(screen.getByText('你确定要执行此操作吗？')).toBeInTheDocument();
  });

  it('应该不渲染对话框当isOpen为false时', () => {
    render(<ConfirmDialog {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('确认操作')).not.toBeInTheDocument();
  });

  it('应该渲染确认和取消按钮', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    expect(screen.getByText('确定')).toBeInTheDocument();
    expect(screen.getByText('取消')).toBeInTheDocument();
  });

  it('应该调用onConfirm当点击确认按钮时', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    const confirmButton = screen.getByText('确定');
    fireEvent.click(confirmButton);
    
    expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
  });

  it('应该调用onClose当点击取消按钮时', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    const cancelButton = screen.getByText('取消');
    fireEvent.click(cancelButton);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('应该调用onClose当点击关闭按钮时', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    const closeButton = screen.getByTestId('close-icon').closest('button');
    fireEvent.click(closeButton);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('应该调用onClose当点击背景遮罩时', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    // 背景遮罩是第一个 div，有 onClick={onClose}
    const backdrop = document.querySelector('.fixed.inset-0.z-50');
    fireEvent.click(backdrop);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('应该渲染警告图标当type为warning时', () => {
    render(<ConfirmDialog {...defaultProps} type="warning" />);
    
    expect(screen.getByTestId('alert-triangle-icon')).toBeInTheDocument();
  });

  it('应该应用危险样式当type为danger时', () => {
    render(<ConfirmDialog {...defaultProps} type="danger" isDarkMode={false} />);
    
    const confirmButton = screen.getByText('确定');
    expect(confirmButton).toHaveStyle('background-color: #B91C1C');
  });

  it('应该应用信息样式当type为info时', () => {
    render(<ConfirmDialog {...defaultProps} type="info" isDarkMode={false} />);
    
    const confirmButton = screen.getByText('确定');
    expect(confirmButton).toHaveStyle('background-color: #1E40AF');
  });

  it('应该应用深色模式样式', () => {
    render(<ConfirmDialog {...defaultProps} isDarkMode={true} type="danger" />);
    
    const confirmButton = screen.getByText('确定');
    expect(confirmButton).toHaveStyle('background-color: #D2691E');
  });

  it('应该使用自定义文本', () => {
    render(
      <ConfirmDialog 
        {...defaultProps} 
        title="删除确认"
        message="此操作不可撤销"
        confirmText="删除"
        cancelText="保留"
      />
    );
    
    expect(screen.getByText('删除确认')).toBeInTheDocument();
    expect(screen.getByText('此操作不可撤销')).toBeInTheDocument();
    expect(screen.getByText('删除')).toBeInTheDocument();
    expect(screen.getByText('保留')).toBeInTheDocument();
  });

  it('应该使用默认值', () => {
    render(
      <ConfirmDialog 
        isOpen={true}
        onClose={vi.fn()}
        onConfirm={vi.fn()}
        message="测试消息"
      />
    );
    
    expect(screen.getByText('确认操作')).toBeInTheDocument();
    expect(screen.getByText('确定')).toBeInTheDocument();
    expect(screen.getByText('取消')).toBeInTheDocument();
  });
});
