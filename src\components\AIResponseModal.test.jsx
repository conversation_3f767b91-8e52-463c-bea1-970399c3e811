import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import AIResponseModal from './AIResponseModal';

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  X: (props) => <div data-testid="x-icon" {...props}>X</div>,
}));

// Mock ReactMarkdown to simplify testing content rendering
vi.mock('react-markdown', () => ({
  default: ({ children }) => <div data-testid="mock-react-markdown">{children}</div>,
}));

describe('AIResponseModal', () => {
  const defaultProps = {
    isOpen: false,
    onClose: vi.fn(),
    content: '',
    isDarkMode: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('does not render when isOpen is false', () => {
    render(<AIResponseModal {...defaultProps} isOpen={false} />);
    expect(screen.queryByText('分析与改进建议')).not.toBeInTheDocument();
    expect(screen.queryByTestId('mock-react-markdown')).not.toBeInTheDocument();
  });

  it('renders when isOpen is true', () => {
    render(<AIResponseModal {...defaultProps} isOpen={true} />);
    expect(screen.getByText('分析与改进建议')).toBeInTheDocument();
    expect(screen.getByTestId('x-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-react-markdown')).toBeInTheDocument();
  });

  it('displays content correctly', () => {
    const testContent = '# Hello\nThis is a test.';
    render(<AIResponseModal {...defaultProps} isOpen={true} content={testContent} />);
    expect(screen.getByTestId('mock-react-markdown')).toHaveTextContent('# Hello This is a test.');
  });

  it('removes "分析与改进建议" title from content', () => {
    const contentWithTitle = '分析与改进建议\n\n# Section\nSome text.';
    render(<AIResponseModal {...defaultProps} isOpen={true} content={contentWithTitle} />);
    expect(screen.getByTestId('mock-react-markdown')).not.toHaveTextContent('分析与改进建议');
    expect(screen.getByTestId('mock-react-markdown')).toHaveTextContent('# Section Some text.');
  });

  it('calls onClose when backdrop is clicked', () => {
    render(<AIResponseModal {...defaultProps} isOpen={true} />);
    const backdrop = screen.getByTestId('modal-backdrop');
    fireEvent.click(backdrop);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when close button is clicked', () => {
    render(<AIResponseModal {...defaultProps} isOpen={true} />);
    const closeButton = screen.getByRole('button', { name: /X/i }); // The X icon is inside a button
    fireEvent.click(closeButton);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('applies dark mode styles', () => {
    render(<AIResponseModal {...defaultProps} isOpen={true} isDarkMode={true} />);
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveStyle('background-color: var(--color-bg-secondary)');
  });

  it('applies light mode styles', () => {
    render(<AIResponseModal {...defaultProps} isOpen={true} isDarkMode={false} />);
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveStyle('background-color: var(--color-bg-secondary)');
  });
});
