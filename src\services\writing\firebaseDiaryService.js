import { db } from '../../config/firebaseConfig';
import { 
  collection, 
  getDocs, 
  query, 
  orderBy, 
  limit 
} from 'firebase/firestore';

/**
 * Firebase日记读取服务
 * 从Firebase的diaries集合中读取日记数据
 */

/**
 * 从Firebase获取所有日记
 * @param {number} limit - 限制数量，默认30
 * @returns {Promise<Array>} 日记数组
 */
export const getFirebaseDiaries = async (limitCount = 30) => {
  try {
    console.log('🔄 开始从Firebase获取日记数据...');
    
    const diariesRef = collection(db, 'diaries');
    const q = query(
      diariesRef,
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    const diaries = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      
      // 兼容两种数据格式：新的直接字段格式和旧的content嵌套格式
      let english, chinese, mood, weather;
      
      if (data.english && data.chinese) {
        // 新格式：直接字段
        english = data.english;
        chinese = data.chinese;
        mood = data.mood || 'neutral';
        weather = data.weather || 'unknown';
      } else if (data.content && data.content.english) {
        // 旧格式：content嵌套
        english = data.content.english;
        chinese = data.content.chinese;
        mood = data.content.mood || 'neutral';
        weather = data.content.weather || 'unknown';
      } else {
        // 默认值
        english = data.english || data.content?.english || '';
        chinese = data.chinese || data.content?.chinese || '';
        mood = data.mood || data.content?.mood || 'neutral';
        weather = data.weather || data.content?.weather || 'unknown';
      }
      
      diaries.push({
        id: doc.id,
        date: data.date,
        english: english,
        chinese: chinese,
        mood: mood,
        weather: weather,
        imageUrl: data.imageUrl || null,
        imageStatus: data.imageStatus || 'pending',
        imageGeneratedAt: data.imageGeneratedAt || null,
        timestamp: data.timestamp?.toDate?.() || new Date(data.timestamp),
        type: data.type || 'auto_generated',
        createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt)
      });
    });
    
    console.log(`✅ 从Firebase成功获取 ${diaries.length} 条日记`);
    return diaries;
    
  } catch (error) {
    console.error('❌ 从Firebase获取日记失败:', error);
    // 如果Firebase失败，返回空数组，让前端显示"无数据"状态
    return [];
  }
};

/**
 * 获取今天的日记
 * @returns {Promise<Object|null>} 今天的日记或null
 */
export const getTodayFirebaseDiary = async () => {
  try {
    const diaries = await getFirebaseDiaries(10); // 只获取最近10条
    const today = new Date().toISOString().split('T')[0];
    
    const todayDiary = diaries.find(diary => diary.date === today);
    return todayDiary || null;
    
  } catch (error) {
    console.error('获取今天日记失败:', error);
    return null;
  }
};

/**
 * 检查Firebase连接状态
 * @returns {Promise<boolean>} 是否连接正常
 */
export const checkFirebaseConnection = async () => {
  try {
    await getFirebaseDiaries(1);
    return true;
  } catch (error) {
    console.error('Firebase连接检查失败:', error);
    return false;
  }
};
