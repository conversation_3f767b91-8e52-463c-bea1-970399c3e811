import React from 'react';
import { 
  filterAndSortSuggestions, 
  createZeroLengthHighlight, 
  createHighlightElement, 
  createTextElement 
} from '../utils/textHighlighting.jsx';

/**
 * 文本高亮覆盖层组件
 * 负责渲染带高亮的文本内容
 */
const TextHighlightOverlay = ({
  text,
  suggestions,
  hoveredSuggestion,
  isDarkMode,
  onMouseEnter,
  onMouseLeave,
  onClick
}) => {
  const renderHighlightedText = () => {
    if (!text || suggestions.length === 0) {
      return text;
    }

    let result = [];
    let lastIndex = 0;

    // 获取排序后的唯一建议
    const sortedSuggestions = filterAndSortSuggestions(suggestions);

    sortedSuggestions.forEach((suggestion, index) => {
      const position = suggestion.positions[0];
      const startIndex = position.start;
      const endIndex = position.end;

      if (startIndex >= lastIndex) {
        // 添加高亮前的普通文本
        if (startIndex > lastIndex) {
          result.push(createTextElement(text, lastIndex, startIndex, index));
        }

        // 处理零长度位置（如句尾添加标点）
        if (startIndex === endIndex) {
          result.push(
            createZeroLengthHighlight(
              suggestion, 
              index, 
              isDarkMode, 
              onMouseEnter, 
              onMouseLeave
            )
          );
          lastIndex = startIndex;
        } else {
          // 添加高亮文本
          const highlightText = text.substring(startIndex, endIndex);
          const isHovered = hoveredSuggestion === suggestion.id;

          result.push(
            createHighlightElement(
              suggestion,
              highlightText,
              isHovered,
              isDarkMode,
              onMouseEnter,
              onMouseLeave
            )
          );

          lastIndex = endIndex;
        }
      }
    });

    // 添加剩余的文本
    if (lastIndex < text.length) {
      result.push(
        <span key={`text-final-${lastIndex}`}>
          {text.substring(lastIndex)}
        </span>
      );
    }

    return result;
  };

  return (
    <div
      data-testid="text-highlight-overlay"
      className="absolute inset-0 leading-relaxed z-10 rounded-2xl"
      style={{
        fontFamily: 'Georgia, "Noto Serif SC", "Times New Roman", serif',
        fontSize: '20px',
        lineHeight: '2',
        letterSpacing: '0.05em',
        color: isDarkMode ? '#E8DCC6' : '#5D4037',
        whiteSpace: 'pre-wrap',
        wordWrap: 'break-word',
        padding: '32px',
        pointerEvents: 'auto'
      }}
      onClick={onClick}
    >
      {renderHighlightedText()}
    </div>
  );
};

export default TextHighlightOverlay;
