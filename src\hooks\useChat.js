import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { ttsService } from '../services/ai/ttsService';
import { getChatResponse } from '../services/chat/chatResponseService';
import { generateNewGreeting } from '../services/chat/greetingService';
import simpleStorageService from '../services/storage/simpleStorageService';
import { generateUniqueId } from '../utils/idGenerator';
import { playAIResponseSound } from '../utils/soundUtils';

const initialMessage = {
    id: 1,
    type: 'ai',
    content: "Hey there! 👋 I'm <PERSON>, a botanist and nature photographer currently exploring the cloud forests of Costa Rica! 🌿📸 What's on your mind today?",
    translation: "你好！👋 我是Alex，一位植物学家和自然摄影师，目前正在探索哥斯达黎加的云雾森林！🌿📸 今天有什么想聊的吗？",
    timestamp: new Date()
};

export function parseAIResponse(response) {
    const responseStr = typeof response === 'string' ? response : String(response);
    const separator = '---';
    const parts = responseStr.split(separator);

    if (parts.length >= 2) {
        const englishContent = parts[0].trim();
        const chineseContent = parts[1].trim();
        return { english: englishContent, chinese: chineseContent };
    }

    return {
        english: responseStr.trim(),
        chinese: '翻译暂不可用'
    };
}

export function useChat(autoPlayTTS, sharedWritingContext, aiResponseSound = false, autoShowTranslation = true, user = null) {
    const [messages, setMessages] = useState(() => {
        try {
            const savedMessages = localStorage.getItem('current_chat_messages');
            if (savedMessages) {
                const parsed = JSON.parse(savedMessages);
                if (Array.isArray(parsed) && parsed.length > 0) {
                    return parsed.map(msg => ({ ...msg, timestamp: new Date(msg.timestamp) }));
                }
            }
        } catch (error) {
            console.error('恢复对话记录失败:', error);
        }
        return [initialMessage];
    });

    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef(null);
    const messagesRef = useRef(messages);

    useEffect(() => {
        messagesRef.current = messages;
        if (messages.length > 0) {
            localStorage.setItem('current_chat_messages', JSON.stringify(messages));
        }
    }, [messages]);

    const scrollToBottom = (behavior) => {
        messagesEndRef.current?.scrollIntoView({ behavior });
    };

    useEffect(() => {
        if (messages.length > 1) {
            scrollToBottom('smooth');
        }
    }, [messages]);

    useLayoutEffect(() => {
        scrollToBottom('auto');
    }, []);

    // 移除组件卸载时的自动保存，避免重复保存
    // useEffect(() => {
    //     return () => {
    //         if (messagesRef.current.length > 1) {
    //             simpleStorageService.saveChatSession(messagesRef.current);
    //         }
    //     };
    // }, []);

    const playTTS = async (text) => {
        if (!text || !text.trim() || !autoPlayTTS) return;
        try {
            await ttsService.speak(text, { lang: 'en-US', rate: 0.9, pitch: 1.0, volume: 1.0 });
        } catch (error) {
            console.error('TTS自动播放失败:', error);
        }
    };

    const handleSendMessage = async (inputText, images = []) => {
        if ((!inputText.trim() && images.length === 0) || isLoading) return;

        // 创建用户消息，支持文本和图片
        const userMessage = {
            id: generateUniqueId(),
            type: 'user',
            content: inputText.trim(),
            images: images.length > 0 ? images : undefined,
            timestamp: new Date()
        };
        const updatedMessages = [...messages, userMessage];
        setMessages(updatedMessages);
        setIsLoading(true);

        try {
            // 传递图片信息给聊天服务
            const aiResponse = await getChatResponse(inputText.trim(), messages, sharedWritingContext, autoShowTranslation, images, user?.uid);
            const parsed = parseAIResponse(aiResponse);
            const aiMessage = {
                id: generateUniqueId(),
                type: 'ai',
                content: parsed.english,
                translation: autoShowTranslation ? parsed.chinese : null,
                timestamp: new Date()
            };
            const finalMessages = [...updatedMessages, aiMessage];
            setMessages(finalMessages);

            // 只在有实际对话内容时保存（避免保存只有AI消息的会话）
            const hasUserMessages = finalMessages.some(msg => msg.type === 'user');
            if (hasUserMessages) {
                simpleStorageService.saveChatSession(finalMessages);
                console.log('💾 保存聊天会话，消息数量:', finalMessages.length);
            }

            playAIResponseSound(aiResponseSound);
            playTTS(parsed.english);
        } catch (error) {
            console.error('Chat error:', error);
            const errorMessage = {
                id: generateUniqueId(),
                type: 'ai',
                content: "I'm sorry, I'm having trouble connecting right now. Could you try again?",
                translation: autoShowTranslation ? "抱歉，我现在连接有点问题。你能再试一次吗？" : null,
                timestamp: new Date()
            };
            const finalMessages = [...updatedMessages, errorMessage];
            setMessages(finalMessages);

            // 错误情况下也只在有用户消息时保存
            const hasUserMessages = finalMessages.some(msg => msg.type === 'user');
            if (hasUserMessages) {
                simpleStorageService.saveChatSession(finalMessages);
                console.log('💾 保存聊天会话（错误情况），消息数量:', finalMessages.length);
            }

            playAIResponseSound(aiResponseSound);
            playTTS(errorMessage.content);
        } finally {
            setIsLoading(false);
        }
    };

    const handleNewConversation = async () => {
        if (isLoading) return;

        // 只有在有实际对话内容时才保存会话
        if (messages.length > 1) {
            // 检查是否已经有用户消息（避免只保存AI的初始消息）
            const hasUserMessages = messages.some(msg => msg.type === 'user');
            if (hasUserMessages) {
                simpleStorageService.saveChatSession(messages);
                console.log('💾 保存聊天会话，消息数量:', messages.length);
            }
        }

        // 清空当前对话状态
        localStorage.removeItem('current_chat_messages');
        localStorage.removeItem('current_chat_suggestions');
        localStorage.removeItem('shared_writing_context');

        setIsLoading(true);
        try {
            const greetingResponse = await generateNewGreeting();
            const parsedGreeting = parseAIResponse(greetingResponse);
            const newGreeting = {
                id: generateUniqueId(),
                type: 'ai',
                content: parsedGreeting.english,
                translation: autoShowTranslation ? parsedGreeting.chinese : null,
                timestamp: new Date()
            };
            setMessages([newGreeting]);
            playAIResponseSound(aiResponseSound);
            playTTS(parsedGreeting.english);
        } catch (error) {
            console.error('生成新开场白失败:', error);
            const fallbackMessage = {
                ...initialMessage,
                translation: autoShowTranslation ? initialMessage.translation : null
            };
            setMessages([fallbackMessage]); // Fallback to the static initial message
            playAIResponseSound(aiResponseSound);
            playTTS(initialMessage.content);
        } finally {
            setIsLoading(false);
        }
    };

    return { messages, setMessages, isLoading, handleSendMessage, handleNewConversation, messagesEndRef };
}
