import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../config/firebaseConfig';

/**
 * Firebase错误处理工具
 */
const handleFirebaseError = (error, operation) => {
  console.error(`Firebase ${operation} 操作失败:`, error);

  // 根据错误类型提供更友好的错误信息
  switch (error.code) {
    case 'unavailable':
      throw new Error('网络连接不可用，请检查网络连接后重试');
    case 'permission-denied':
      throw new Error('权限不足，请重新登录');
    case 'not-found':
      throw new Error('请求的数据不存在');
    case 'deadline-exceeded':
      throw new Error('请求超时，请重试');
    case 'resource-exhausted':
      throw new Error('请求过于频繁，请稍后重试');
    default:
      throw new Error(`${operation}失败: ${error.message}`);
  }
};

/**
 * 安全的Firestore操作包装器
 */
const safeFirestoreOperation = async (operation, operationName) => {
  try {
    return await operation();
  } catch (error) {
    handleFirebaseError(error, operationName);
    // handleFirebaseError 会抛出错误，这里不会执行到
    throw error;
  }
};

/**
 * 用户设置服务
 * 管理用户在Firebase Firestore中的设置数据和系统配置
 */

/**
 * 获取系统配置（包括共享的API Key）
 * @returns {Promise<Object>} 系统配置对象
 */
export const getSystemConfig = async () => {
  return await safeFirestoreOperation(async () => {
    console.log('🔧 正在获取系统配置...');
    const configDocRef = doc(db, 'systemConfig', 'main');
    const configDoc = await getDoc(configDocRef);

    if (configDoc.exists()) {
      const config = configDoc.data();
      console.log('✅ 成功获取系统配置:', Object.keys(config));
      return config;
    } else {
      console.log('📝 系统配置不存在，创建默认配置...');
      // 如果系统配置不存在，返回默认配置
      const defaultConfig = {
        doubaoApiKey: '5f480627-1927-49b3-8dc4-0e3f47a75a99', // 您提供的API Key
        // 免费版限制
        freeUserLimits: {
          chatRequestsPerDay: 10,      // 每日AI对话次数
          writingAnalysisPerDay: 5     // 每日写作纠错次数
        },
        // 付费版配置
        paidUserLimits: {
          basicPlan: {
            price: 20,                 // ¥20/月
            chatRequestsPerDay: -1,    // 无限次对话 (-1表示无限制)
            writingAnalysisPerDay: -1  // 无限次写作纠错
          }
        },
        enablePaymentSystem: false, // 支付系统开关
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 创建默认系统配置
      await setDoc(configDocRef, defaultConfig);
      console.log('✅ 默认系统配置创建成功');
      return defaultConfig;
    }
  }, '获取系统配置');
};

/**
 * 获取豆包API Key（从系统配置中）
 * @returns {Promise<string>} API Key
 */
export const getDoubaoApiKey = async () => {
  try {
    const config = await getSystemConfig();
    return config.doubaoApiKey;
  } catch (error) {
    console.error('获取API Key失败:', error);
    // 返回默认API Key作为后备
    return '5f480627-1927-49b3-8dc4-0e3f47a75a99';
  }
};

/**
 * 获取Creem API Key（从系统配置中）
 * @returns {Promise<string>} Creem API Key
 */
export const getUserCreemApiKey = async () => {
  try {
    const config = await getSystemConfig();
    return config.creemApiKey || '';
  } catch (error) {
    console.error('获取Creem API Key失败:', error);
    return '';
  }
};

/**
 * 设置Creem API Key（更新系统配置）
 * @param {string} apiKey - Creem API Key
 * @returns {Promise<void>}
 */
export const setCreemApiKey = async (apiKey) => {
  return await safeFirestoreOperation(async () => {
    console.log('🔧 正在设置Creem API Key...');

    const configRef = doc(db, 'systemConfig', 'main');
    const configSnap = await getDoc(configRef);

    if (configSnap.exists()) {
      // 更新现有配置
      await updateDoc(configRef, {
        creemApiKey: apiKey,
        updatedAt: new Date().toISOString()
      });
    } else {
      // 创建新配置
      const defaultConfig = {
        doubaoApiKey: '5f480627-1927-49b3-8dc4-0e3f47a75a99',
        creemApiKey: apiKey,
        freeUserLimits: {
          chatRequestsPerDay: 10,
          writingAnalysisPerDay: 5
        },
        premiumUserLimits: {
          chatRequestsPerDay: -1,
          writingAnalysisPerDay: -1
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await setDoc(configRef, defaultConfig);
    }

    console.log('✅ Creem API Key设置成功');
  }, '设置Creem API Key');
};

/**
 * 获取用户设置
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 用户设置对象
 */
export const getUserSettings = async (userId) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  return await safeFirestoreOperation(async () => {
    const userDocRef = doc(db, 'userSettings', userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      return userDoc.data();
    } else {
      // 如果用户设置不存在，返回默认设置
      const defaultSettings = {
        theme: 'light',
        autoPlayTTS: false,
        aiResponseSound: true,
        autoShowTranslation: true,
        autoShowSuggestion: false,
        // 分类使用量跟踪
        usageToday: {
          chatRequests: 0,        // 今日AI对话次数
          writingAnalysis: 0,     // 今日写作纠错次数
          lastRequestDate: new Date().toISOString().split('T')[0]
        },
        // 用户订阅状态
        subscription: {
          plan: 'free',           // 'free' | 'basic'
          status: 'active',       // 'active' | 'expired' | 'cancelled'
          expiresAt: null,        // 付费版到期时间
          paypalSubscriptionId: null // PayPal订阅ID
        },
        // 兼容旧版本字段（逐步废弃）
        requestsUsedToday: 0,
        lastRequestDate: new Date().toISOString().split('T')[0],
        isPremiumUser: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 创建默认设置文档
      await setDoc(userDocRef, defaultSettings);
      return defaultSettings;
    }
  }, '获取用户设置');
};

/**
 * 更新用户设置
 * @param {string} userId - 用户ID
 * @param {Object} settings - 要更新的设置
 * @returns {Promise<void>}
 */
export const updateUserSettings = async (userId, settings) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  return await safeFirestoreOperation(async () => {
    const userDocRef = doc(db, 'userSettings', userId);
    const updateData = {
      ...settings,
      updatedAt: new Date().toISOString()
    };

    await updateDoc(userDocRef, updateData);
  }, '更新用户设置');
};

/**
 * 检查用户是否可以使用特定类型的API（基于分类使用量限制）
 * @param {string} userId - 用户ID
 * @param {string} apiType - API类型: 'chat' | 'writing'
 * @returns {Promise<Object>} 包含是否可用和剩余次数的对象
 */
export const checkApiUsageLimit = async (userId, apiType = 'chat') => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  return await safeFirestoreOperation(async () => {
    const settings = await getUserSettings(userId);
    const systemConfig = await getSystemConfig();
    const today = new Date().toISOString().split('T')[0];

    // 确保使用量数据结构存在
    const usageToday = settings.usageToday || {
      chatRequests: 0,
      writingAnalysis: 0,
      lastRequestDate: today
    };

    // 如果是新的一天，重置使用次数
    if (usageToday.lastRequestDate !== today) {
      usageToday.chatRequests = 0;
      usageToday.writingAnalysis = 0;
      usageToday.lastRequestDate = today;
    }

    // 获取用户订阅状态
    const subscription = settings.subscription || { plan: 'free', status: 'active' };
    const isPaidUser = subscription.plan === 'basic' && subscription.status === 'active';

    // 获取限制配置
    const limits = isPaidUser
      ? systemConfig.paidUserLimits?.basicPlan
      : systemConfig.freeUserLimits;

    let currentUsage, maxAllowed, usageType;

    if (apiType === 'writing') {
      currentUsage = usageToday.writingAnalysis;
      maxAllowed = limits?.writingAnalysisPerDay || 5;
      usageType = 'writingAnalysis';
    } else {
      currentUsage = usageToday.chatRequests;
      maxAllowed = limits?.chatRequestsPerDay || 10;
      usageType = 'chatRequests';
    }

    // -1 表示无限制
    const isUnlimited = maxAllowed === -1;
    const remainingRequests = isUnlimited ? 999 : Math.max(0, maxAllowed - currentUsage);
    const canUse = isUnlimited || remainingRequests > 0;

    console.log('分类使用量检查:', {
      userId,
      apiType,
      currentUsage,
      maxAllowed,
      remainingRequests,
      canUse,
      isPaidUser,
      subscription: subscription.plan
    });

    return {
      canUse,
      remainingRequests,
      maxRequests: maxAllowed,
      currentUsage,
      isPaidUser,
      usageType,
      subscription
    };
  }, '检查API使用限制');
};

/**
 * 记录特定类型的API使用（增加使用次数）
 * @param {string} userId - 用户ID
 * @param {string} apiType - API类型: 'chat' | 'writing'
 * @returns {Promise<void>}
 */
export const recordApiUsage = async (userId, apiType = 'chat') => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  return await safeFirestoreOperation(async () => {
    const settings = await getUserSettings(userId);
    const today = new Date().toISOString().split('T')[0];

    // 确保使用量数据结构存在
    const usageToday = settings.usageToday || {
      chatRequests: 0,
      writingAnalysis: 0,
      lastRequestDate: today
    };

    // 如果是新的一天，重置使用次数
    if (usageToday.lastRequestDate !== today) {
      usageToday.chatRequests = 0;
      usageToday.writingAnalysis = 0;
      usageToday.lastRequestDate = today;
    }

    // 根据API类型增加相应的使用次数
    if (apiType === 'writing') {
      usageToday.writingAnalysis += 1;
    } else {
      usageToday.chatRequests += 1;
    }

    // 更新使用量数据（同时保持兼容旧版本字段）
    await updateUserSettings(userId, {
      usageToday,
      // 兼容旧版本字段
      requestsUsedToday: (settings.requestsUsedToday || 0) + 1,
      lastRequestDate: today
    });
  }, '记录API使用');
};

/**
 * 保存用户主题设置
 * @param {string} userId - 用户ID
 * @param {string} theme - 主题 ('light' 或 'dark')
 * @returns {Promise<void>}
 */
export const saveThemeSettings = async (userId, theme) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  try {
    await updateUserSettings(userId, { theme });
  } catch (error) {
    console.error('保存主题设置失败:', error);
    throw new Error('保存主题设置失败: ' + error.message);
  }
};

/**
 * 保存用户语音设置
 * @param {string} userId - 用户ID
 * @param {boolean} autoPlayTTS - 是否自动播放TTS
 * @param {boolean} aiResponseSound - 是否播放AI响应声音
 * @returns {Promise<void>}
 */
export const saveVoiceSettings = async (userId, autoPlayTTS, aiResponseSound) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  try {
    await updateUserSettings(userId, { autoPlayTTS, aiResponseSound });
  } catch (error) {
    console.error('保存语音设置失败:', error);
    throw new Error('保存语音设置失败: ' + error.message);
  }
};

/**
 * 保存用户聊天设置
 * @param {string} userId - 用户ID
 * @param {boolean} autoShowTranslation - 是否自动显示翻译
 * @param {boolean} autoShowSuggestion - 是否自动显示建议
 * @returns {Promise<void>}
 */
export const saveChatSettings = async (userId, autoShowTranslation, autoShowSuggestion) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  try {
    await updateUserSettings(userId, { autoShowTranslation, autoShowSuggestion });
  } catch (error) {
    console.error('保存聊天设置失败:', error);
    throw new Error('保存聊天设置失败: ' + error.message);
  }
};

/**
 * 初始化用户设置
 * @param {string} userId - 用户ID
 * @param {string} email - 用户邮箱
 * @returns {Promise<void>}
 */
export const initializeUserSettings = async (userId, email) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  try {
    const settings = await getUserSettings(userId);
    // 如果设置已存在，只更新邮箱
    if (settings && settings.createdAt) {
      await updateUserSettings(userId, { email });
    } else {
      // 如果设置不存在，创建新的设置
      const defaultSettings = {
        email,
        theme: 'light',
        autoPlayTTS: false,
        aiResponseSound: true,
        autoShowTranslation: true,
        autoShowSuggestion: false,
        usageToday: {
          chatRequests: 0,
          writingAnalysis: 0,
          lastRequestDate: new Date().toISOString().split('T')[0]
        },
        subscription: {
          plan: 'free',
          status: 'active',
          expiresAt: null,
          paypalSubscriptionId: null
        },
        requestsUsedToday: 0,
        lastRequestDate: new Date().toISOString().split('T')[0],
        isPremiumUser: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await updateUserSettings(userId, defaultSettings);
    }
  } catch (error) {
    console.error('初始化用户设置失败:', error);
    throw new Error('初始化用户设置失败: ' + error.message);
  }
};
