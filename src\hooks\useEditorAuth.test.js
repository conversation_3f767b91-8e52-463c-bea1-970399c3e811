import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import { useEditorAuth } from './useEditorAuth';

// Mock auth service
vi.mock('../services/auth/authService', () => ({
  onAuthChange: vi.fn(),
  logout: vi.fn()
}));

import { onAuthChange, logout } from '../services/auth/authService';

describe('useEditorAuth', () => {
  let mockUnsubscribe;

  beforeEach(() => {
    vi.clearAllMocks();
    mockUnsubscribe = vi.fn();
    onAuthChange.mockReturnValue(mockUnsubscribe);
  });

  it('should initialize with null user and closed auth modal', () => {
    const { result } = renderHook(() => useEditorAuth());

    expect(result.current.user).toBe(null);
    expect(result.current.showAuthModal).toBe(false);
    expect(typeof result.current.handleShowAuth).toBe('function');
    expect(typeof result.current.handleLogout).toBe('function');
    expect(typeof result.current.handleCloseAuthModal).toBe('function');
  });

  it('should set up auth change listener on mount', () => {
    renderHook(() => useEditorAuth());

    expect(onAuthChange).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should clean up auth listener on unmount', () => {
    const { unmount } = renderHook(() => useEditorAuth());

    unmount();

    expect(mockUnsubscribe).toHaveBeenCalled();
  });

  it('should update user state when auth changes', () => {
    const { result } = renderHook(() => useEditorAuth());
    const mockUser = { uid: '123', email: '<EMAIL>' };

    // Get the auth change callback
    const authChangeCallback = onAuthChange.mock.calls[0][0];

    act(() => {
      authChangeCallback(mockUser);
    });

    expect(result.current.user).toEqual(mockUser);
    expect(result.current.showAuthModal).toBe(false);
  });

  it('should clear user state when auth changes to null', () => {
    const { result } = renderHook(() => useEditorAuth());
    const authChangeCallback = onAuthChange.mock.calls[0][0];

    // First set a user
    act(() => {
      authChangeCallback({ uid: '123' });
    });

    expect(result.current.user).toEqual({ uid: '123' });

    // Then clear it
    act(() => {
      authChangeCallback(null);
    });

    expect(result.current.user).toBe(null);
  });

  it('should show auth modal when handleShowAuth is called', () => {
    const { result } = renderHook(() => useEditorAuth());

    act(() => {
      result.current.handleShowAuth();
    });

    expect(result.current.showAuthModal).toBe(true);
  });

  it('should close auth modal when handleCloseAuthModal is called', () => {
    const { result } = renderHook(() => useEditorAuth());

    // First show the modal
    act(() => {
      result.current.handleShowAuth();
    });

    expect(result.current.showAuthModal).toBe(true);

    // Then close it
    act(() => {
      result.current.handleCloseAuthModal();
    });

    expect(result.current.showAuthModal).toBe(false);
  });

  it('should call logout service when handleLogout is called', async () => {
    logout.mockResolvedValue();
    const { result } = renderHook(() => useEditorAuth());

    await act(async () => {
      await result.current.handleLogout();
    });

    expect(logout).toHaveBeenCalled();
  });

  it('should handle logout errors gracefully', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const error = new Error('Logout failed');
    logout.mockRejectedValue(error);
    const { result } = renderHook(() => useEditorAuth());

    await act(async () => {
      await result.current.handleLogout();
    });

    expect(logout).toHaveBeenCalled();
    expect(consoleSpy).toHaveBeenCalledWith('Logout failed:', error);

    consoleSpy.mockRestore();
  });
});
