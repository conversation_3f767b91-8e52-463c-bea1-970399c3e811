import React, { useState, useEffect } from 'react';
import { X, Trash2, Clock, Search } from 'lucide-react';
import ThemedButton from './themed/ThemedButton';
import ThemedInput from './themed/ThemedInput';
import ConfirmDialog from './ConfirmDialog';
import { useConfirmDialog } from '../hooks/useConfirmDialog';
import { useAnalysisStorage } from '../hooks/useSimpleStorage';

// 简单的Markdown渲染函数
const renderMarkdown = (text, isDarkMode = false) => {
  if (!text) return null;

  const textColor = isDarkMode ? '#E8DCC6' : '#5D4037';

  // 处理标题
  let formattedText = text.replace(/^### (.*$)/gm, `<h3 style="font-size: 1.25rem; font-weight: 600; margin-top: 1rem; margin-bottom: 0.5rem; color: ${textColor};">$1</h3>`);
  formattedText = formattedText.replace(/^## (.*$)/gm, `<h2 style="font-size: 1.5rem; font-weight: 600; margin-top: 1.5rem; margin-bottom: 0.75rem; color: ${textColor};">$1</h2>`);
  formattedText = formattedText.replace(/^# (.*$)/gm, `<h1 style="font-size: 1.75rem; font-weight: 700; margin-top: 2rem; margin-bottom: 1rem; color: ${textColor};">$1</h1>`);

  // 处理粗体和斜体
  formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  formattedText = formattedText.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 处理列表
  formattedText = formattedText.replace(/^\s*-\s+(.*$)/gm, '<li style="margin-left: 1.5rem; margin-bottom: 0.25rem;">$1</li>');

  // 处理段落
  formattedText = formattedText.replace(/\n\n/g, '<br/><br/>');

  return <div dangerouslySetInnerHTML={{ __html: formattedText }} />;
};

const HistoryModal = ({ isOpen, onClose, onSelectHistory, isDarkMode }) => {
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'detail'
  const [isSearchFocused, setIsSearchFocused] = useState(false); // 跟踪搜索框是否处于焦点状态
  const { dialogState, showConfirm, hideConfirm } = useConfirmDialog();
  
  // 使用统一存储服务
  const { 
    data: history, 
    isLoading, 
    error, 
    syncStatus, 
    fetchData, 
    deleteData, 
    clearAllData 
  } = useAnalysisStorage();

  // 加载历史记录
  useEffect(() => {
    console.log('HistoryModal useEffect 触发, isOpen:', isOpen);
    if (isOpen) {
      loadHistory();
    }
  }, [isOpen]);

  // 加载历史记录的函数
  const loadHistory = async () => {
    try {
      const records = await fetchData(50, false);
      console.log('获取到历史记录:', records.length, '条');
    } catch (error) {
      console.error('获取历史记录失败:', error);
    }
  };

  // 处理删除单条记录
  const handleDeleteRecord = async (e, recordId) => {
    e.stopPropagation();
    showConfirm({
      title: '删除历史记录',
      message: '确定要删除这条历史记录吗？',
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          await deleteData(recordId);
          // 数据已经通过Hook自动更新，不需要手动更新状态
          if (selectedRecord && selectedRecord.id === recordId) {
            setSelectedRecord(null);
            setViewMode('list');
          }
          console.log('✅ 记录已删除，ID:', recordId);
        } catch (error) {
          console.error('删除历史记录失败:', error);
        }
      }
    });
  };

  // 处理清空所有记录
  const handleClearAll = () => {
    showConfirm({
      title: '清空历史记录',
      message: '确定要清空所有历史记录吗？此操作无法撤销。',
      confirmText: '清空',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          await clearAllData();
          setSelectedRecord(null);
          setViewMode('list');
        } catch (error) {
          console.error('清空历史记录失败:', error);
        }
      }
    });
  };

  // 处理选择记录
  const handleSelectRecord = (record) => {
    setSelectedRecord(record);
    setViewMode('detail');
  };

  // 计算清空按钮的样式，避免闪烁
  const getClearButtonStyle = () => {
    return {
      backgroundColor: 'transparent',
      color: history.length === 0
        ? (isDarkMode ? '#8B7D6B' : '#D2B48C')
        : (isDarkMode ? '#D2691E' : '#B91C1C'),
      cursor: history.length === 0 ? 'not-allowed' : 'pointer',
      border: 'none',
      transition: 'none' // 禁用过渡效果，避免闪烁
    };
  };

  // 过滤历史记录
  const filteredHistory = history.filter(record =>
    record && typeof record.text === 'string' && record.text.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 格式化日期
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取文本预览
  const getTextPreview = (text, maxLength = 100) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // 处理点击遮罩层关闭弹窗
  const handleBackdropClick = (e) => {
    // 只有点击遮罩层本身时才关闭，点击弹窗内容不关闭
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // 如果弹窗未打开，则不渲染
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? 'rgba(26, 22, 17, 0.6)' : 'rgba(93, 64, 55, 0.4)' }}
      onClick={handleBackdropClick}
    >
      <div className="rounded-2xl max-w-4xl w-full mx-8 transition-colors duration-300" style={{
        backgroundColor: isDarkMode ? '#332B22' : '#FEFCF5',
        maxHeight: '80vh',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <div className="flex items-center justify-between transition-colors duration-300" style={{
          padding: '32px 32px 24px 32px',
          borderBottom: `1px solid ${isDarkMode ? '#4A3F35' : '#F0E6D2'}`
        }}>
          {viewMode === 'list' ? (
            <div className="relative flex-1 mr-4">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="搜索历史记录..."
                className="w-full rounded-xl focus:outline-none transition-all duration-200"
                style={{
                  backgroundColor: isSearchFocused
                    ? (isDarkMode ? '#3D342A' : '#FFFDF0')
                    : (isDarkMode ? '#2A241D' : '#FFFEF7'),
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  letterSpacing: '0.05em',
                  padding: '12px 16px 12px 42px', // 增加左侧内边距，为图标留出空间
                  fontSize: '16px',
                  border: '2px solid',
                  borderColor: isSearchFocused
                    ? (isDarkMode ? '#C4B59A' : '#8B4513')
                    : 'transparent',
                  boxSizing: 'border-box',
                  boxShadow: isSearchFocused
                    ? (isDarkMode ? '0 0 0 1px rgba(196, 181, 154, 0.1)' : '0 0 0 1px rgba(139, 69, 19, 0.1)')
                    : 'none',
                  outline: isSearchFocused
                    ? 'none'
                    : `1px solid ${isDarkMode ? '#4A3F35' : '#E5E7EB'}`
                }}
                onFocus={() => {
                  setIsSearchFocused(true);
                }}
                onBlur={() => {
                  setIsSearchFocused(false);
                }}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" style={{ color: isDarkMode ? '#C4B59A' : '#8B4513', marginLeft: '8px' }} />
            </div>
          ) : (
            <h3 className="text-xl font-semibold flex items-center gap-2" style={{
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.05em'
            }}>
              <Clock className="w-5 h-5" />
              历史记录详情
            </h3>
          )}
          <button
            onClick={onClose}
            className="header-btn"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {viewMode === 'list' ? (
          <>

            <div className="custom-scrollbar" style={{
              overflowY: 'auto',
              padding: '0 32px 16px 32px',
              flex: '1'
            }}>
              {filteredHistory.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12" style={{ color: isDarkMode ? '#C4B59A' : '#8B4513' }}>
                  <p>暂无历史记录</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredHistory.map(record => (
                    <div
                      key={record.id}
                      className="history-item group"
                      onClick={() => handleSelectRecord(record)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <p className="font-medium" style={{ color: isDarkMode ? '#E8DCC6' : '#5D4037' }}>
                            {getTextPreview(record.text, 80)}
                          </p>
                          <div className="flex items-center mt-2" style={{ color: isDarkMode ? '#C4B59A' : '#8B4513', fontSize: '14px' }}>
                            <Clock className="w-4 h-4 mr-1" />
                            <span>{formatDate(record.timestamp)}</span>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <button
                            onClick={(e) => handleDeleteRecord(e, record.id)}
                            className="delete-btn p-2 rounded-full"
                            title="删除记录"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div style={{
              padding: '16px 32px',
              borderTop: `1px solid ${isDarkMode ? '#4A3F35' : '#F0E6D2'}`,
              display: 'flex',
              justifyContent: 'space-between'
            }}>
              <button
                onClick={handleClearAll}
                className="clear-history-btn"
                disabled={history.length === 0}
              >
                <Trash2 className="w-5 h-5" />
                清空历史记录
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="custom-scrollbar" style={{
              overflowY: 'auto',
              padding: '16px 32px',
              flex: '1'
            }}>
              {selectedRecord && (
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-medium mb-2" style={{ color: isDarkMode ? '#E8DCC6' : '#5D4037' }}>原始文本</h4>
                    <div className="rounded-xl p-4" style={{
                      backgroundColor: isDarkMode ? '#2A241D' : '#FFFEF7',
                      border: `1px solid ${isDarkMode ? '#4A3F35' : '#F0E6D2'}`
                    }}>
                      <p style={{ color: isDarkMode ? '#E8DCC6' : '#5D4037', whiteSpace: 'pre-wrap' }}>{selectedRecord.text}</p>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-medium mb-2" style={{ color: isDarkMode ? '#E8DCC6' : '#5D4037' }}>AI分析结果</h4>
                    <div className="rounded-xl p-4" style={{
                      backgroundColor: isDarkMode ? '#2A241D' : '#FFFEF7',
                      border: `1px solid ${isDarkMode ? '#4A3F35' : '#F0E6D2'}`
                    }}>
                      <div style={{ color: isDarkMode ? '#E8DCC6' : '#5D4037' }}>
                        {renderMarkdown(selectedRecord.rawAnalysis, isDarkMode)}
                      </div>
                    </div>
                  </div>


                </div>
              )}
            </div>

            <div style={{
              padding: '16px 32px',
              borderTop: `1px solid ${isDarkMode ? '#4A3F35' : '#F0E6D2'}`,
              display: 'flex',
              justifyContent: 'flex-end' // 将按钮靠右对齐
            }}>
              <button
                onClick={() => setViewMode('list')}
                className="back-to-list-btn"
              >
                返回列表
              </button>
            </div>
          </>
        )}
      </div>

      {/* 自定义确认对话框 */}
      <ConfirmDialog
        isOpen={dialogState.isOpen}
        onClose={hideConfirm}
        onConfirm={dialogState.onConfirm}
        title={dialogState.title}
        message={dialogState.message}
        confirmText={dialogState.confirmText}
        cancelText={dialogState.cancelText}
        type={dialogState.type}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

export default HistoryModal;