/**
 * Firebase服务调试工具
 * 用于验证修复是否生效
 */

import { 
  chatHistoryService, 
  aiAnalysisService, 
  dictionarySearchService, 
  writingHistoryService, 
  diaryHistoryService 
} from '../services/history/firebaseHistoryService';
import { auth } from '../config/firebaseConfig';

/**
 * 获取当前用户ID
 */
export const getCurrentUserId = () => {
  const user = auth.currentUser;
  if (user) {
    console.log('👤 当前用户ID:', user.uid);
    console.log('📧 用户邮箱:', user.email);
    return user.uid;
  } else {
    console.log('❌ 没有登录用户');
    return null;
  }
};

/**
 * 测试Firebase服务方法
 */
export const testFirebaseServices = async (userId = null) => {
  console.log('🧪 开始测试Firebase服务方法...');
  
  try {
    // 使用真实用户ID或测试用户ID
    const testUserId = userId || 'test-user-debug';
    console.log('🔍 使用用户ID:', testUserId);
    
    // 测试所有服务方法
    const tests = [
      {
        name: 'getChatHistory',
        method: () => chatHistoryService.getChatHistory(testUserId, 1)
      },
      {
        name: 'getSearchHistory', 
        method: () => dictionarySearchService.getSearchHistory(testUserId, 1)
      },
      {
        name: 'getWritingHistory',
        method: () => writingHistoryService.getWritingHistory(testUserId, 1)
      },
      {
        name: 'getDiaryHistory',
        method: () => diaryHistoryService.getDiaryHistory(testUserId, 1)
      }
    ];
    
    for (const test of tests) {
      try {
        console.log(`🔍 测试 ${test.name}...`);
        const result = await test.method();
        console.log(`✅ ${test.name} 测试通过，返回 ${result.length} 条记录`);
      } catch (error) {
        console.error(`❌ ${test.name} 测试失败:`, error.message);
        if (error.message.includes('limit is not a function')) {
          console.error('🚨 发现 limit is not a function 错误！');
        }
      }
    }
    
    console.log('🎉 Firebase服务测试完成');
    return true;
  } catch (error) {
    console.error('❌ Firebase服务测试失败:', error);
    return false;
  }
};

/**
 * 使用当前用户ID测试Firebase服务
 */
export const testFirebaseServicesWithCurrentUser = async () => {
  const currentUserId = getCurrentUserId();
  if (currentUserId) {
    return await testFirebaseServices(currentUserId);
  } else {
    console.log('⚠️ 请先登录后再测试');
    return false;
  }
};

/**
 * 检查Firebase导入
 */
export const checkFirebaseImports = () => {
  console.log('🔍 检查Firebase导入...');
  
  try {
    // 检查Firebase函数是否正确导入
    const firebaseFunctions = [
      'collection',
      'doc', 
      'setDoc',
      'getDoc',
      'getDocs',
      'query',
      'where',
      'orderBy',
      'limit',
      'deleteDoc',
      'writeBatch',
      'serverTimestamp'
    ];
    
    // 这里我们无法直接检查导入，但可以检查服务对象
    const services = {
      chatHistoryService,
      aiAnalysisService,
      dictionarySearchService,
      writingHistoryService,
      diaryHistoryService
    };
    
    console.log('📋 服务对象检查:');
    Object.entries(services).forEach(([name, service]) => {
      if (service && typeof service === 'object') {
        console.log(`✅ ${name}: 已正确导入`);
      } else {
        console.log(`❌ ${name}: 导入失败`);
      }
    });
    
    return true;
  } catch (error) {
    console.error('❌ Firebase导入检查失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.firebaseDebug = {
    testFirebaseServices,
    testFirebaseServicesWithCurrentUser,
    getCurrentUserId,
    checkFirebaseImports
  };
  
  console.log('🔧 Firebase调试工具已加载到 window.firebaseDebug');
  console.log('💡 使用方法:');
  console.log('  - await window.firebaseDebug.testFirebaseServices() // 使用测试用户ID');
  console.log('  - await window.firebaseDebug.testFirebaseServicesWithCurrentUser() // 使用当前登录用户ID');
  console.log('  - window.firebaseDebug.getCurrentUserId() // 获取当前用户ID');
  console.log('  - window.firebaseDebug.checkFirebaseImports()');
}
