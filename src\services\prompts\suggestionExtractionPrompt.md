# Suggestion Extraction Prompt

请分析以下AI生成的英语文本分析，提取出所有具体的建议并以JSON格式返回。
每个建议应包含：原文(original)、建议修改(replacement)、解释(explanation)、类别(category)。

类别应该是以下之一：
- grammar（语法问题）
- style（风格问题）
- clarity（清晰度问题）

原始文本:
"""
{{originalText}}
"""

AI生成的分析:
"""
{{aiResponse}}
"""

请提取所有具体的建议，并以以下JSON格式返回：

```json
{
  "suggestions": [
    {
      "original": "原文中的具体文本",
      "replacement": "建议的修改文本",
      "explanation": "修改的原因和解释",
      "category": "grammar|style|clarity"
    }
  ]
}
```

注意：
1. 确保每个建议的original字段是原文中实际存在的文本
2. 确保每个建议的replacement字段是具体的修改建议，而不是描述性文本
3. 如果无法确定具体的replacement，可以将其设置为null
4. 确保返回的是有效的JSON格式
5. 不要在JSON外添加任何额外的文本或解释
