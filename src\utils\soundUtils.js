/**
 * 音效播放工具函数
 */

// 音效文件路径
const SOUND_FILES = {
  AI_RESPONSE: '/sounds/ai-response.mp3'
};

// 音频实例缓存
const audioCache = new Map();

/**
 * 预加载音效文件
 * @param {string} soundKey - 音效键名
 */
export const preloadSound = (soundKey) => {
  if (!SOUND_FILES[soundKey]) {
    console.warn(`音效文件不存在: ${soundKey}`);
    return;
  }

  if (!audioCache.has(soundKey)) {
    const audio = new Audio(SOUND_FILES[soundKey]);
    audio.preload = 'auto';
    audioCache.set(soundKey, audio);
  }
};

/**
 * 播放音效
 * @param {string} soundKey - 音效键名
 * @param {number} volume - 音量 (0-1)
 * @returns {Promise<void>}
 */
export const playSound = async (soundKey, volume = 0.5) => {
  try {
    if (!SOUND_FILES[soundKey]) {
      console.warn(`音效文件不存在: ${soundKey}`);
      return;
    }

    let audio = audioCache.get(soundKey);
    
    if (!audio) {
      audio = new Audio(SOUND_FILES[soundKey]);
      audioCache.set(soundKey, audio);
    }

    // 重置音频到开始位置
    audio.currentTime = 0;
    audio.volume = Math.max(0, Math.min(1, volume));

    // 播放音效
    await audio.play();
  } catch (error) {
    console.warn('播放音效失败:', error);
  }
};

/**
 * 播放AI回复音效
 * @param {boolean} enabled - 是否启用音效
 * @param {number} volume - 音量 (0-1)
 */
export const playAIResponseSound = (enabled = true, volume = 0.3) => {
  if (!enabled) return;
  playSound('AI_RESPONSE', volume);
};

/**
 * 预加载所有音效文件
 */
export const preloadAllSounds = () => {
  Object.keys(SOUND_FILES).forEach(soundKey => {
    preloadSound(soundKey);
  });
};

// 在模块加载时预加载音效
preloadAllSounds();
