// 简化布局测试工具
// 用于测试修复后的简化释义布局

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试简化布局效果
const testSimplifiedLayout = async () => {
  console.log('🧪 测试简化释义布局...');
  console.log('='.repeat(50));
  
  const testWords = ['water', 'doing', 'nothing', 'school'];
  const results = {};
  
  for (const word of testWords) {
    try {
      console.log(`\n📖 测试单词: ${word}`);
      console.log('-'.repeat(30));
      
      const result = await getWordDetails(word, 'ecdict');
      
      if (result && !result.notFound) {
        console.log(`✅ ${word} 查询成功`);
        console.log(`📊 数据源: ${result.source}`);
        
        if (result.definitionGroups && result.definitionGroups.length > 0) {
          console.log(`🔗 分组数量: ${result.definitionGroups.length}`);
          
          result.definitionGroups.forEach((group, groupIndex) => {
            console.log(`\n  ${groupIndex + 1}. 词性分组:`);
            console.log(`     🏷️  词性: ${group.pos} (${group.posName})`);
            console.log(`     🇺🇸 英文释义数量: ${group.english.length}`);
            console.log(`     🇨🇳 中文释义数量: ${group.chinese.length}`);
            
            // 检查是否有重复的词性标签
            const hasDuplicatePos = group.english.some(def => def.startsWith(group.pos + ' '));
            if (hasDuplicatePos) {
              console.log(`     ⚠️  英文释义中有重复的词性标签`);
            } else {
              console.log(`     ✅ 英文释义中无重复的词性标签`);
            }
            
            const hasDuplicatePosChi = group.chinese.some(def => def.startsWith(group.pos + ' '));
            if (hasDuplicatePosChi) {
              console.log(`     ⚠️  中文释义中有重复的词性标签`);
            } else {
              console.log(`     ✅ 中文释义中无重复的词性标签`);
            }
          });
          
          results[word] = {
            success: true,
            groupsCount: result.definitionGroups.length,
            hasGroups: true,
            groups: result.definitionGroups
          };
        } else {
          console.log('❌ 没有分组释义数据');
          
          results[word] = {
            success: true,
            groupsCount: 0,
            hasGroups: false
          };
        }
      } else {
        console.log(`❌ ${word} 查询失败`);
        console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
        
        results[word] = {
          success: false,
          error: result?.error || '未找到该单词'
        };
      }
    } catch (error) {
      console.log(`❌ ${word} 查询出错: ${error.message}`);
      results[word] = {
        success: false,
        error: error.message
      };
    }
  }
  
  // 统计结果
  console.log('\n📊 简化布局统计:');
  console.log('='.repeat(50));
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const groupsCount = Object.values(results).filter(r => r.hasGroups).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`✅ 成功查询: ${successCount}/${totalCount}`);
  console.log(`🔗 有分组释义: ${groupsCount}/${totalCount}`);
  
  Object.entries(results).forEach(([word, result]) => {
    if (result.success) {
      if (result.hasGroups) {
        console.log(`  ✅ ${word}: ${result.groupsCount} 个词性分组`);
      } else {
        console.log(`  ⚠️  ${word}: 无分组释义数据`);
      }
    } else {
      console.log(`  ❌ ${word}: ${result.error}`);
    }
  });
  
  return results;
};

// 测试特定单词的简化布局
const testSpecificWordSimplified = async (word) => {
  console.log(`🧪 测试单词 "${word}" 的简化布局...`);
  console.log('='.repeat(50));
  
  try {
    const result = await getWordDetails(word, 'ecdict');
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log(`📖 单词: ${result.word}`);
      console.log(`🔊 音标: ${result.phonetic || '无'}`);
      console.log(`📊 数据源: ${result.source}`);
      
      if (result.definitionGroups && result.definitionGroups.length > 0) {
        console.log(`\n🔗 简化布局预览 (${result.definitionGroups.length} 个分组):`);
        console.log('-'.repeat(50));
        console.log('释义：');
        
        result.definitionGroups.forEach((group, groupIndex) => {
          console.log(`\n[${group.pos}] ${group.posName}`);
          
          if (group.english.length > 0) {
            group.english.forEach((def, defIndex) => {
              console.log(`  ${defIndex + 1}. ${def}`);
            });
          }
          
          if (group.chinese.length > 0) {
            group.chinese.forEach((def, defIndex) => {
              console.log(`  ${defIndex + 1}. ${def}`);
            });
          }
        });
        
        console.log('\n✅ 布局特点:');
        console.log('  - 词性标签不重复');
        console.log('  - 无"英文释义"和"中文释义"标签');
        console.log('  - 布局简洁紧凑');
        console.log('  - 按词性分组清晰');
        
        return result;
      } else {
        console.log('❌ 没有分组释义数据');
        return null;
      }
    } else {
      console.log('❌ 查询失败');
      console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return null;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始简化布局测试...');
  console.log('='.repeat(60));
  
  try {
    // 测试简化布局效果
    const results = await testSimplifiedLayout();
    
    // 测试特定单词简化布局
    console.log('\n🔍 详细测试 "water" 单词简化布局:');
    await testSpecificWordSimplified('water');
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    return results;
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testSimplifiedLayout,
  testSpecificWordSimplified,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testSimplifiedLayout = {
    testSimplifiedLayout,
    testSpecificWordSimplified,
    runAllTests
  };
  
  console.log('🧪 简化布局测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testSimplifiedLayout.runAllTests() // 运行所有测试');
  console.log('  - window.testSimplifiedLayout.testSimplifiedLayout() // 测试简化布局效果');
  console.log('  - window.testSimplifiedLayout.testSpecificWordSimplified("water") // 测试特定单词简化布局');
}