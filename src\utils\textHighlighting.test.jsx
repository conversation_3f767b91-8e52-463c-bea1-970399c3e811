import { describe, it, expect } from 'vitest';
import { 
  filterAndSortSuggestions, 
  createZeroLengthHighlight, 
  createHighlightElement, 
  createTextElement 
} from './textHighlighting';

describe('textHighlighting', () => {
  describe('filterAndSortSuggestions', () => {
    it('应该过滤掉没有位置信息的建议', () => {
      const suggestions = [
        { id: '1', positions: [{ start: 0, end: 5 }] },
        { id: '2', positions: [] },
        { id: '3', positions: [{ start: 10, end: 15 }] },
        { id: '4' }
      ];

      const result = filterAndSortSuggestions(suggestions);
      
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('1');
      expect(result[1].id).toBe('3');
    });

    it('应该按位置排序建议', () => {
      const suggestions = [
        { id: '1', positions: [{ start: 20, end: 25 }] },
        { id: '2', positions: [{ start: 5, end: 10 }] },
        { id: '3', positions: [{ start: 15, end: 20 }] }
      ];

      const result = filterAndSortSuggestions(suggestions);
      
      expect(result[0].id).toBe('2');
      expect(result[1].id).toBe('3');
      expect(result[2].id).toBe('1');
    });

    it('应该去重相同位置的建议', () => {
      const suggestions = [
        { id: '1', positions: [{ start: 5, end: 10 }] },
        { id: '2', positions: [{ start: 5, end: 10 }] },
        { id: '3', positions: [{ start: 15, end: 20 }] }
      ];

      const result = filterAndSortSuggestions(suggestions);
      
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('1');
      expect(result[1].id).toBe('3');
    });

    it('应该处理空数组', () => {
      const result = filterAndSortSuggestions([]);
      expect(result).toHaveLength(0);
    });
  });

  describe('createZeroLengthHighlight', () => {
    it('应该创建零长度高亮元素', () => {
      const suggestion = { id: 'test-1' };
      const index = 0;
      const isDarkMode = false;
      const onMouseEnter = () => {};
      const onMouseLeave = () => {};

      const element = createZeroLengthHighlight(suggestion, index, isDarkMode, onMouseEnter, onMouseLeave);
      
      expect(element.type).toBe('span');
      expect(element.key).toBe('suggestion-test-1');
      expect(element.props.className).toContain('suggestion-highlight');
      expect(element.props['data-suggestion-id']).toBe('test-1');
      expect(element.props.children).toBe('⚬');
    });

    it('应该应用正确的样式', () => {
      const suggestion = { id: 'test-1' };
      const isDarkMode = true;
      const onMouseEnter = () => {};
      const onMouseLeave = () => {};

      const element = createZeroLengthHighlight(suggestion, 0, isDarkMode, onMouseEnter, onMouseLeave);
      
      expect(element.props.style.borderLeft).toBe('2px solid');
      expect(element.props.style.borderColor).toBe('#D2691E');
      expect(element.props.style.color).toBe('#E8DCC6');
      expect(element.props.style.pointerEvents).toBe('auto');
    });

    it('应该处理浅色模式样式', () => {
      const suggestion = { id: 'test-1' };
      const isDarkMode = false;
      const onMouseEnter = () => {};
      const onMouseLeave = () => {};

      const element = createZeroLengthHighlight(suggestion, 0, isDarkMode, onMouseEnter, onMouseLeave);
      
      expect(element.props.style.borderColor).toBe('#991B1B');
      expect(element.props.style.color).toBe('#5D4037');
    });
  });

  describe('createHighlightElement', () => {
    it('应该创建高亮元素', () => {
      const suggestion = { id: 'test-1' };
      const highlightText = 'test text';
      const isHovered = false;
      const isDarkMode = false;
      const onMouseEnter = () => {};
      const onMouseLeave = () => {};

      const element = createHighlightElement(suggestion, highlightText, isHovered, isDarkMode, onMouseEnter, onMouseLeave);
      
      expect(element.type).toBe('span');
      expect(element.key).toBe('test-1');
      expect(element.props.className).toContain('relative inline cursor-pointer');
      expect(element.props['data-suggestion-id']).toBe('test-1');
      expect(element.props.children).toBe('test text');
    });

    it('应该应用悬停样式', () => {
      const suggestion = { id: 'test-1' };
      const highlightText = 'test text';
      const isHovered = true;
      const isDarkMode = false;
      const onMouseEnter = () => {};
      const onMouseLeave = () => {};

      const element = createHighlightElement(suggestion, highlightText, isHovered, isDarkMode, onMouseEnter, onMouseLeave);
      
      expect(element.props.style.backgroundColor).toBe('#FEF2F2');
      expect(element.props.style.borderRadius).toBe('4px');
    });

    it('应该应用深色模式样式', () => {
      const suggestion = { id: 'test-1' };
      const highlightText = 'test text';
      const isHovered = true;
      const isDarkMode = true;
      const onMouseEnter = () => {};
      const onMouseLeave = () => {};

      const element = createHighlightElement(suggestion, highlightText, isHovered, isDarkMode, onMouseEnter, onMouseLeave);
      
      expect(element.props.style.borderBottom).toContain('#D2691E');
      expect(element.props.style.backgroundColor).toBe('rgba(210, 105, 30, 0.2)');
      expect(element.props.style.color).toBe('#E8DCC6');
    });

    it('应该应用非悬停样式', () => {
      const suggestion = { id: 'test-1' };
      const highlightText = 'test text';
      const isHovered = false;
      const isDarkMode = false;
      const onMouseEnter = () => {};
      const onMouseLeave = () => {};

      const element = createHighlightElement(suggestion, highlightText, isHovered, isDarkMode, onMouseEnter, onMouseLeave);
      
      expect(element.props.style.backgroundColor).toBe('transparent');
      expect(element.props.style.borderRadius).toBe('0');
    });
  });

  describe('createTextElement', () => {
    it('应该创建文本元素', () => {
      const text = 'Hello world';
      const startIndex = 0;
      const endIndex = 5;
      const index = 0;

      const element = createTextElement(text, startIndex, endIndex, index);
      
      expect(element.type).toBe('span');
      expect(element.key).toBe('text-0-0');
      expect(element.props.children).toBe('Hello');
    });

    it('应该正确处理文本截取', () => {
      const text = 'Hello world';
      const startIndex = 6;
      const endIndex = 11;
      const index = 1;

      const element = createTextElement(text, startIndex, endIndex, index);
      
      expect(element.props.children).toBe('world');
    });

    it('应该处理空文本', () => {
      const text = '';
      const startIndex = 0;
      const endIndex = 0;
      const index = 0;

      const element = createTextElement(text, startIndex, endIndex, index);
      
      expect(element.props.children).toBe('');
    });
  });
});
