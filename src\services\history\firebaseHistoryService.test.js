import { vi, describe, it, expect, beforeEach } from 'vitest';

const mockCommit = vi.fn();
const mockBatchDelete = vi.fn();

vi.mock('firebase/firestore', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    collection: vi.fn(),
    doc: vi.fn(),
    setDoc: vi.fn(),
    getDoc: vi.fn(),
    getDocs: vi.fn(),
    deleteDoc: vi.fn(),
    writeBatch: () => ({ 
      commit: mockCommit,
      delete: mockBatchDelete
    }),
    query: vi.fn(),
    orderBy: vi.fn(),
    limit: vi.fn(),
    serverTimestamp: vi.fn(() => 'mock-timestamp')
  };
});

// We need to import the mocked functions to be able to spy on them
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  deleteDoc,
  writeBatch,
  query,
  orderBy,
  limit,
  serverTimestamp
} from 'firebase/firestore';

import { db } from '../../config/firebaseConfig';
import {
  chatHistoryService,
  aiAnalysisService,
  dictionarySearchService,
  writingHistoryService,
  diaryHistoryService,
  migrationService
} from './firebaseHistoryService';

vi.mock('../../config/firebaseConfig', () => ({ db: {} }));

const mockUserId = 'user123';
const mockError = new Error('Firebase error');

describe('Firebase History Services', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // --- Chat History Service ---
  describe('chatHistoryService', () => {
    const mockSession = { id: 'session1', messages: [] };

    it('saveChatSession should save a session', async () => {
      await chatHistoryService.saveChatSession(mockSession, mockUserId);
      expect(setDoc).toHaveBeenCalled();
    });

    it('getChatHistory should retrieve history', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ id: 'doc1', data: () => ({}) }) });
      const history = await chatHistoryService.getChatHistory(mockUserId);
      expect(getDocs).toHaveBeenCalled();
      expect(history).toBeInstanceOf(Array);
    });

    it('deleteChatSession should delete a session', async () => {
      await chatHistoryService.deleteChatSession(mockUserId, 'session1');
      expect(deleteDoc).toHaveBeenCalled();
    });

    it('clearAllChatHistory should clear all sessions', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ ref: 'mockRef' }) });
      await chatHistoryService.clearAllChatHistory(mockUserId);
      expect(mockCommit).toHaveBeenCalled();
    });

    it('should require userId for all operations', async () => {
      await expect(chatHistoryService.saveChatSession(mockSession, null)).rejects.toThrow('用户ID是必需的');
      await expect(chatHistoryService.getChatHistory(null)).rejects.toThrow('用户ID是必需的');
      await expect(chatHistoryService.deleteChatSession(null, 'id')).rejects.toThrow('用户ID和会话ID都是必需的');
      await expect(chatHistoryService.clearAllChatHistory(null)).rejects.toThrow('用户ID是必需的');
    });
  });

  // --- AI Analysis Service ---
  describe('aiAnalysisService', () => {
    const mockAnalysis = { id: 'analysis1', text: '...' };

    it('saveAnalysis should save an analysis', async () => {
      await aiAnalysisService.saveAnalysis(mockAnalysis, mockUserId);
      expect(setDoc).toHaveBeenCalled();
    });

    it('getAnalysisHistory should retrieve history', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ id: 'doc1', data: () => ({}) }) });
      const history = await aiAnalysisService.getAnalysisHistory(mockUserId);
      expect(getDocs).toHaveBeenCalled();
      expect(history).toBeInstanceOf(Array);
    });

    it('deleteAnalysis should delete an analysis', async () => {
      await aiAnalysisService.deleteAnalysis(mockUserId, 'analysis1');
      expect(deleteDoc).toHaveBeenCalled();
    });

    it('clearAllAnalysisHistory should clear all analyses', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ ref: 'mockRef' }) });
      await aiAnalysisService.clearAllAnalysisHistory(mockUserId);
      expect(writeBatch(db).commit).toHaveBeenCalled();
    });
  });

  // --- Dictionary Search Service ---
  describe('dictionarySearchService', () => {
    it('saveSearchTerm should save a term', async () => {
      await dictionarySearchService.saveSearchTerm('word', mockUserId);
      expect(setDoc).toHaveBeenCalled();
    });

    it('getSearchHistory should retrieve history', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ id: 'doc1', data: () => ({}) }) });
      const history = await dictionarySearchService.getSearchHistory(mockUserId);
      expect(getDocs).toHaveBeenCalled();
      expect(history).toBeInstanceOf(Array);
    });

    it('deleteSearch should delete a search', async () => {
      await dictionarySearchService.deleteSearch(mockUserId, 'search1');
      expect(deleteDoc).toHaveBeenCalled();
    });

    it('clearAllSearch should clear all searches', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ ref: 'mockRef' }) });
      await dictionarySearchService.clearAllSearch(mockUserId);
      expect(writeBatch(db).commit).toHaveBeenCalled();
    });
  });

  // --- Writing History Service ---
  describe('writingHistoryService', () => {
    const mockWriting = { id: 'writing1', content: '...' };

    it('saveWriting should save a writing', async () => {
      await writingHistoryService.saveWriting(mockWriting, mockUserId);
      expect(setDoc).toHaveBeenCalled();
    });

    it('getWritingHistory should retrieve history', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ id: 'doc1', data: () => ({}) }) });
      const history = await writingHistoryService.getWritingHistory(mockUserId);
      expect(getDocs).toHaveBeenCalled();
      expect(history).toBeInstanceOf(Array);
    });

    it('deleteWriting should delete a writing', async () => {
      await writingHistoryService.deleteWriting(mockUserId, 'writing1');
      expect(deleteDoc).toHaveBeenCalled();
    });

    it('clearAllWriting should clear all writings', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ ref: 'mockRef' }) });
      await writingHistoryService.clearAllWriting(mockUserId);
      expect(writeBatch(db).commit).toHaveBeenCalled();
    });
  });

  // --- Diary History Service ---
  describe('diaryHistoryService', () => {
    const mockDiary = { id: 'diary1', content: '...' };

    it('saveDiary should save a diary entry', async () => {
      await diaryHistoryService.saveDiary(mockDiary, mockUserId);
      expect(setDoc).toHaveBeenCalled();
    });

    it('getDiaryHistory should retrieve history', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ id: 'doc1', data: () => ({}) }) });
      const history = await diaryHistoryService.getDiaryHistory(mockUserId);
      expect(getDocs).toHaveBeenCalled();
      expect(history).toBeInstanceOf(Array);
    });

    it('deleteDiary should delete a diary entry', async () => {
      await diaryHistoryService.deleteDiary(mockUserId, 'diary1');
      expect(deleteDoc).toHaveBeenCalled();
    });

    it('clearAllDiary should clear all diary entries', async () => {
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ ref: 'mockRef' }) });
      await diaryHistoryService.clearAllDiary(mockUserId);
      expect(mockCommit).toHaveBeenCalled();
    });
  });

  // --- Migration Service ---
  describe('migrationService', () => {
    beforeEach(() => {
      // Mock localStorage
      const store = {};
      global.localStorage = {
        getItem: vi.fn(key => store[key] || null),
        setItem: vi.fn((key, value) => { store[key] = value; }),
        clear: vi.fn(() => { store = {}; })
      };
    });

    it('migrateChatHistory should migrate data if not present in Firebase', async () => {
      localStorage.setItem('english_chat_history', JSON.stringify([{ id: 's1', content: 'hi' }]));
      getDocs.mockResolvedValue({ forEach: () => {}, length: 0 }); // Firebase is empty
      const count = await migrationService.migrateChatHistory(mockUserId);
      expect(setDoc).toHaveBeenCalled();
      expect(count).toBe(1);
    });

    it('migrateChatHistory should skip migration if data exists in Firebase', async () => {
      localStorage.setItem('english_chat_history', JSON.stringify([{ id: 's1', content: 'hi' }]));
      getDocs.mockResolvedValue({ forEach: (cb) => cb({ id: 'doc1', data: () => ({}) }), length: 1 }); // Firebase has data
      const count = await migrationService.migrateChatHistory(mockUserId);
      expect(setDoc).not.toHaveBeenCalled();
      expect(count).toBe(0);
    });

    it('migrateAllHistory should call individual migration functions', async () => {
      const chatSpy = vi.spyOn(migrationService, 'migrateChatHistory').mockResolvedValue(1);
      const analysisSpy = vi.spyOn(migrationService, 'migrateAnalysisHistory').mockResolvedValue(2);
      const results = await migrationService.migrateAllHistory(mockUserId);
      expect(chatSpy).toHaveBeenCalledWith(mockUserId);
      expect(analysisSpy).toHaveBeenCalledWith(mockUserId);
      expect(results.chat).toBe(1);
      expect(results.analysis).toBe(2);
    });
  });
});
;
