import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import TextHighlightOverlay from './TextHighlightOverlay';

import { vi } from 'vitest';

// Mock the utility functions
vi.mock('../utils/textHighlighting.jsx', () => ({
  filterAndSortSuggestions: vi.fn((suggestions) => suggestions.filter(s => s.positions && s.positions.length > 0)),
  createZeroLengthHighlight: vi.fn((suggestion, index, isDarkMode, onMouseEnter, onMouseLeave) => {
    return (
      <span 
        data-testid="zero-length-highlight"
        onMouseEnter={(e) => onMouseEnter(e, suggestion)}
        onMouseLeave={onMouseLeave}
      >
        ⚬
      </span>
    );
  }),
  createHighlightElement: vi.fn((suggestion, text, isHovered, isDarkMode, onMouseEnter, onMouseLeave) => {
    return (
      <span 
        data-testid="highlight-element"
        onMouseEnter={(e) => onMouseEnter(e, suggestion)}
        onMouseLeave={onMouseLeave}
      >
        highlighted text
      </span>
    );
  }),
  createTextElement: vi.fn(() => <span data-testid="text-element">normal text</span>)
}));

describe('TextHighlightOverlay', () => {
  const defaultProps = {
    text: 'Hello world',
    suggestions: [],
    hoveredSuggestion: null,
    isDarkMode: false,
    onMouseEnter: vi.fn(),
    onMouseLeave: vi.fn(),
    onClick: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without suggestions', () => {
    render(<TextHighlightOverlay {...defaultProps} />);
    
    expect(screen.getByText('Hello world')).toBeInTheDocument();
  });

  it('should render with suggestions', () => {
    const suggestions = [
      { id: '1', positions: [{ start: 0, end: 5 }] }
    ];
    
    render(<TextHighlightOverlay {...defaultProps} suggestions={suggestions} />);
    
    expect(screen.getByTestId('highlight-element')).toBeInTheDocument();
  });

  it('should call onMouseEnter when mouse enters highlight', () => {
    const onMouseEnter = vi.fn();
    const suggestions = [
      { id: '1', positions: [{ start: 0, end: 5 }] }
    ];
    
    render(
      <TextHighlightOverlay 
        {...defaultProps} 
        suggestions={suggestions}
        onMouseEnter={onMouseEnter}
      />
    );
    
    const highlightElement = screen.getByTestId('highlight-element');
    fireEvent.mouseEnter(highlightElement);
    
    expect(onMouseEnter).toHaveBeenCalled();
  });

  it('should call onMouseLeave when mouse leaves highlight', () => {
    const onMouseLeave = vi.fn();
    const suggestions = [
      { id: '1', positions: [{ start: 0, end: 5 }] }
    ];
    
    render(
      <TextHighlightOverlay 
        {...defaultProps} 
        suggestions={suggestions}
        onMouseLeave={onMouseLeave}
      />
    );
    
    const highlightElement = screen.getByTestId('highlight-element');
    fireEvent.mouseLeave(highlightElement);
    
    expect(onMouseLeave).toHaveBeenCalled();
  });

  it('should call onClick when clicked', () => {
    const onClick = vi.fn();
    
    render(<TextHighlightOverlay {...defaultProps} onClick={onClick} />);
    
    const overlay = screen.getByTestId('text-highlight-overlay');
    fireEvent.click(overlay);
    
    expect(onClick).toHaveBeenCalled();
  });

  it('should apply dark mode styles', () => {
    render(<TextHighlightOverlay {...defaultProps} isDarkMode={true} />);
    
    const overlay = screen.getByTestId('text-highlight-overlay');
    expect(overlay).toHaveStyle('color: #E8DCC6');
  });

  it('should apply light mode styles', () => {
    render(<TextHighlightOverlay {...defaultProps} isDarkMode={false} />);
    
    const overlay = screen.getByTestId('text-highlight-overlay');
    expect(overlay).toHaveStyle('color: #5D4037');
  });

  it('should handle empty text', () => {
    render(<TextHighlightOverlay {...defaultProps} text="" />);
    
    expect(screen.getByTestId('text-highlight-overlay')).toBeInTheDocument();
  });

  it('should handle null text', () => {
    render(<TextHighlightOverlay {...defaultProps} text={null} />);
    
    expect(screen.getByTestId('text-highlight-overlay')).toBeInTheDocument();
  });
});
