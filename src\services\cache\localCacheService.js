/**
 * 本地缓存同步服务
 * 提供智能缓存、增量同步和离线优先的数据管理
 */

import { 
  hybridChatHistoryService,
  hybridAnalysisService,
  hybridDictionarySearchService,
  hybridWritingHistoryService,
  hybridDiaryHistoryService,
  getStorageStatus
} from '../history/hybridHistoryService';

// 缓存配置
const CACHE_CONFIG = {
  // 缓存过期时间（毫秒）
  EXPIRY_TIMES: {
    CHAT_HISTORY: 5 * 60 * 1000,      // 5分钟
    ANALYSIS_HISTORY: 10 * 60 * 1000,  // 10分钟
    DICTIONARY_SEARCH: 30 * 60 * 1000, // 30分钟
    WRITING_HISTORY: 15 * 60 * 1000,   // 15分钟
    DIARY_HISTORY: 5 * 60 * 1000,      // 5分钟
  },
  // 最大缓存条目数
  MAX_CACHE_ITEMS: {
    CHAT_HISTORY: 100,
    ANALYSIS_HISTORY: 200,
    DICTIONARY_SEARCH: 50,
    WRITING_HISTORY: 100,
    DIARY_HISTORY: 50,
  },
  // 同步间隔（毫秒）
  SYNC_INTERVALS: {
    BACKGROUND_SYNC: 2 * 60 * 1000,    // 2分钟后台同步
    FOREGROUND_SYNC: 30 * 1000,        // 30秒前台同步
  }
};

// 缓存键名
const CACHE_KEYS = {
  CHAT_HISTORY: 'cache_chat_history',
  ANALYSIS_HISTORY: 'cache_analysis_history',
  DICTIONARY_SEARCH: 'cache_dictionary_search',
  WRITING_HISTORY: 'cache_writing_history',
  DIARY_HISTORY: 'cache_diary_history',
  LAST_SYNC: 'cache_last_sync',
  SYNC_STATUS: 'cache_sync_status'
};

/**
 * 缓存数据结构
 */
class CacheItem {
  constructor(data, timestamp = Date.now()) {
    this.data = data;
    this.timestamp = timestamp;
    this.version = 1;
  }

  isExpired(expiryTime) {
    return Date.now() - this.timestamp > expiryTime;
  }

  update(data) {
    this.data = data;
    this.timestamp = Date.now();
    this.version += 1;
  }
}

/**
 * 本地缓存服务类
 */
class LocalCacheService {
  constructor() {
    this.syncInProgress = false;
    this.syncCallbacks = new Set();
    this.backgroundSyncTimer = null;
    this.isOnline = navigator.onLine;
    
    // 监听网络状态变化
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.triggerBackgroundSync();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @param {number} expiryTime - 过期时间
   * @returns {any|null} 缓存数据或null
   */
  getCache(key, expiryTime) {
    try {
      const cached = localStorage.getItem(key);
      if (!cached) return null;

      const cacheItem = JSON.parse(cached);
      const item = new CacheItem(cacheItem.data, cacheItem.timestamp);
      
      if (item.isExpired(expiryTime)) {
        localStorage.removeItem(key);
        return null;
      }

      return item.data;
    } catch (error) {
      console.error(`获取缓存失败 (${key}):`, error);
      return null;
    }
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} data - 要缓存的数据
   */
  setCache(key, data) {
    try {
      const cacheItem = new CacheItem(data);
      localStorage.setItem(key, JSON.stringify(cacheItem));
    } catch (error) {
      console.error(`设置缓存失败 (${key}):`, error);
    }
  }

  /**
   * 清除指定缓存
   * @param {string} key - 缓存键
   */
  clearCache(key) {
    localStorage.removeItem(key);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }

  /**
   * 获取聊天历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 聊天历史
   */
  async getChatHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.CHAT_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.CHAT_HISTORY;

    // 尝试从缓存获取
    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取聊天历史');
        return cached.slice(0, limit);
      }
    }

    // 从远程获取
    console.log('🌐 从远程获取聊天历史');
    try {
      const data = await hybridChatHistoryService.getChatHistory(limit);
      this.setCache(cacheKey, data);
      this.updateLastSync('chat_history');
      return data;
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      // 尝试返回过期缓存
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 获取AI分析历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 分析历史
   */
  async getAnalysisHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.ANALYSIS_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.ANALYSIS_HISTORY;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取分析历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从远程获取分析历史');
    try {
      const data = await hybridAnalysisService.getAnalysisHistory(limit);
      this.setCache(cacheKey, data);
      this.updateLastSync('analysis_history');
      return data;
    } catch (error) {
      console.error('获取分析历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 获取字典搜索历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 搜索历史
   */
  async getDictionarySearchHistory(limit = 20, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.DICTIONARY_SEARCH;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.DICTIONARY_SEARCH;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取搜索历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从远程获取搜索历史');
    try {
      const data = await hybridDictionarySearchService.getSearchHistory(limit);
      this.setCache(cacheKey, data);
      this.updateLastSync('dictionary_search');
      return data;
    } catch (error) {
      console.error('获取搜索历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 获取写作历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 写作历史
   */
  async getWritingHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.WRITING_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.WRITING_HISTORY;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取写作历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从远程获取写作历史');
    try {
      const data = await hybridWritingHistoryService.getWritingHistory(limit);
      this.setCache(cacheKey, data);
      this.updateLastSync('writing_history');
      return data;
    } catch (error) {
      console.error('获取写作历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 获取日记历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 日记历史
   */
  async getDiaryHistory(limit = 30, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.DIARY_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.DIARY_HISTORY;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取日记历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从远程获取日记历史');
    try {
      const data = await hybridDiaryHistoryService.getDiaryHistory(limit);
      this.setCache(cacheKey, data);
      this.updateLastSync('diary_history');
      return data;
    } catch (error) {
      console.error('获取日记历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 保存数据并更新缓存
   * @param {string} type - 数据类型
   * @param {any} data - 要保存的数据
   * @returns {Promise<any>} 保存结果
   */
  async saveData(type, data) {
    let result;
    
    try {
      switch (type) {
        case 'chat':
          result = await hybridChatHistoryService.saveChatSession(data);
          break;
        case 'analysis':
          result = await hybridAnalysisService.saveAnalysis(data);
          break;
        case 'dictionary':
          result = await hybridDictionarySearchService.saveSearchTerm(data);
          break;
        case 'writing':
          result = await hybridWritingHistoryService.saveWriting(data);
          break;
        case 'diary':
          result = await hybridDiaryHistoryService.saveDiary(data);
          break;
        default:
          throw new Error(`未知的数据类型: ${type}`);
      }

      // 保存成功后，标记缓存为过期，下次获取时会重新加载
      this.invalidateCache(type);
      return result;
    } catch (error) {
      console.error(`保存${type}数据失败:`, error);
      throw error;
    }
  }

  /**
   * 使指定类型的缓存失效
   * @param {string} type - 数据类型
   */
  invalidateCache(type) {
    const cacheKeyMap = {
      chat: CACHE_KEYS.CHAT_HISTORY,
      analysis: CACHE_KEYS.ANALYSIS_HISTORY,
      dictionary: CACHE_KEYS.DICTIONARY_SEARCH,
      writing: CACHE_KEYS.WRITING_HISTORY,
      diary: CACHE_KEYS.DIARY_HISTORY
    };

    const cacheKey = cacheKeyMap[type];
    if (cacheKey) {
      this.clearCache(cacheKey);
      console.log(`🗑️ 已清除${type}缓存`);
    }
  }

  /**
   * 更新最后同步时间
   * @param {string} type - 数据类型
   */
  updateLastSync(type) {
    const lastSync = this.getCache(CACHE_KEYS.LAST_SYNC, Infinity) || {};
    lastSync[type] = Date.now();
    this.setCache(CACHE_KEYS.LAST_SYNC, lastSync);
  }

  /**
   * 获取最后同步时间
   * @param {string} type - 数据类型
   * @returns {number|null} 最后同步时间戳
   */
  getLastSync(type) {
    const lastSync = this.getCache(CACHE_KEYS.LAST_SYNC, Infinity) || {};
    return lastSync[type] || null;
  }

  /**
   * 触发后台同步
   */
  async triggerBackgroundSync() {
    if (!this.isOnline || this.syncInProgress) {
      return;
    }

    console.log('🔄 开始后台同步...');
    this.syncInProgress = true;

    try {
      const status = getStorageStatus();
      if (!status.canUseFirebase) {
        console.log('⚠️ 无法使用Firebase，跳过同步');
        return;
      }

      // 并行同步所有数据类型
      const syncPromises = [
        this.getChatHistory(50, true).catch(e => console.error('同步聊天历史失败:', e)),
        this.getAnalysisHistory(50, true).catch(e => console.error('同步分析历史失败:', e)),
        this.getDictionarySearchHistory(20, true).catch(e => console.error('同步搜索历史失败:', e)),
        this.getWritingHistory(50, true).catch(e => console.error('同步写作历史失败:', e)),
        this.getDiaryHistory(30, true).catch(e => console.error('同步日记历史失败:', e))
      ];

      await Promise.allSettled(syncPromises);
      console.log('✅ 后台同步完成');

      // 通知所有监听器
      this.syncCallbacks.forEach(callback => callback('completed'));
    } catch (error) {
      console.error('❌ 后台同步失败:', error);
      this.syncCallbacks.forEach(callback => callback('failed'));
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 启动后台同步定时器
   */
  startBackgroundSync() {
    if (this.backgroundSyncTimer) {
      clearInterval(this.backgroundSyncTimer);
    }

    this.backgroundSyncTimer = setInterval(() => {
      this.triggerBackgroundSync();
    }, CACHE_CONFIG.SYNC_INTERVALS.BACKGROUND_SYNC);

    console.log('🚀 后台同步定时器已启动');
  }

  /**
   * 停止后台同步定时器
   */
  stopBackgroundSync() {
    if (this.backgroundSyncTimer) {
      clearInterval(this.backgroundSyncTimer);
      this.backgroundSyncTimer = null;
      console.log('⏹️ 后台同步定时器已停止');
    }
  }

  /**
   * 添加同步状态监听器
   * @param {Function} callback - 回调函数
   */
  addSyncListener(callback) {
    this.syncCallbacks.add(callback);
  }

  /**
   * 移除同步状态监听器
   * @param {Function} callback - 回调函数
   */
  removeSyncListener(callback) {
    this.syncCallbacks.delete(callback);
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    const stats = {
      totalSize: 0,
      itemCount: 0,
      lastSync: this.getCache(CACHE_KEYS.LAST_SYNC, Infinity) || {},
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress
    };

    Object.values(CACHE_KEYS).forEach(key => {
      const data = localStorage.getItem(key);
      if (data) {
        stats.totalSize += data.length;
        stats.itemCount += 1;
      }
    });

    return stats;
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    Object.entries(CACHE_CONFIG.EXPIRY_TIMES).forEach(([type, expiryTime]) => {
      const cacheKeyMap = {
        CHAT_HISTORY: CACHE_KEYS.CHAT_HISTORY,
        ANALYSIS_HISTORY: CACHE_KEYS.ANALYSIS_HISTORY,
        DICTIONARY_SEARCH: CACHE_KEYS.DICTIONARY_SEARCH,
        WRITING_HISTORY: CACHE_KEYS.WRITING_HISTORY,
        DIARY_HISTORY: CACHE_KEYS.DIARY_HISTORY
      };

      const cacheKey = cacheKeyMap[type];
      if (cacheKey) {
        const cached = this.getCache(cacheKey, expiryTime);
        if (!cached) {
          console.log(`🧹 清理过期缓存: ${type}`);
        }
      }
    });
  }
}

// 创建单例实例
const localCacheService = new LocalCacheService();

export default localCacheService;
export { CACHE_CONFIG, CACHE_KEYS };
