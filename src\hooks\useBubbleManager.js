import { useRef, useCallback } from 'react';

/**
 * 气泡管理器 Hook
 * 用于管理建议气泡的显示、隐藏和超时控制
 */
export const useBubbleManager = () => {
  const timeoutRef = useRef(null);
  const activeSuggestionRef = useRef(null);

  const clearTimeout = useCallback(() => {
    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const setTimeout = useCallback((callback, delay) => {
    clearTimeout();
    timeoutRef.current = window.setTimeout(callback, delay);
  }, [clearTimeout]);

  const setActiveSuggestion = useCallback((id) => {
    activeSuggestionRef.current = id;
  }, []);

  const getActiveSuggestion = useCallback(() => {
    return activeSuggestionRef.current;
  }, []);

  const clearActiveSuggestion = useCallback(() => {
    activeSuggestionRef.current = null;
  }, []);

  return {
    clearTimeout,
    setTimeout,
    setActiveSuggestion,
    getActiveSuggestion,
    clearActiveSuggestion
  };
};
