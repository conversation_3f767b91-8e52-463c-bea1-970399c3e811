/**
 * 测试Firebase数据清理功能
 */

import simpleStorageService from '../services/storage/simpleStorageService';

// 测试数据清理功能
export const testDataCleaning = () => {
  console.log('🧹 测试数据清理功能...');
  
  // 测试包含undefined值的数据
  const testDataWithUndefined = {
    id: 'test_123',
    message: 'Hello',
    messageCount: undefined, // 这个字段会导致Firebase错误
    messages: [
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: 'Hi', timestamp: undefined }, // 嵌套undefined
      undefined // 数组中的undefined
    ],
    metadata: {
      score: 85,
      rating: undefined, // 嵌套对象中的undefined
      tags: ['test', undefined, 'firebase'] // 数组中的undefined
    },
    timestamp: new Date().toISOString(),
    userId: 'test_user'
  };
  
  console.log('📄 原始数据:', testDataWithUndefined);
  
  // 清理数据
  const cleanedData = simpleStorageService.cleanDataForFirebase(testDataWithUndefined);
  console.log('✨ 清理后数据:', cleanedData);
  
  // 验证清理结果
  const hasUndefined = JSON.stringify(cleanedData).includes('undefined');
  console.log('🔍 是否还有undefined值:', hasUndefined);
  
  if (!hasUndefined) {
    console.log('✅ 数据清理成功！');
    return true;
  } else {
    console.log('❌ 数据清理失败，仍有undefined值');
    return false;
  }
};

// 测试Firebase同步（使用清理后的数据）
export const testFirebaseSyncWithCleaning = async () => {
  console.log('🔄 测试Firebase同步（使用数据清理）...');
  
  const testUserId = 'cleaning_test_' + Date.now();
  simpleStorageService.init(testUserId);
  
  // 创建包含undefined值的数据
  const testChat = {
    id: 'cleaning_chat_' + Date.now(),
    messages: [
      { role: 'user', content: 'Test message' },
      { role: 'assistant', content: 'Test response' }
    ],
    sessionTitle: 'Cleaning Test Chat',
    messageCount: undefined, // 故意添加undefined值
    timestamp: new Date().toISOString(),
    userId: testUserId
  };
  
  console.log('💾 保存包含undefined值的数据...');
  const savedChat = simpleStorageService.saveChatSession(testChat);
  console.log('✅ 本地保存成功:', savedChat.id);
  
  // 手动同步到Firebase
  console.log('🔄 同步到Firebase...');
  try {
    await simpleStorageService.manualSync();
    console.log('✅ Firebase同步成功！');
    return true;
  } catch (error) {
    console.error('❌ Firebase同步失败:', error);
    return false;
  }
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testDataCleaning = testDataCleaning;
  window.testFirebaseSyncWithCleaning = testFirebaseSyncWithCleaning;
}
