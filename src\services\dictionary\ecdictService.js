// ECDICT 词典服务
// 基于 ECDICT 开源英汉词典数据库的本地查询服务

// 词典数据存储
let dictionaryData = null;
let isInitialized = false;

// 初始化 ECDICT 服务
export const initEcdict = async () => {
  if (isInitialized) return true;
  
  try {
    console.log('正在初始化 ECDICT 服务...');
    
    // 加载词典数据
    const response = await fetch('/data/dictionary/ecdict.csv');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const csvText = await response.text();
    dictionaryData = parseCsvData(csvText);
    
    isInitialized = true;
    console.log(`ECDICT 服务初始化成功，加载了 ${dictionaryData.size} 个词条`);
    return true;
  } catch (error) {
    console.error('ECDICT 服务初始化失败:', error);
    return false;
  }
};

// 解析 CSV 数据
const parseCsvData = (csvText) => {
  const lines = csvText.split('\n');
  const data = new Map();
  let successCount = 0;
  let errorCount = 0;
  
  console.log(`开始解析 CSV 数据，共 ${lines.length} 行`);
  
  // 跳过标题行
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    try {
      const entry = parseCsvLine(line);
      if (entry && entry.word) {
        data.set(entry.word.toLowerCase(), entry);
        successCount++;
        
        // 记录一些常见单词的解析
        if (['water', 'hello', 'world', 'beautiful', 'curious', 'nothing'].includes(entry.word.toLowerCase())) {
          console.log(`成功解析单词: ${entry.word}`, {
            translation: entry.translation,
            definition: entry.definition
          });
        }
      }
    } catch (error) {
      errorCount++;
      if (errorCount < 10) { // 只记录前10个错误
        console.warn(`解析 CSV 行失败 (${i}): ${line.substring(0, 100)}...`, error);
      }
    }
  }
  
  console.log(`CSV 解析完成: 成功 ${successCount} 条，失败 ${errorCount} 条`);
  return data;
};

// 解析单行 CSV 数据
const parseCsvLine = (line) => {
  const fields = [];
  let current = '';
  let inQuotes = false;
  let i = 0;
  
  while (i < line.length) {
    const char = line[i];
    
    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // 处理转义的引号
        current += '"';
        i += 2;
        continue;
      }
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      fields.push(current.trim());
      current = '';
    } else {
      current += char;
    }
    i++;
  }
  
  fields.push(current.trim());
  
  if (fields.length < 13) {
    return null;
  }
  
  return {
    word: fields[0].replace(/^'|'$/g, ''), // 移除单引号
    phonetic: fields[1] || '',
    definition: fields[2] || '',
    translation: fields[3] || '',
    pos: fields[4] || '',
    collins: fields[5] || '',
    oxford: fields[6] || '',
    tag: fields[7] || '',
    bnc: fields[8] || '',
    frq: fields[9] || '',
    exchange: fields[10] || '',
    detail: fields[11] || '',
    audio: fields[12] || ''
  };
};

// 查询单词详情
export const getWordDetails = async (word) => {
  if (!isInitialized) {
    const initialized = await initEcdict();
    if (!initialized) {
      return {
        word: word,
        notFound: true,
        error: 'ECDICT 服务初始化失败'
      };
    }
  }
  
  const normalizedWord = word.toLowerCase().trim();
  const entry = dictionaryData.get(normalizedWord);
  
  if (!entry) {
    return {
      word: word,
      notFound: true,
      error: '未找到该单词'
    };
  }
  
  // 解析词性信息
  const posInfo = parsePosInfo(entry.pos);
  
  // 解析词形变化
  const exchangeInfo = parseExchangeInfo(entry.exchange);
  
  // 将 ECDICT 数据转换为 DictionaryLookup 组件期望的格式
  const meanings = [];
  
  // 解析英文释义和中文翻译，按词性分组
  const parsedMeanings = parseDefinitionsByPos(entry.definition, entry.translation);
  
  // 调试信息
  if (['curious', 'water', 'hello', 'nothing'].includes(entry.word.toLowerCase())) {
    console.log(`解析单词 ${entry.word} 的词性分组:`, parsedMeanings);
  }
  
  if (parsedMeanings.length > 0) {
    meanings.push(...parsedMeanings);
  } else {
    // 如果没有解析到词性信息，创建一个默认的 meaning
    meanings.push({
      partOfSpeech: '',
      definitions: [{
        definition: entry.definition || '',
        translation: entry.translation || '',
        example: '',
        synonyms: [],
        antonyms: []
      }]
    });
  }

  return {
    word: entry.word,
    phonetic: entry.phonetic,
    phonetics: entry.phonetic ? [{
      text: entry.phonetic,
      audio: entry.audio || ''
    }] : [],
    meanings: meanings,
    translation: entry.translation,
    pos: posInfo,
    collins: entry.collins ? parseInt(entry.collins) : null,
    oxford: entry.oxford === '1',
    tag: entry.tag ? entry.tag.split(' ') : [],
    bnc: entry.bnc ? parseInt(entry.bnc) : null,
    frq: entry.frq ? parseInt(entry.frq) : null,
    exchange: exchangeInfo,
    detail: entry.detail,
    audio: entry.audio,
    source: 'ECDICT',
    timestamp: new Date().toISOString()
  };
};

// 解析释义和翻译，按词性分组
const parseDefinitionsByPos = (definition, translation) => {
  const meanings = [];
  
  if (!definition && !translation) return meanings;
  
  // 词性映射
  const posMap = {
    'n': '名词',
    'v': '动词', 
    'adj': '形容词',
    'adv': '副词',
    'prep': '介词',
    'conj': '连词',
    'pron': '代词',
    'det': '限定词',
    'int': '感叹词',
    'aux': '助动词',
    'mod': '情态动词',
    'a': '形容词', // 中文翻译中常用
    's': '名词',   // 中文翻译中常用
    'vt': '及物动词',
    'vi': '不及物动词',
    'na': '名词/形容词',
    'ns': '名词复数',
    'r': '副词',   // ECDICT中r表示副词
    'interj': '感叹词' // 完整形式
  };
  
  // 解析英文释义
  const englishDefinitions = parseDefinitionText(definition);
  // 解析中文翻译
  const chineseTranslations = parseDefinitionText(translation);
  
  // 按词性分组
  const groupedByPos = {};
  
  // 处理英文释义
  englishDefinitions.forEach(def => {
    const pos = def.pos || 'unknown';
    if (!groupedByPos[pos]) {
      groupedByPos[pos] = {
        partOfSpeech: posMap[pos] || pos,
        definitions: []
      };
    }
    // 只有当定义内容不为空时才添加
    if (def.text && def.text.trim()) {
      groupedByPos[pos].definitions.push({
        definition: def.text,
        translation: '',
        example: '',
        synonyms: [],
        antonyms: []
      });
    }
  });
  
  // 处理中文翻译
  chineseTranslations.forEach(trans => {
    const pos = trans.pos || 'unknown';
    if (!groupedByPos[pos]) {
      groupedByPos[pos] = {
        partOfSpeech: posMap[pos] || pos,
        definitions: []
      };
    }
    
    // 查找对应的英文释义
    let existingDef = groupedByPos[pos].definitions.find(d => !d.translation);
    
    // 如果没有找到完全匹配的词性，尝试匹配相近的词性
    if (!existingDef) {
      // 特殊处理：r (副词) 与 adv (副词) 的匹配
      if (pos === 'adv' && groupedByPos['r']) {
        existingDef = groupedByPos['r'].definitions.find(d => !d.translation);
        if (existingDef) {
          // 将r组的词性更新为adv
          groupedByPos['r'].partOfSpeech = posMap['adv'];
          groupedByPos['adv'] = groupedByPos['r'];
          delete groupedByPos['r'];
        }
      }
    }
    
    if (existingDef) {
      existingDef.translation = trans.text;
    } else {
      // 只有当翻译内容不为空时才添加
      if (trans.text && trans.text.trim()) {
        groupedByPos[pos].definitions.push({
          definition: '',
          translation: trans.text,
          example: '',
          synonyms: [],
          antonyms: []
        });
      }
    }
  });
  
  // 转换为数组，过滤掉空的定义
  Object.values(groupedByPos).forEach(meaning => {
    // 过滤掉空的定义
    const validDefinitions = meaning.definitions.filter(def => 
      (def.definition && def.definition.trim()) || (def.translation && def.translation.trim())
    );
    
    if (validDefinitions.length > 0) {
      // 直接使用有效的定义，不需要额外处理
      const processedDefinitions = validDefinitions;
      
      meanings.push({
        ...meaning,
        definitions: processedDefinitions
      });
    }
  });
  
  return meanings;
};

// 解析定义文本，提取词性和内容
const parseDefinitionText = (text) => {
  if (!text) return [];
  
  const definitions = [];
  
  // 先处理转义的换行符
  const processedText = text.replace(/\\n/g, '\n');
  const lines = processedText.split('\n');
  
  for (const line of lines) {
    const trimmed = line.trim();
    if (!trimmed) continue;
    
    // 匹配词性标记，如 "n.", "v.", "adj.", "a.", "s." 等
    const posMatch = trimmed.match(/^([a-z]+(?:[a-z]*))\.\s*(.*)$/);
    if (posMatch) {
      const pos = posMatch[1];
      const content = posMatch[2];
      if (content) {
        definitions.push({
          pos: pos,
          text: content
        });
      }
    } else {
      // 如果没有词性标记，作为通用内容
      definitions.push({
        pos: 'unknown',
        text: trimmed
      });
    }
  }
  
  return definitions;
};

// 解析词性信息
const parsePosInfo = (posString) => {
  if (!posString) return [];
  
  const posMap = {
    'n': '名词',
    'v': '动词',
    'adj': '形容词',
    'adv': '副词',
    'prep': '介词',
    'conj': '连词',
    'pron': '代词',
    'det': '限定词',
    'int': '感叹词',
    'aux': '助动词',
    'mod': '情态动词'
  };
  
  const posList = [];
  const parts = posString.split('/');
  
  for (const part of parts) {
    const [pos, freq] = part.split(':');
    if (pos && posMap[pos]) {
      posList.push({
        pos: posMap[pos],
        frequency: freq ? parseInt(freq) : null
      });
    }
  }
  
  return posList;
};

// 解析词形变化信息
const parseExchangeInfo = (exchangeString) => {
  if (!exchangeString) return {};
  
  const exchangeMap = {
    'p': '过去式',
    'd': '过去分词',
    'i': '现在分词',
    '3': '第三人称单数',
    'r': '比较级',
    't': '最高级',
    's': '复数',
    '0': '原形'
  };
  
  const exchange = {};
  const parts = exchangeString.split('/');
  
  for (const part of parts) {
    const [type, form] = part.split(':');
    if (type && form && exchangeMap[type]) {
      exchange[exchangeMap[type]] = form;
    }
  }
  
  return exchange;
};

// 模糊搜索
export const searchWords = async (query, options = {}) => {
  if (!isInitialized) {
    const initialized = await initEcdict();
    if (!initialized) {
      return [];
    }
  }
  
  const { limit = 10, fuzzy = true } = options;
  const normalizedQuery = query.toLowerCase().trim();
  const results = [];
  
  for (const [word, entry] of dictionaryData) {
    if (fuzzy ? word.includes(normalizedQuery) : word.startsWith(normalizedQuery)) {
      results.push({
        word: entry.word,
        phonetic: entry.phonetic,
        translation: entry.translation,
        pos: entry.pos
      });
      
      if (results.length >= limit) break;
    }
  }
  
  return results;
};

// 获取词典统计信息
export const getDictionaryStats = () => {
  return {
    totalWords: dictionaryData ? dictionaryData.size : 0,
    initialized: isInitialized,
    source: 'ECDICT'
  };
};

// 检查服务状态
export const checkServiceStatus = () => {
  return {
    initialized: isInitialized,
    totalWords: dictionaryData ? dictionaryData.size : 0,
    source: 'ECDICT'
  };
};
