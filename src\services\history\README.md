# Firebase历史记录服务使用指南

## 概述

本服务将应用的历史记录功能从本地存储迁移到Firebase，实现跨设备同步和更好的数据持久化。

## 服务架构

### 1. Firebase历史记录服务 (`firebaseHistoryService.js`)
- 纯Firebase实现，直接与Firestore交互
- 支持所有历史记录类型：聊天、AI分析、字典搜索、写作、日记
- 包含数据迁移工具

### 2. 混合存储服务 (`hybridHistoryService.js`)
- 自动在本地存储和Firebase之间切换
- 支持离线模式和在线同步
- 向后兼容现有代码

## 使用方法

### 初始化服务

```javascript
import { initializeHybridStorage, STORAGE_MODE } from './services/history/hybridHistoryService';

// 初始化本地存储模式
initializeHybridStorage(STORAGE_MODE.LOCAL);

// 初始化Firebase模式（需要用户ID）
initializeHybridStorage(STORAGE_MODE.FIREBASE, 'user123');

// 初始化混合模式
initializeHybridStorage(STORAGE_MODE.HYBRID, 'user123');
```

### 使用聊天历史服务

```javascript
import { hybridChatHistoryService } from './services/history/hybridHistoryService';

// 保存聊天会话
await hybridChatHistoryService.saveChatSession({
  id: 'session123',
  title: '英语对话',
  messages: [...],
  timestamp: new Date().toISOString()
});

// 获取聊天历史
const history = await hybridChatHistoryService.getChatHistory(50);

// 删除会话
await hybridChatHistoryService.deleteChatSession('session123');

// 清空所有历史
await hybridChatHistoryService.clearAllChatHistory();
```

### 使用AI分析历史服务

```javascript
import { hybridAnalysisService } from './services/history/hybridHistoryService';

// 保存AI分析记录
await hybridAnalysisService.saveAnalysis({
  text: '用户输入的文本',
  rawAnalysis: 'AI原始分析结果',
  analysis: '结构化分析结果'
});

// 获取分析历史
const history = await hybridAnalysisService.getAnalysisHistory(50);

// 删除记录
await hybridAnalysisService.deleteAnalysis('analysis123');

// 清空所有历史
await hybridAnalysisService.clearAllAnalysisHistory();
```

### 使用字典搜索历史服务

```javascript
import { hybridDictionarySearchService } from './services/history/hybridHistoryService';

// 保存搜索词
await hybridDictionarySearchService.saveSearchTerm('hello');

// 获取搜索历史
const history = await hybridDictionarySearchService.getSearchHistory(20);

// 清空搜索历史
await hybridDictionarySearchService.clearSearchHistory();
```

### 使用写作历史服务

```javascript
import { hybridWritingHistoryService } from './services/history/hybridHistoryService';

// 保存写作记录
await hybridWritingHistoryService.saveWriting({
  content: '写作内容',
  type: 'essay',
  language: 'en'
});

// 获取写作历史
const history = await hybridWritingHistoryService.getWritingHistory(50);
```

### 使用日记历史服务

```javascript
import { hybridDiaryHistoryService } from './services/history/hybridHistoryService';

// 保存日记
await hybridDiaryHistoryService.saveDiary({
  date: '2024-01-01',
  content: '今天的日记内容',
  mood: 'happy'
});

// 获取日记历史
const history = await hybridDiaryHistoryService.getDiaryHistory(30);
```

## 数据迁移

### 自动迁移
当用户登录并切换到Firebase模式时，系统会自动迁移本地数据：

```javascript
// 切换到Firebase模式时会自动触发迁移
setStorageMode(STORAGE_MODE.FIREBASE, userId);
```

### 手动迁移
```javascript
import { migrationService } from './services/history/firebaseHistoryService';

// 迁移所有历史记录
const results = await migrationService.migrateAllHistory(userId);
console.log('迁移结果:', results);

// 迁移特定类型的历史记录
const chatCount = await migrationService.migrateChatHistory(userId);
const analysisCount = await migrationService.migrateAnalysisHistory(userId);
```

## 存储模式

### LOCAL (本地存储)
- 使用localStorage
- 离线可用
- 数据仅限当前设备

### FIREBASE (Firebase存储)
- 使用Firestore
- 需要网络连接
- 支持跨设备同步
- 自动数据迁移

### HYBRID (混合模式)
- 优先使用Firebase
- 失败时回退到本地存储
- 最佳的用户体验

## 错误处理

服务包含完善的错误处理机制：

1. **网络错误**：自动回退到本地存储
2. **权限错误**：提示用户重新登录
3. **数据格式错误**：跳过错误数据，继续处理其他数据

## 性能优化

- 使用批量操作减少网络请求
- 实现数据分页加载
- 支持离线缓存
- 智能数据同步

## 安全考虑

- 用户数据隔离（每个用户只能访问自己的数据）
- 数据验证和清理
- 防止SQL注入（Firestore自动处理）

## 监控和调试

```javascript
import { getStorageStatus } from './services/history/hybridHistoryService';

// 获取存储状态
const status = getStorageStatus();
console.log('存储状态:', status);
// 输出: { mode: 'firebase', userId: 'user123', canUseFirebase: true, isOnline: true }
```

## 迁移步骤

### 1. 更新现有组件
将现有的历史记录调用替换为混合服务：

```javascript
// 旧代码
import { getAnalysisHistory } from '../writing/historyService';
const history = getAnalysisHistory();

// 新代码
import { hybridAnalysisService } from './services/history/hybridHistoryService';
const history = await hybridAnalysisService.getAnalysisHistory();
```

### 2. 初始化服务
在应用启动时初始化混合存储服务：

```javascript
// App.jsx 或主组件
useEffect(() => {
  if (user) {
    initializeHybridStorage(STORAGE_MODE.FIREBASE, user.uid);
  } else {
    initializeHybridStorage(STORAGE_MODE.LOCAL);
  }
}, [user]);
```

### 3. 测试功能
- 测试在线模式下的数据同步
- 测试离线模式下的本地存储
- 验证数据迁移功能

## 注意事项

1. **异步操作**：所有Firebase操作都是异步的，需要使用async/await
2. **用户认证**：Firebase模式需要用户登录
3. **网络状态**：考虑网络连接状态对用户体验的影响
4. **数据一致性**：确保本地和远程数据的一致性

## 故障排除

### 常见问题

1. **"用户ID是必需的"错误**
   - 确保用户已登录
   - 检查用户ID是否正确传递

2. **网络连接错误**
   - 检查网络连接
   - 服务会自动回退到本地存储

3. **数据迁移失败**
   - 检查Firebase权限设置
   - 验证数据结构格式

### 调试技巧

```javascript
// 启用详细日志
localStorage.setItem('debug_history', 'true');

// 检查存储状态
console.log('存储状态:', getStorageStatus());

// 测试连接
try {
  await hybridChatHistoryService.getChatHistory(1);
  console.log('Firebase连接正常');
} catch (error) {
  console.error('Firebase连接失败:', error);
}
```
