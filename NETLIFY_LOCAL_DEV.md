# Netlify本地开发环境设置指南

## 概述
这个指南帮助您设置本地Netlify开发环境，确保本地开发和线上部署的一致性。

## 已完成的配置

### 1. Netlify CLI安装
- ✅ 已安装 `netlify-cli` 全局包
- ✅ 版本: 23.4.2

### 2. 配置文件
- ✅ `netlify.toml` - Netlify配置文件
- ✅ `netlify.env` - 环境变量文件
- ✅ `start-netlify-dev.bat` - Windows批处理启动脚本
- ✅ `start-netlify-dev.ps1` - PowerShell启动脚本

## 使用方法

### 方法一：使用启动脚本（推荐）
```bash
# Windows批处理
start-netlify-dev.bat

# 或使用PowerShell
.\start-netlify-dev.ps1
```

### 方法二：直接使用CLI命令
```bash
# 启动本地开发服务器（离线模式）
netlify dev --offline

# 启动本地开发服务器（在线模式，需要网络）
netlify dev

# 启动并创建公共URL（需要网络）
netlify dev --live
```

## 配置说明

### netlify.toml 配置
- **构建命令**: `npm run build`
- **发布目录**: `dist`
- **Node.js版本**: 18
- **开发端口**: 8888
- **SPA重定向**: 已配置

### 环境变量
- `NODE_ENV=production` - 模拟生产环境
- `NETLIFY=true` - 标识Netlify环境

## 优势

### 1. 环境一致性
- 使用与生产环境相同的Node.js版本
- 相同的构建命令和配置
- 相同的环境变量

### 2. 问题排查
- 本地复现生产环境问题
- 测试部署前的构建过程
- 验证环境变量配置

### 3. 开发效率
- 快速测试部署配置
- 本地验证重定向规则
- 测试Netlify Functions（如果有）

## 常见问题

### 1. 网络连接问题
如果遇到网络连接问题，使用离线模式：
```bash
netlify dev --offline
```

### 2. 端口冲突
如果8888端口被占用，可以指定其他端口：
```bash
netlify dev --port 3000
```

### 3. 环境变量问题
确保环境变量在 `netlify.env` 文件中正确配置。

## 下一步
1. 启动本地Netlify开发服务器
2. 访问 http://localhost:8888
3. 测试应用功能
4. 对比本地Vite服务器和Netlify环境的差异

## 注意事项
- 本地Netlify环境会使用生产环境的构建配置
- 某些开发时的热重载功能可能不同
- 建议在部署前使用此环境进行最终测试
