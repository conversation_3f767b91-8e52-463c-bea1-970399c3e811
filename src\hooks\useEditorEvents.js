import { useSuggestionHandler } from './useSuggestionHandler';

/**
 * 编辑器事件处理Hook
 * 处理各种用户交互事件
 */
export const useEditorEvents = (text, setText, suggestions, setSuggestions, dispatch) => {
  const { applySuggestion, dismissSuggestion } = useSuggestionHandler(text, setText, suggestions, setSuggestions);

  // 处理查词请求
  const handleLookupWord = (word) => {
    console.log('EditorPage: 词典被请求，单词:', word);
    dispatch({ type: 'SHOW_DICTIONARY', payload: true, word: word });
  };

  // 处理API配置显示
  const handleShowApiConfig = () => {
    console.log('EditorPage: 设置按钮被点击');
    dispatch({ type: 'SHOW_API_CONFIG', payload: true });
  };

  // 处理词典显示
  const handleShowDictionary = () => {
    dispatch({ type: 'SHOW_DICTIONARY', payload: true });
  };

  // 处理页面切换
  const handleSwitchToChat = () => {
    console.log('EdgeNavigationArrow: 切换到聊天模式');
    dispatch({ type: 'SET_CURRENT_PAGE', payload: 'chat' });
  };

  return {
    applySuggestion,
    dismissSuggestion,
    handleLookupWord,
    handleShowApiConfig,
    handleShowDictionary,
    handleSwitchToChat
  };
};
