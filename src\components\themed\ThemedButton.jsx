import React from 'react';

const ThemedButton = ({
  variant = 'secondary',
  size = 'md',
  children,
  className = '',
  style = {},
  isDarkMode = false,
  isLoading = false,
  disabled = false,
  fullWidth = false,
  ...props
}) => {

  // 基础样式
  const baseStyles = {
    border: 'none',
    outline: 'none',
    cursor: 'pointer',
    fontFamily: 'Georgia, "Noto Serif SC", serif',
    letterSpacing: '0.05em',
    transition: 'all 0.2s ease',
    appearance: 'none',
    WebkitAppearance: 'none',
    MozAppearance: 'none',
  };

  // 尺寸样式
  const sizeStyles = {
    sm: {
      padding: '8px 16px',
      fontSize: '14px',
      borderRadius: '8px',
    },
    md: {
      padding: '12px 24px',
      fontSize: '16px',
      borderRadius: '12px',
    },
    lg: {
      padding: '16px 32px',
      fontSize: '18px',
      borderRadius: '16px',
    },
    icon: {
      width: '48px',
      height: '48px',
      padding: '0',
      borderRadius: '12px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }
  };

  // 变体样式
  const variantStyles = {
    primary: {
      backgroundColor: 'var(--color-accent-red)',
      color: '#FEFCF5',
    },
    secondary: {
      backgroundColor: 'var(--color-bg-accent)',
      color: 'var(--color-text-primary)',
    },
    success: {
      backgroundColor: 'var(--color-accent-green)',
      color: '#FEFCF5',
    },
    ghost: {
      backgroundColor: 'transparent',
      color: 'var(--color-text-secondary)',
    }
  };

  // 悬停样式
  const hoverStyles = {
    primary: {
      backgroundColor: 'var(--color-accent-red-hover)',
    },
    secondary: {
      backgroundColor: 'var(--color-bg-hover)',
    },
    success: {
      backgroundColor: 'var(--color-accent-green-hover)',
    },
    ghost: {
      backgroundColor: 'var(--color-bg-accent)',
    }
  };

  // 合并样式
  const finalStyles = {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant],
    ...(fullWidth && { width: '100%' }),
    ...(disabled || isLoading ? {
      opacity: 0.6,
      cursor: 'not-allowed',
      pointerEvents: 'none'
    } : {}),
    ...style,
  };

  return (
    <button
      className={`themed-button themed-button-${variant} ${className}`}
      style={finalStyles}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            style={{
              width: '16px',
              height: '16px',
              border: '2px solid rgba(255, 255, 255, 0.3)',
              borderTop: '2px solid currentColor',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}
          />
          {typeof children === 'string' ? children : 'Loading...'}
        </div>
      ) : (
        children
      )}
    </button>
  );
};

export default ThemedButton;