/**
 * 统一存储Hook
 * 提供易用的React接口来访问统一存储服务
 */

import { useState, useEffect, useCallback } from 'react';
import unifiedStorageService from '../services/storage/unifiedStorageService';

/**
 * 使用统一存储的通用Hook
 * @param {string} dataType - 数据类型 ('chat', 'analysis', 'dictionary', 'writing', 'diary')
 * @returns {Object} 存储相关的状态和方法
 */
export const useUnifiedStorage = (dataType) => {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [syncStatus, setSyncStatus] = useState('idle'); // 'idle', 'syncing', 'completed', 'failed'

  // 监听同步状态
  useEffect(() => {
    const handleSyncStatus = (status) => {
      setSyncStatus(status);
    };

    unifiedStorageService.addSyncListener(handleSyncStatus);
    return () => {
      unifiedStorageService.removeSyncListener(handleSyncStatus);
    };
  }, []);

  // 获取数据的方法映射
  const getDataMethods = {
    chat: unifiedStorageService.getChatHistory.bind(unifiedStorageService),
    analysis: unifiedStorageService.getAnalysisHistory.bind(unifiedStorageService),
    dictionary: unifiedStorageService.getDictionarySearchHistory.bind(unifiedStorageService),
    writing: unifiedStorageService.getWritingHistory.bind(unifiedStorageService),
    diary: unifiedStorageService.getDiaryHistory.bind(unifiedStorageService)
  };

  // 保存数据的方法映射
  const saveDataMethods = {
    chat: unifiedStorageService.saveChatSession.bind(unifiedStorageService),
    analysis: unifiedStorageService.saveAnalysis.bind(unifiedStorageService),
    dictionary: unifiedStorageService.saveDictionarySearch.bind(unifiedStorageService),
    writing: unifiedStorageService.saveWritingHistory.bind(unifiedStorageService),
    diary: unifiedStorageService.saveDiaryHistory.bind(unifiedStorageService)
  };

  // 获取数据
  const fetchData = useCallback(async (limit = 50, forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const getDataMethod = getDataMethods[dataType];
      if (!getDataMethod) {
        throw new Error(`不支持的数据类型: ${dataType}`);
      }

      const result = await getDataMethod(limit, forceRefresh);
      setData(result);
      return result;
    } catch (err) {
      console.error(`获取${dataType}数据失败:`, err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [dataType]);

  // 保存数据
  const saveData = useCallback(async (dataToSave) => {
    try {
      setError(null);
      
      const saveDataMethod = saveDataMethods[dataType];
      if (!saveDataMethod) {
        throw new Error(`不支持的数据类型: ${dataType}`);
      }

      const result = await saveDataMethod(dataToSave);
      
      // 更新本地状态
      setData(prev => [result, ...prev.filter(item => item.id !== result.id)]);
      
      return result;
    } catch (err) {
      console.error(`保存${dataType}数据失败:`, err);
      setError(err.message);
      throw err;
    }
  }, [dataType]);

  // 删除数据
  const deleteData = useCallback(async (id) => {
    try {
      setError(null);
      
      // 检查用户ID
      if (!unifiedStorageService.userId) {
        throw new Error('用户未登录，无法删除数据');
      }
      
      console.log(`🗑️ 准备删除${dataType}数据，用户ID:`, unifiedStorageService.userId, '记录ID:', id);
      
      // 根据数据类型调用相应的删除方法
      const deleteMethods = {
        chat: async (recordId) => {
          const { chatHistoryService } = await import('../services/history/firebaseHistoryService');
          return await chatHistoryService.deleteChatSession(unifiedStorageService.userId, recordId);
        },
        analysis: async (recordId) => {
          const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
          return await aiAnalysisService.deleteAnalysis(unifiedStorageService.userId, recordId);
        },
        dictionary: async (recordId) => {
          const { dictionarySearchService } = await import('../services/history/firebaseHistoryService');
          return await dictionarySearchService.deleteSearch(unifiedStorageService.userId, recordId);
        },
        writing: async (recordId) => {
          const { writingHistoryService } = await import('../services/history/firebaseHistoryService');
          return await writingHistoryService.deleteWriting(unifiedStorageService.userId, recordId);
        },
        diary: async (recordId) => {
          const { diaryHistoryService } = await import('../services/history/firebaseHistoryService');
          return await diaryHistoryService.deleteDiary(unifiedStorageService.userId, recordId);
        }
      };
      
      const deleteMethod = deleteMethods[dataType];
      if (deleteMethod) {
        console.log(`🗑️ 从Firebase删除${dataType}数据，ID:`, id);
        await deleteMethod(id);
        
        // 更新本地状态
        setData(prev => prev.filter(item => item.id !== id));
        
        // 更新本地缓存
        const cacheKey = `cache_${dataType}_history`;
        const cached = unifiedStorageService.getCache(cacheKey, Infinity) || [];
        const updatedCache = cached.filter(item => item.id !== id);
        unifiedStorageService.setCache(cacheKey, updatedCache);
        
        console.log(`✅ ${dataType}数据删除成功，ID:`, id);
      } else {
        throw new Error(`不支持的数据类型: ${dataType}`);
      }
      
      return true;
    } catch (err) {
      console.error(`删除${dataType}数据失败:`, err);
      setError(err.message);
      throw err;
    }
  }, [dataType]);

  // 清除所有数据
  const clearAllData = useCallback(async () => {
    try {
      setError(null);
      
      // 检查用户ID
      if (!unifiedStorageService.userId) {
        throw new Error('用户未登录，无法清除数据');
      }
      
      console.log(`🗑️ 准备清除所有${dataType}数据，用户ID:`, unifiedStorageService.userId);
      
      // 根据数据类型调用相应的清除方法
      const clearMethods = {
        chat: async () => {
          const { chatHistoryService } = await import('../services/history/firebaseHistoryService');
          return await chatHistoryService.clearAllChatHistory(unifiedStorageService.userId);
        },
        analysis: async () => {
          const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
          return await aiAnalysisService.clearAllAnalysisHistory(unifiedStorageService.userId);
        },
        dictionary: async () => {
          const { dictionarySearchService } = await import('../services/history/firebaseHistoryService');
          return await dictionarySearchService.clearAllSearch(unifiedStorageService.userId);
        },
        writing: async () => {
          const { writingHistoryService } = await import('../services/history/firebaseHistoryService');
          return await writingHistoryService.clearAllWriting(unifiedStorageService.userId);
        },
        diary: async () => {
          const { diaryHistoryService } = await import('../services/history/firebaseHistoryService');
          return await diaryHistoryService.clearAllDiary(unifiedStorageService.userId);
        }
      };
      
      const clearMethod = clearMethods[dataType];
      if (clearMethod) {
        console.log(`🗑️ 从Firebase清除所有${dataType}数据`);
        await clearMethod();
        
        // 更新本地状态
        setData([]);
        
        // 清除本地缓存
        const cacheKey = `cache_${dataType}_history`;
        unifiedStorageService.clearCache(cacheKey);
        
        console.log(`✅ 所有${dataType}数据清除成功`);
      } else {
        throw new Error(`不支持的数据类型: ${dataType}`);
      }
      
      return true;
    } catch (err) {
      console.error(`清除${dataType}数据失败:`, err);
      setError(err.message);
      throw err;
    }
  }, [dataType]);

  // 手动同步
  const manualSync = useCallback(async () => {
    try {
      setError(null);
      await unifiedStorageService.manualSync();
    } catch (err) {
      console.error('手动同步失败:', err);
      setError(err.message);
      throw err;
    }
  }, []);

  return {
    data,
    isLoading,
    error,
    syncStatus,
    fetchData,
    saveData,
    deleteData,
    clearAllData,
    manualSync
  };
};

/**
 * 使用聊天历史存储
 */
export const useChatStorage = () => {
  return useUnifiedStorage('chat');
};

/**
 * 使用AI分析历史存储
 */
export const useAnalysisStorage = () => {
  return useUnifiedStorage('analysis');
};

/**
 * 使用字典搜索历史存储
 */
export const useDictionaryStorage = () => {
  return useUnifiedStorage('dictionary');
};

/**
 * 使用写作历史存储
 */
export const useWritingStorage = () => {
  return useUnifiedStorage('writing');
};

/**
 * 使用日记历史存储
 */
export const useDiaryStorage = () => {
  return useUnifiedStorage('diary');
};

/**
 * 使用存储状态
 */
export const useStorageStatus = () => {
  const [syncStatus, setSyncStatus] = useState('idle');
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [cacheStats, setCacheStats] = useState(null);

  useEffect(() => {
    const handleSyncStatus = (status) => {
      setSyncStatus(status);
    };

    const handleOnlineStatus = () => {
      setIsOnline(true);
    };

    const handleOfflineStatus = () => {
      setIsOnline(false);
    };

    unifiedStorageService.addSyncListener(handleSyncStatus);
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOfflineStatus);

    // 获取缓存统计
    setCacheStats(unifiedStorageService.getCacheStats());

    return () => {
      unifiedStorageService.removeSyncListener(handleSyncStatus);
      window.removeEventListener('online', handleOnlineStatus);
      window.removeEventListener('offline', handleOfflineStatus);
    };
  }, []);

  const manualSync = useCallback(async () => {
    await unifiedStorageService.manualSync();
  }, []);

  const clearAllCache = useCallback(() => {
    unifiedStorageService.clearAllCache();
    setCacheStats(unifiedStorageService.getCacheStats());
  }, []);

  return {
    syncStatus,
    isOnline,
    cacheStats,
    manualSync,
    clearAllCache
  };
};
