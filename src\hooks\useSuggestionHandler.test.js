import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import { useSuggestionHandler } from './useSuggestionHandler';

// Mock writing service
vi.mock('../services/writing/writingTextService', () => ({
  autoSaveWritingText: vi.fn()
}));

import { autoSaveWritingText } from '../services/writing/writingTextService';

describe('useSuggestionHandler', () => {
  const mockText = 'how can i reach my dream?';
  const mockSetText = vi.fn();
  const mockSuggestions = [
    {
      id: 'suggestion1',
      type: 'capitalization',
      canApply: true,
      original: 'how can i reach my dream?',
      replacement: 'How can I reach my dream?',
      positions: [{ start: 0, end: 25 }]
    }
  ];
  const mockSetSuggestions = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    // 确保 mock 被正确设置
    autoSaveWritingText.mockClear();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should initialize with empty applied suggestions', () => {
    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, mockSuggestions, mockSetSuggestions)
    );

    expect(result.current.appliedSuggestions).toEqual(new Set());
  });

  it('should have autoSaveWritingText mock available', () => {
    expect(autoSaveWritingText).toBeDefined();
    expect(vi.isMockFunction(autoSaveWritingText)).toBe(true);
  });

  it('should apply suggestion and update text', () => {
    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, mockSuggestions, mockSetSuggestions)
    );

    act(() => {
      result.current.applySuggestion(mockSuggestions[0]);
    });

    expect(mockSetText).toHaveBeenCalledWith('How can I reach my dream?');
    expect(result.current.appliedSuggestions).toContain('suggestion1');
  });

  it('should not apply suggestion if canApply is false', () => {
    const nonApplicableSuggestion = {
      ...mockSuggestions[0],
      canApply: false
    };

    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, mockSuggestions, mockSetSuggestions)
    );

    act(() => {
      result.current.applySuggestion(nonApplicableSuggestion);
    });

    expect(mockSetText).not.toHaveBeenCalled();
  });

  it('should not apply suggestion if already applied', () => {
    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, mockSuggestions, mockSetSuggestions)
    );

    // First application
    act(() => {
      result.current.applySuggestion(mockSuggestions[0]);
    });

    // Second application should be ignored
    act(() => {
      result.current.applySuggestion(mockSuggestions[0]);
    });

    expect(mockSetText).toHaveBeenCalledTimes(1);
  });

  it('should remove applied suggestion from suggestions list after timeout', () => {
    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, mockSuggestions, mockSetSuggestions)
    );

    act(() => {
      result.current.applySuggestion(mockSuggestions[0]);
    });

    // Fast forward time
    act(() => {
      vi.advanceTimersByTime(1500);
    });

    expect(mockSetSuggestions).toHaveBeenCalledWith(expect.any(Function));
    expect(autoSaveWritingText).toHaveBeenCalledWith('How can I reach my dream?', []);
  });

  it('should dismiss suggestion and update localStorage', () => {
    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, mockSuggestions, mockSetSuggestions)
    );

    act(() => {
      result.current.dismissSuggestion(mockSuggestions[0]);
    });

    expect(mockSetSuggestions).toHaveBeenCalledWith(expect.any(Function));
    
    // 手动调用 setSuggestions 函数来验证 autoSaveWritingText 是否被调用
    const setSuggestionsCall = mockSetSuggestions.mock.calls[0][0];
    setSuggestionsCall(mockSuggestions);
    
    expect(autoSaveWritingText).toHaveBeenCalledWith(mockText, []);
  });

  it('should apply all applicable suggestions', () => {
    const multipleSuggestions = [
      {
        id: 'suggestion1',
        type: 'capitalization',
        canApply: true,
        original: 'how',
        replacement: 'How',
        positions: [{ start: 0, end: 3 }]
      },
      {
        id: 'suggestion2',
        type: 'capitalization',
        canApply: true,
        original: 'i',
        replacement: 'I',
        positions: [{ start: 8, end: 9 }]
      }
    ];

    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, multipleSuggestions, mockSetSuggestions)
    );

    act(() => {
      result.current.applyAllSuggestions();
    });

    expect(mockSetText).toHaveBeenCalledWith('How can I reach my dream?');
  });

  it('should handle spelling correction suggestions', () => {
    const spellingSuggestion = {
      id: 'spelling1',
      type: 'spelling',
      canApply: true,
      original: 'recieve',
      replacement: 'receive',
      positions: [{ start: 0, end: 7 }]
    };

    const { result } = renderHook(() => 
      useSuggestionHandler('recieve', mockSetText, [spellingSuggestion], mockSetSuggestions)
    );

    act(() => {
      result.current.applySuggestion(spellingSuggestion);
    });

    expect(mockSetText).toHaveBeenCalledWith('receive');
  });

  it('should handle punctuation suggestions', () => {
    const punctuationSuggestion = {
      id: 'punctuation1',
      type: 'punctuation',
      canApply: true,
      isAppend: true,
      replacement: '.'
    };

    const { result } = renderHook(() => 
      useSuggestionHandler('Hello world', mockSetText, [punctuationSuggestion], mockSetSuggestions)
    );

    act(() => {
      result.current.applySuggestion(punctuationSuggestion);
    });

    expect(mockSetText).toHaveBeenCalledWith('Hello world.');
  });

  it('should update localStorage when suggestions are removed after timeout', () => {
    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, mockSuggestions, mockSetSuggestions)
    );

    act(() => {
      result.current.applySuggestion(mockSuggestions[0]);
    });

    // Fast forward time to trigger timeout
    act(() => {
      vi.advanceTimersByTime(1500);
    });

    expect(autoSaveWritingText).toHaveBeenCalledWith('How can I reach my dream?', []);
  });

  it('should handle overlapping suggestions correctly', () => {
    const overlappingSuggestions = [
      {
        id: 'suggestion1',
        type: 'capitalization',
        canApply: true,
        original: 'how can i',
        replacement: 'How can I',
        positions: [{ start: 0, end: 9 }]
      },
      {
        id: 'suggestion2',
        type: 'capitalization',
        canApply: true,
        original: 'i',
        replacement: 'I',
        positions: [{ start: 8, end: 9 }]
      }
    ];

    const { result } = renderHook(() => 
      useSuggestionHandler(mockText, mockSetText, overlappingSuggestions, mockSetSuggestions)
    );

    act(() => {
      result.current.applySuggestion(overlappingSuggestions[0]);
    });

    // Fast forward time
    act(() => {
      vi.advanceTimersByTime(1500);
    });

    // Should remove overlapping suggestions
    expect(mockSetSuggestions).toHaveBeenCalledWith(expect.any(Function));
  });
});
