const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
import { loadPrompt, replacePromptVariables } from '../ai/promptLoader.js';
import { translateToChinese } from '../ai/translationService.js';
import { checkApiUsageLimit, getDoubaoApiKey, recordApiUsage } from '../user/userSettingsService.js';

async function fetchFromAI(data) {
    const apiKey = await getDoubaoApiKey();
    if (!apiKey) {
        throw new Error('系统API密钥未配置，请联系管理员');
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };

    const response = await fetch(API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorBody}`);
    }

    const result = await response.json();
    const aiResponse = result.choices[0].message.content;

    console.log('📥 Received from AI - Raw response:', aiResponse);
    console.log('📥 Received from AI - Full result:', JSON.stringify(result, null, 2));

    return aiResponse;
}

// AI聊天响应函数 - 修改为可选的翻译，支持多模态
export const getChatResponse = async (userMessage, conversationHistory = [], writingContext = null, autoTranslate = true, images = [], userId = null) => {
    console.log('🚀 getChatResponse called with:', { userMessage, conversationHistoryLength: conversationHistory.length, writingContext, autoTranslate, imageCount: images.length, userId });

    // 如果有用户ID，检查AI对话使用量限制
    if (userId) {
        const usageCheck = await checkApiUsageLimit(userId, 'chat');
        if (!usageCheck.canUse) {
            const maxRequests = usageCheck.maxRequests === -1 ? '无限制' : usageCheck.maxRequests;
            const remainingRequests = usageCheck.remainingRequests || 0;
            const planType = usageCheck.isPaidUser ? '基础版' : '免费版';
            throw new Error(`${planType}今日AI对话次数已达上限 (${maxRequests}次)，剩余 ${remainingRequests} 次。${usageCheck.isPaidUser ? '' : '升级到基础版(¥20/月)可享受无限次对话。'}`);
        }
    }

    try {
        // 第一次调用：生成英文响应（支持图片）
        const englishResponse = await generateEnglishResponse(userMessage, conversationHistory, writingContext, images);
        console.log('📥 英文响应生成成功:', englishResponse.substring(0, 100) + '...');

        if (autoTranslate) {
            // 第二次调用：翻译为中文
            const chineseTranslation = await translateToChinese(englishResponse);
            console.log('📥 中文翻译完成:', chineseTranslation.substring(0, 100) + '...');

            // 记录AI对话使用量（如果有用户ID）
            if (userId) {
                try {
                    await recordApiUsage(userId, 'chat');
                    console.log('✅ AI对话使用量记录成功');
                } catch (usageError) {
                    console.warn('记录AI对话使用量失败:', usageError);
                    // 不影响主要功能，继续执行
                }
            }

            // 返回格式化的响应
            return `${englishResponse}\n---\n${chineseTranslation}`;
        } else {
            // 记录AI对话使用量（如果有用户ID）
            if (userId) {
                try {
                    await recordApiUsage(userId, 'chat');
                    console.log('✅ AI对话使用量记录成功');
                } catch (usageError) {
                    console.warn('记录AI对话使用量失败:', usageError);
                    // 不影响主要功能，继续执行
                }
            }

            // 只返回英文响应
            return englishResponse;
        }
    } catch (error) {
        console.error('聊天响应生成失败:', error);
        throw error;
    }
};

// 生成英文响应的函数 - 支持多模态
const generateEnglishResponse = async (userMessage, conversationHistory = [], writingContext = null, images = []) => {
    // 从外部文件加载提示词
    let systemContent = await loadPrompt('chatResponsePrompt');

    if (writingContext) {
        // 替换提示词中的变量
        systemContent = replacePromptVariables(systemContent, {
            writingContext: {
                title: writingContext.title,
                contentPreview: `${writingContext.content.substring(0, 200)}${writingContext.content.length > 200 ? '...' : ''}`,
                wordCount: writingContext.wordCount,
                sharedDate: new Date(writingContext.sharedAt).toLocaleDateString()
            }
        });
    }

    // 构建多模态用户消息
    const buildUserMessage = (text, images) => {
        if (images && images.length > 0) {
            // 多模态消息：文本 + 图片
            const content = [
                { type: 'text', text: text || 'Please analyze these images.' }
            ];

            // 添加图片内容
            images.forEach(image => {
                content.push({
                    type: 'image_url',
                    image_url: {
                        url: image.dataUrl,
                        detail: 'high'
                    }
                });
            });

            return { role: 'user', content };
        } else {
            // 纯文本消息
            return { role: 'user', content: text };
        }
    };

    const messages = [
        { role: 'system', content: systemContent },
        ...conversationHistory
            .filter((msg, index) => !(index === 0 && msg.type === 'ai')) // Skip initial greeting
            .map(msg => {
                if (msg.images && msg.images.length > 0) {
                    // 历史消息中的多模态内容
                    return buildUserMessage(msg.content, msg.images);
                } else {
                    // 纯文本历史消息
                    return {
                        role: msg.type === 'user' ? 'user' : 'assistant',
                        content: msg.content,
                    };
                }
            }),
        buildUserMessage(userMessage, images),
    ];

    const data = {
        model: 'doubao-seed-1-6-flash-250615',
        messages,
        temperature: 0.7,
        max_tokens: 800,
        thinking: { type: "disabled" }
    };

    console.log('📤 发送英文生成请求 - System content:', systemContent);
    console.log('📤 发送英文生成请求 - Full messages:', messages);
    console.log('📤 发送英文生成请求 - Request data:', JSON.stringify(data, null, 2));

    return fetchFromAI(data);
};

// 手动翻译函数
export const translateMessage = async (englishText) => {
    console.log('🔄 手动翻译消息:', englishText.substring(0, 100) + '...');

    try {
        const chineseTranslation = await translateToChinese(englishText);
        console.log('📥 手动翻译完成:', chineseTranslation.substring(0, 100) + '...');
        return chineseTranslation;
    } catch (error) {
        console.error('手动翻译失败:', error);
        throw error;
    }
};
