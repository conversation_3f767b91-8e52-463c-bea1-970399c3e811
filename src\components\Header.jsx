import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>ting<PERSON>, Maximize } from 'lucide-react';

const Header = ({ onShowApiConfig, onShowAuth, onLogout, onToggleImmersiveMode, isDarkMode, user }) => {
  return (
    <div
      className="transition-colors duration-300"
      style={{ backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2' }}
    >
      <div className="max-w-7xl mx-auto py-6">
        <div className="flex items-center justify-between" style={{ paddingLeft: '80px', paddingRight: '80px' }}>
          <div className="flex items-center gap-8">
            <div>
              <h1
                className="text-3xl font-bold transition-colors duration-300"
                style={{
                  color: isDarkMode ? '#E8DCC6' : '#5D4037',
                  fontFamily: 'Georgia, "Noto Serif SC", serif',
                  letterSpacing: '0.1em'
                }}
              >
                對白
              </h1>
            </div>
            <div
              className="flex items-center justify-center w-12 h-12"
            >
              <img 
                src={isDarkMode ? "/logo-dark.svg" : "/logo.svg"} 
                alt="對白" 
                className="w-32 h-32 object-contain"
                style={{ maxWidth: 'none', maxHeight: 'none' }}
              />
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={onToggleImmersiveMode}
              className="header-btn"
              title="沉浸写作模式"
            >
              <Maximize className="w-6 h-6" />
            </button>
            <button
              onClick={() => {
                if (user) {
                  // If user is logged in, show API config
                  onShowApiConfig();
                } else {
                  // If user is not logged in, show auth modal
                  onShowAuth();
                }
              }}
              className="header-btn"
              title="设置"
            >
              <Settings className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
