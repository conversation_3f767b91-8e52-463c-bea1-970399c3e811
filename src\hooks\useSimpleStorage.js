/**
 * 简化存储Hook
 * 统一AI聊天和AI写作的存储接口
 */

import { useState, useEffect, useCallback } from 'react';
import simpleStorageService from '../services/storage/simpleStorageService';

/**
 * 使用简化存储的通用Hook
 * @param {string} type - 历史类型 ('chat', 'analysis')
 * @returns {Object} 历史记录相关的状态和方法
 */
export const useSimpleStorage = (type) => {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取数据
  const fetchData = useCallback(async (limit = null) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = simpleStorageService.getHistory(type.toUpperCase() + '_HISTORY', limit);
      setData(result);
      return result;
    } catch (err) {
      console.error(`获取${type}历史失败:`, err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [type]);

  // 保存数据
  const saveData = useCallback(async (dataToSave) => {
    try {
      setError(null);
      
      const result = simpleStorageService.saveHistory(type.toUpperCase() + '_HISTORY', dataToSave);
      
      // 更新本地状态
      setData(prev => [result, ...prev]);
      
      return result;
    } catch (err) {
      console.error(`保存${type}历史失败:`, err);
      setError(err.message);
      throw err;
    }
  }, [type]);

  // 删除数据
  const deleteData = useCallback(async (recordId) => {
    try {
      setError(null);
      
      const success = simpleStorageService.deleteHistory(type.toUpperCase() + '_HISTORY', recordId);
      
      if (success) {
        // 更新本地状态
        setData(prev => prev.filter(item => item.id !== recordId));
      }
      
      return success;
    } catch (err) {
      console.error(`删除${type}历史失败:`, err);
      setError(err.message);
      throw err;
    }
  }, [type]);

  // 清空数据
  const clearData = useCallback(async () => {
    try {
      setError(null);
      
      const success = simpleStorageService.clearHistory(type.toUpperCase() + '_HISTORY');
      
      if (success) {
        setData([]);
      }
      
      return success;
    } catch (err) {
      console.error(`清空${type}历史失败:`, err);
      setError(err.message);
      throw err;
    }
  }, [type]);

  // 手动同步
  const manualSync = useCallback(async () => {
    try {
      setError(null);
      await simpleStorageService.manualSync();
      // 同步后重新获取数据
      await fetchData();
    } catch (err) {
      console.error(`同步${type}历史失败:`, err);
      setError(err.message);
      throw err;
    }
  }, [type, fetchData]);

  // 初始化时加载数据
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    saveData,
    deleteData,
    clearData,
    manualSync
  };
};

/**
 * 使用聊天历史
 */
export const useChatStorage = () => {
  return useSimpleStorage('chat');
};

/**
 * 使用AI分析历史
 */
export const useAnalysisStorage = () => {
  return useSimpleStorage('analysis');
};

/**
 * 使用存储统计
 */
export const useStorageStats = () => {
  const [stats, setStats] = useState({
    chat: 0,
    analysis: 0,
    syncQueue: 0
  });

  const refreshStats = useCallback(() => {
    const newStats = simpleStorageService.getStats();
    setStats(newStats);
  }, []);

  useEffect(() => {
    refreshStats();
  }, [refreshStats]);

  return {
    stats,
    refreshStats
  };
};
