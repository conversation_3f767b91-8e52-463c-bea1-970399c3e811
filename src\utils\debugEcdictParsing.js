// ECDICT 数据解析调试工具
// 用于调试 CSV 解析和字段映射问题

// 测试 CSV 解析
const testCSVParsing = () => {
  console.log('🧪 测试 CSV 解析...');
  console.log('='.repeat(50));
  
  // 模拟 "doing" 的数据行
  const testLine = 'doing,\'du:i艐,"v engage in\\nv carry out or perform an action\\nv get (something) done\\nv proceed or get along\\nv give rise to; cause to happen or occur, not always intentionally\\nv carry out or practice; as of jobs and professions\\nv be sufficient; be adequate, either in quality or quantity\\nv create or design, often in a certain way\\nv behave in a certain manner; show a certain behavior; conduct or comport oneself\\nv spend time in prison or in a labor camp\\nv carry on or function\\nv arrange attractively\\nv travel or traverse (a distance)","n. 行为, 活动",,,,,5939,0,1:i/0:do/s:doings,,';
  
  console.log('原始数据行:');
  console.log(testLine);
  console.log('');
  
  // 解析 CSV
  const fields = parseCSVLine(testLine);
  
  console.log('解析后的字段:');
  fields.forEach((field, index) => {
    console.log(`  ${index}: "${field}"`);
  });
  
  console.log('');
  console.log('字段数量:', fields.length);
  
  // 检查关键字段
  const FIELDS = {
    WORD: 0,
    PHONETIC: 1,
    DEFINITION: 2,
    TRANSLATION: 3,
    POS: 4,
    COLLINS: 5,
    OXFORD: 6,
    TAG: 7,
    BNC: 8,
    FRQ: 9,
    EXCHANGE: 10,
    DETAIL: 11,
    AUDIO: 12
  };
  
  console.log('');
  console.log('关键字段内容:');
  console.log('  单词:', fields[FIELDS.WORD]);
  console.log('  音标:', fields[FIELDS.PHONETIC]);
  console.log('  英文释义:', fields[FIELDS.DEFINITION]);
  console.log('  中文释义:', fields[FIELDS.TRANSLATION]);
  console.log('  词性:', fields[FIELDS.POS]);
  console.log('  柯林斯:', fields[FIELDS.COLLINS]);
  console.log('  牛津:', fields[FIELDS.OXFORD]);
  console.log('  标签:', fields[FIELDS.TAG]);
  console.log('  BNC:', fields[FIELDS.BNC]);
  console.log('  当代:', fields[FIELDS.FRQ]);
  console.log('  词形变化:', fields[FIELDS.EXCHANGE]);
  
  return fields;
};

// 简单的CSV行解析器（复制自 ecdictService.js）
const parseCSVLine = (line) => {
  const fields = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      fields.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  fields.push(current.trim());
  return fields;
};

// 测试实际数据加载
const testActualDataLoading = async () => {
  console.log('🧪 测试实际数据加载...');
  console.log('='.repeat(50));
  
  try {
    const response = await fetch('/data/dictionary/ecdict.csv');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const csvText = await response.text();
    const lines = csvText.split('\n');
    
    console.log('总行数:', lines.length);
    console.log('标题行:', lines[0]);
    
    // 查找 "doing" 行
    const doingLine = lines.find(line => line.startsWith('doing,'));
    
    if (doingLine) {
      console.log('找到 "doing" 行:');
      console.log(doingLine);
      console.log('');
      
      const fields = parseCSVLine(doingLine);
      console.log('解析后的字段:');
      fields.forEach((field, index) => {
        console.log(`  ${index}: "${field}"`);
      });
      
      console.log('');
      console.log('关键字段内容:');
      console.log('  单词:', fields[0]);
      console.log('  音标:', fields[1]);
      console.log('  英文释义:', fields[2]);
      console.log('  中文释义:', fields[3]);
      console.log('  词性:', fields[4]);
      console.log('  柯林斯:', fields[5]);
      console.log('  牛津:', fields[6]);
      console.log('  标签:', fields[7]);
      console.log('  BNC:', fields[8]);
      console.log('  当代:', fields[9]);
      console.log('  词形变化:', fields[10]);
      
      return fields;
    } else {
      console.log('❌ 未找到 "doing" 行');
      return null;
    }
  } catch (error) {
    console.error('❌ 加载数据失败:', error);
    return null;
  }
};

// 测试 ECDICT 服务
const testEcdictService = async () => {
  console.log('🧪 测试 ECDICT 服务...');
  console.log('='.repeat(50));
  
  try {
    const { getWordDetails } = await import('../services/dictionary/ecdictService');
    const result = await getWordDetails('doing');
    
    if (result && !result.notFound) {
      console.log('✅ ECDICT 服务查询成功');
      console.log('📖 数据源:', result.source);
      console.log('🔤 单词:', result.word);
      console.log('🔊 音标:', result.phonetic);
      console.log('🇺🇸 英文释义:', result.definition);
      console.log('🇨🇳 中文释义:', result.translation);
      console.log('📝 词性:', result.pos);
      console.log('⭐ 柯林斯:', result.collins);
      console.log('📚 牛津:', result.oxford);
      console.log('🏷️ 标签:', result.tags);
      console.log('📊 BNC:', result.bnc);
      console.log('📈 当代:', result.frq);
      console.log('🔄 词形变化:', result.exchange);
      
      return result;
    } else {
      console.log('❌ ECDICT 服务查询失败');
      console.log('📝 错误信息:', result?.error || '未找到该单词');
      return null;
    }
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 运行所有调试测试
const runAllDebugTests = async () => {
  console.log('🚀 开始 ECDICT 数据解析调试...');
  console.log('='.repeat(70));
  
  try {
    // 测试 CSV 解析
    console.log('\n1. 测试 CSV 解析:');
    testCSVParsing();
    
    // 测试实际数据加载
    console.log('\n2. 测试实际数据加载:');
    await testActualDataLoading();
    
    // 测试 ECDICT 服务
    console.log('\n3. 测试 ECDICT 服务:');
    await testEcdictService();
    
    console.log('\n🎯 调试测试完成！');
    console.log('='.repeat(70));
    
  } catch (error) {
    console.error('❌ 调试过程中出现错误:', error);
  }
};

// 导出测试函数
export {
  testCSVParsing,
  testActualDataLoading,
  testEcdictService,
  runAllDebugTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.debugEcdictParsing = {
    testCSVParsing,
    testActualDataLoading,
    testEcdictService,
    runAllDebugTests
  };
  
  console.log('🧪 ECDICT 数据解析调试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.debugEcdictParsing.runAllDebugTests() // 运行所有调试测试');
  console.log('  - window.debugEcdictParsing.testCSVParsing() // 测试 CSV 解析');
  console.log('  - window.debugEcdictParsing.testActualDataLoading() // 测试实际数据加载');
  console.log('  - window.debugEcdictParsing.testEcdictService() // 测试 ECDICT 服务');
}