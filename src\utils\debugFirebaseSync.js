/**
 * 调试Firebase同步功能
 * 逐步验证每个环节
 */

import { db } from '../config/firebaseConfig';
import { collection, addDoc, getDocs, query, orderBy, limit } from 'firebase/firestore';
import simpleStorageService from '../services/storage/simpleStorageService';

// 步骤1: 测试Firebase连接
export const testFirebaseConnection = async () => {
  console.log('🔌 步骤1: 测试Firebase连接...');
  
  try {
    // 尝试创建一个测试集合
    const testCollection = collection(db, 'test_connection');
    console.log('✅ Firebase数据库连接正常');
    return true;
  } catch (error) {
    console.error('❌ Firebase连接失败:', error);
    return false;
  }
};

// 步骤2: 测试直接写入Firebase
export const testDirectFirebaseWrite = async () => {
  console.log('✍️ 步骤2: 测试直接写入Firebase...');
  
  try {
    const testData = {
      message: 'Hello Firebase!',
      timestamp: new Date(),
      testId: 'test_' + Date.now()
    };
    
    const testCollection = collection(db, 'test_direct_write');
    const docRef = await addDoc(testCollection, testData);
    console.log('✅ 直接写入Firebase成功，文档ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('❌ 直接写入Firebase失败:', error);
    return null;
  }
};

// 步骤3: 测试从Firebase读取
export const testDirectFirebaseRead = async () => {
  console.log('📖 步骤3: 测试从Firebase读取...');
  
  try {
    const testCollection = collection(db, 'test_direct_write');
    const q = query(testCollection, orderBy('timestamp', 'desc'), limit(5));
    const querySnapshot = await getDocs(q);
    
    const results = [];
    querySnapshot.forEach((doc) => {
      results.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    console.log('✅ 从Firebase读取成功，共', results.length, '条记录');
    console.log('📄 最新记录:', results[0]);
    return results;
  } catch (error) {
    console.error('❌ 从Firebase读取失败:', error);
    return [];
  }
};

// 步骤4: 测试简化存储服务的Firebase同步
export const testSimpleStorageFirebaseSync = async () => {
  console.log('🔄 步骤4: 测试简化存储服务的Firebase同步...');
  
  const testUserId = 'debug_user_' + Date.now();
  console.log('👤 测试用户ID:', testUserId);
  
  // 初始化服务
  simpleStorageService.init(testUserId);
  
  // 保存测试数据
  const testChat = {
    id: 'debug_chat_' + Date.now(),
    messages: [
      { role: 'user', content: 'Debug test message' },
      { role: 'assistant', content: 'Debug test response' }
    ],
    sessionTitle: 'Debug Test Chat',
    timestamp: new Date().toISOString(),
    userId: testUserId
  };
  
  console.log('💾 保存测试数据到本地...');
  const savedChat = simpleStorageService.saveChatSession(testChat);
  console.log('✅ 本地保存成功:', savedChat.id);
  
  // 检查同步队列
  const stats = simpleStorageService.getStats();
  console.log('📊 同步队列长度:', stats.syncQueue);
  
  // 手动同步
  console.log('🔄 开始手动同步...');
  try {
    await simpleStorageService.manualSync();
    console.log('✅ 手动同步完成');
    
    // 验证同步结果
    const syncedData = await simpleStorageService.syncFromFirebase('CHAT_HISTORY');
    console.log('🔍 验证同步结果:', syncedData.length, '条记录');
    
    if (syncedData.length > 0) {
      console.log('🎉 Firebase同步验证成功！');
      return true;
    } else {
      console.log('⚠️ 同步验证失败：没有从Firebase读取到数据');
      return false;
    }
  } catch (error) {
    console.error('❌ 同步过程失败:', error);
    return false;
  }
};

// 完整测试流程
export const runFullFirebaseDebug = async () => {
  console.log('🚀 开始完整Firebase调试流程...');
  console.log('='.repeat(50));
  
  // 步骤1: 测试连接
  const connectionOk = await testFirebaseConnection();
  if (!connectionOk) {
    console.log('❌ Firebase连接失败，停止测试');
    return;
  }
  
  // 步骤2: 测试直接写入
  const writeResult = await testDirectFirebaseWrite();
  if (!writeResult) {
    console.log('❌ 直接写入失败，停止测试');
    return;
  }
  
  // 步骤3: 测试直接读取
  const readResult = await testDirectFirebaseRead();
  if (readResult.length === 0) {
    console.log('❌ 直接读取失败，停止测试');
    return;
  }
  
  // 步骤4: 测试简化存储同步
  const syncResult = await testSimpleStorageFirebaseSync();
  if (!syncResult) {
    console.log('❌ 简化存储同步失败');
    return;
  }
  
  console.log('='.repeat(50));
  console.log('🎉 所有Firebase测试通过！');
};

// 在控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testFirebaseConnection = testFirebaseConnection;
  window.testDirectFirebaseWrite = testDirectFirebaseWrite;
  window.testDirectFirebaseRead = testDirectFirebaseRead;
  window.testSimpleStorageFirebaseSync = testSimpleStorageFirebaseSync;
  window.runFullFirebaseDebug = runFullFirebaseDebug;
}
