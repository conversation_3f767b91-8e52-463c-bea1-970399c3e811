/**
 * 测试删除功能
 */

import { auth } from '../config/firebaseConfig';
import { generateUniqueId } from './idGenerator';

/**
 * 测试删除功能
 */
export const testDeleteFunction = async () => {
  console.log('🧪 开始测试删除功能...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    console.log('👤 用户ID:', user.uid);
    
    // 导入Firebase服务
    const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
    
    // 创建测试记录
    const testRecord = {
      id: generateUniqueId(),
      text: '测试删除功能的文本',
      rawAnalysis: '测试分析结果',
      analysis: { suggestions: [] },
      timestamp: new Date().toISOString()
    };
    
    console.log('📝 创建测试记录:', testRecord);
    
    // 保存测试记录
    await aiAnalysisService.saveAnalysis(testRecord, user.uid);
    console.log('✅ 测试记录保存成功');
    
    // 等待一下确保保存完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 验证记录存在
    const history = await aiAnalysisService.getAnalysisHistory(user.uid, 10);
    const foundRecord = history.find(record => record.id === testRecord.id);
    
    if (!foundRecord) {
      console.log('❌ 测试记录未找到，可能保存失败');
      return false;
    }
    
    console.log('✅ 测试记录验证成功，开始删除...');
    
    // 删除测试记录
    try {
      await aiAnalysisService.deleteAnalysis(user.uid, testRecord.id);
      console.log('✅ 删除操作完成');
      
      // 等待一下确保删除完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 验证删除结果
      const updatedHistory = await aiAnalysisService.getAnalysisHistory(user.uid, 10);
      const stillExists = updatedHistory.find(record => record.id === testRecord.id);
      
      if (stillExists) {
        console.log('❌ 删除失败，记录仍然存在');
        return false;
      } else {
        console.log('✅ 删除成功，记录已从Firebase移除');
        return true;
      }
      
    } catch (error) {
      console.error('❌ 删除操作失败:', error);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 删除功能测试失败:', error);
    return false;
  }
};

/**
 * 测试权限
 */
export const testFirebasePermissions = async () => {
  console.log('🧪 开始测试Firebase权限...');
  
  try {
    const user = auth.currentUser;
    if (!user) {
      console.log('❌ 没有登录用户');
      return false;
    }
    
    console.log('👤 用户ID:', user.uid);
    console.log('📧 用户邮箱:', user.email);
    console.log('🔑 用户认证状态:', user.emailVerified ? '已验证' : '未验证');
    
    // 测试读取权限
    const { aiAnalysisService } = await import('../services/history/firebaseHistoryService');
    
    try {
      const history = await aiAnalysisService.getAnalysisHistory(user.uid, 1);
      console.log('✅ 读取权限正常');
    } catch (error) {
      console.error('❌ 读取权限失败:', error);
      return false;
    }
    
    // 测试写入权限
    try {
      const testData = {
        id: generateUniqueId(),
        text: '测试删除功能',
        rawAnalysis: '测试分析',
        analysis: { suggestions: [] },
        timestamp: new Date().toISOString()
      };
      
      await aiAnalysisService.saveAnalysis(testData, user.uid);
      console.log('✅ 写入权限正常');
      
      // 立即删除测试数据
      await aiAnalysisService.deleteAnalysis(user.uid, testData.id);
      console.log('✅ 删除权限正常');
      
    } catch (error) {
      console.error('❌ 写入/删除权限失败:', error);
      return false;
    }
    
    console.log('🎉 所有权限测试通过');
    return true;
  } catch (error) {
    console.error('❌ 权限测试失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.testDelete = {
    testDeleteFunction,
    testFirebasePermissions
  };
  
  console.log('🔧 删除功能测试工具已加载到 window.testDelete');
  console.log('💡 使用方法:');
  console.log('  - await window.testDelete.testDeleteFunction() // 测试删除功能');
  console.log('  - await window.testDelete.testFirebasePermissions() // 测试权限');
}