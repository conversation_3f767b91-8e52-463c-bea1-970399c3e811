// 使用公共 WordNet API 的服务

// WordNet API 端点
const WORDNET_API_URL = 'https://api.wordnik.com/v4';
// 注意：实际使用时需要申请 API key
const API_KEY = 'your_api_key_here'; 

// 查询单词定义
const getDefinitions = async (word) => {
  try {
    const response = await fetch(
      `${WORDNET_API_URL}/word.json/${word}/definitions?limit=10&includeRelated=true&sourceDictionaries=wordnet&useCanonical=true&api_key=${API_KEY}`
    );
    
    if (!response.ok) {
      throw new Error(`API 请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取定义失败:', error);
    return [];
  }
};

// 查询同义词
const getSynonyms = async (word) => {
  try {
    const response = await fetch(
      `${WORDNET_API_URL}/word.json/${word}/relatedWords?relationshipTypes=synonym&limitPerRelationshipType=10&api_key=${API_KEY}`
    );
    
    if (!response.ok) {
      throw new Error(`API 请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.length > 0 ? data[0].words : [];
  } catch (error) {
    console.error('获取同义词失败:', error);
    return [];
  }
};

// 查询反义词
const getAntonyms = async (word) => {
  try {
    const response = await fetch(
      `${WORDNET_API_URL}/word.json/${word}/relatedWords?relationshipTypes=antonym&limitPerRelationshipType=10&api_key=${API_KEY}`
    );
    
    if (!response.ok) {
      throw new Error(`API 请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.length > 0 ? data[0].words : [];
  } catch (error) {
    console.error('获取反义词失败:', error);
    return [];
  }
};

// 获取单词的详细信息
const getWordDetails = async (word) => {
  try {
    const [definitions, synonyms, antonyms] = await Promise.all([
      getDefinitions(word),
      getSynonyms(word),
      getAntonyms(word)
    ]);
    
    return {
      word,
      definitions,
      synonyms,
      antonyms
    };
  } catch (error) {
    console.error('获取单词详情失败:', error);
    return {
      word,
      definitions: [],
      synonyms: [],
      antonyms: []
    };
  }
};

export {
  getDefinitions,
  getSynonyms,
  getAntonyms,
  getWordDetails
};