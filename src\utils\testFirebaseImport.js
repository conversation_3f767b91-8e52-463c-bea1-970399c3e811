/**
 * 测试Firebase函数导入
 */

import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  deleteDoc,
  writeBatch,
  serverTimestamp 
} from 'firebase/firestore';

import { db } from '../config/firebaseConfig';

/**
 * 测试Firebase函数导入
 */
export const testFirebaseImport = () => {
  console.log('🧪 测试Firebase函数导入...');
  
  const functions = {
    collection,
    doc,
    setDoc,
    getDoc,
    getDocs,
    query,
    where,
    orderBy,
    limit,
    deleteDoc,
    writeBatch,
    serverTimestamp
  };
  
  console.log('📋 Firebase函数检查:');
  Object.entries(functions).forEach(([name, func]) => {
    if (typeof func === 'function') {
      console.log(`✅ ${name}: 已正确导入 (${typeof func})`);
    } else {
      console.log(`❌ ${name}: 导入失败 (${typeof func})`);
    }
  });
  
  // 特别测试limit函数
  console.log('🔍 特别测试limit函数:');
  console.log('limit函数类型:', typeof limit);
  console.log('limit函数:', limit);
  
  if (typeof limit === 'function') {
    try {
      const testLimit = limit(5);
      console.log('✅ limit(5) 调用成功:', testLimit);
    } catch (error) {
      console.error('❌ limit(5) 调用失败:', error);
    }
  } else {
    console.error('❌ limit不是函数，类型:', typeof limit);
  }
  
  return typeof limit === 'function';
};

/**
 * 测试简单的Firestore查询
 */
export const testSimpleQuery = async () => {
  console.log('🧪 测试简单Firestore查询...');
  
  try {
    // 创建一个简单的查询来测试limit函数
    const testCollection = collection(db, 'test');
    const testQuery = query(testCollection, limit(1));
    
    console.log('✅ 查询创建成功:', testQuery);
    return true;
  } catch (error) {
    console.error('❌ 查询创建失败:', error);
    return false;
  }
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.firebaseImportTest = {
    testFirebaseImport,
    testSimpleQuery
  };
  
  console.log('🔧 Firebase导入测试工具已加载到 window.firebaseImportTest');
  console.log('💡 使用方法:');
  console.log('  - window.firebaseImportTest.testFirebaseImport()');
  console.log('  - await window.firebaseImportTest.testSimpleQuery()');
}
