import { describe, it, expect, beforeEach } from 'vitest';
import { 
  generateUniqueId, 
  generateSimpleId, 
  generateTimestampId, 
  generateIncrementId, 
  resetIncrementId 
} from './idGenerator';

describe('idGenerator', () => {
  beforeEach(() => {
    resetIncrementId();
  });

  describe('generateUniqueId', () => {
    it('应该生成唯一ID', () => {
      const id1 = generateUniqueId();
      const id2 = generateUniqueId();
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });

    it('应该生成不同长度的ID', () => {
      const ids = Array.from({ length: 10 }, () => generateUniqueId());
      
      // 所有ID都应该不同
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(10);
    });

    it('应该生成包含时间戳的ID', () => {
      const id = generateUniqueId();
      
      // ID应该包含数字（时间戳）
      expect(/\d+/.test(id)).toBe(true);
    });

    it('应该生成随机字符串', () => {
      const id = generateUniqueId();
      
      // ID应该包含字母和数字
      expect(/[a-zA-Z0-9]+/.test(id)).toBe(true);
    });
  });

  describe('generateSimpleId', () => {
    it('应该生成简单ID', () => {
      const id = generateSimpleId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id.length).toBe(9);
    });

    it('应该生成不同的简单ID', () => {
      const id1 = generateSimpleId();
      const id2 = generateSimpleId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateTimestampId', () => {
    it('应该生成时间戳ID', () => {
      const id = generateTimestampId();
      expect(typeof id).toBe('number');
      expect(id).toBeGreaterThan(0);
    });

    it('应该生成递增的时间戳ID', () => {
      const id1 = generateTimestampId();
      const id2 = generateTimestampId();
      expect(id2).toBeGreaterThanOrEqual(id1);
    });
  });

  describe('generateIncrementId', () => {
    it('应该生成递增ID', () => {
      const id1 = generateIncrementId();
      const id2 = generateIncrementId();
      const id3 = generateIncrementId();
      
      expect(id1).toBe(1);
      expect(id2).toBe(2);
      expect(id3).toBe(3);
    });

    it('应该从1开始递增', () => {
      resetIncrementId();
      const id = generateIncrementId();
      expect(id).toBe(1);
    });
  });

  describe('resetIncrementId', () => {
    it('应该重置递增ID计数器', () => {
      generateIncrementId();
      generateIncrementId();
      resetIncrementId();
      
      const id = generateIncrementId();
      expect(id).toBe(1);
    });
  });
});