import React, { useState, useEffect } from 'react';
import { useChatCache } from '../hooks/useLocalCache';

/**
 * 使用缓存功能的聊天历史组件示例
 * 展示如何在实际组件中集成本地缓存功能
 */
const ChatHistoryWithCache = ({ isDarkMode, onSelectSession }) => {
  const { getChatHistory, saveChatSession, isLoading, error, syncStatus } = useChatCache();
  const [history, setHistory] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 加载聊天历史
  const loadHistory = async (forceRefresh = false) => {
    try {
      setIsRefreshing(forceRefresh);
      const data = await getChatHistory(50, forceRefresh);
      setHistory(data);
    } catch (err) {
      console.error('加载聊天历史失败:', err);
    } finally {
      setIsRefreshing(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadHistory();
  }, []);

  // 处理会话选择
  const handleSelectSession = (session) => {
    if (onSelectSession) {
      onSelectSession(session);
    }
  };

  // 手动刷新
  const handleRefresh = () => {
    loadHistory(true);
  };

  // 保存新会话
  const handleSaveSession = async (sessionData) => {
    try {
      await saveChatSession(sessionData);
      // 保存成功后重新加载历史
      loadHistory(true);
    } catch (err) {
      console.error('保存会话失败:', err);
    }
  };

  return (
    <div
      className="chat-history-container"
      style={{
        backgroundColor: isDarkMode ? '#1A1611' : '#F5EFE6',
        color: isDarkMode ? '#E8DCC6' : '#5D4037'
      }}
    >
      {/* 头部 */}
      <div
        className="flex items-center justify-between p-4 border-b"
        style={{
          borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8'
        }}
      >
        <h2
          className="text-xl font-bold"
          style={{
            fontFamily: 'Georgia, "Noto Serif SC", serif'
          }}
        >
          聊天历史
        </h2>
        
        <div className="flex items-center gap-2">
          {/* 同步状态指示 */}
          {syncStatus === 'syncing' && (
            <div className="flex items-center gap-1 text-sm">
              <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
              <span>同步中...</span>
            </div>
          )}
          
          {/* 刷新按钮 */}
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="p-2 rounded-lg transition-colors duration-200 hover:opacity-80"
            style={{
              backgroundColor: isDarkMode ? '#374151' : '#F3F4F6',
              color: isDarkMode ? '#E8DCC6' : '#5D4037'
            }}
            title="刷新历史记录"
          >
            <svg
              className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div
          className="p-3 m-4 rounded-lg border"
          style={{
            backgroundColor: isDarkMode ? '#2D1B1B' : '#FEF2F2',
            borderColor: isDarkMode ? '#7F1D1D' : '#FECACA',
            color: isDarkMode ? '#FCA5A5' : '#DC2626'
          }}
        >
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-sm">加载失败: {error}</span>
          </div>
        </div>
      )}

      {/* 加载状态 */}
      {isLoading && !isRefreshing && (
        <div className="flex items-center justify-center p-8">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>加载中...</span>
          </div>
        </div>
      )}

      {/* 历史列表 */}
      <div className="flex-1 overflow-y-auto">
        {history.length === 0 && !isLoading ? (
          <div className="flex items-center justify-center p-8 text-center">
            <div>
              <div className="text-4xl mb-2">💬</div>
              <p className="text-sm opacity-60">暂无聊天历史</p>
              <p className="text-xs opacity-40 mt-1">开始新的对话吧！</p>
            </div>
          </div>
        ) : (
          <div className="p-2">
            {history.map((session) => (
              <div
                key={session.id}
                onClick={() => handleSelectSession(session)}
                className="p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 hover:scale-[1.02]"
                style={{
                  backgroundColor: isDarkMode ? '#2A241D' : '#F0E6D2',
                  border: `1px solid ${isDarkMode ? '#4A3F35' : '#E6D7B8'}`
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3
                      className="font-medium truncate"
                      style={{
                        fontFamily: 'Georgia, "Noto Serif SC", serif'
                      }}
                    >
                      {session.title || '未命名对话'}
                    </h3>
                    <p
                      className="text-sm mt-1 opacity-70 truncate"
                      style={{
                        fontFamily: 'Georgia, "Noto Serif SC", serif'
                      }}
                    >
                      {session.messages && session.messages.length > 0
                        ? session.messages[session.messages.length - 1].content
                        : '暂无消息'}
                    </p>
                    <div
                      className="text-xs mt-2 opacity-50"
                      style={{
                        fontFamily: 'Georgia, "Noto Serif SC", serif'
                      }}
                    >
                      {session.timestamp
                        ? new Date(session.timestamp).toLocaleString()
                        : '未知时间'}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1 ml-2">
                    <span
                      className="text-xs px-2 py-1 rounded-full"
                      style={{
                        backgroundColor: isDarkMode ? '#374151' : '#E5E7EB',
                        color: isDarkMode ? '#D1D5DB' : '#6B7280'
                      }}
                    >
                      {session.messages ? session.messages.length : 0} 条消息
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部信息 */}
      <div
        className="p-3 border-t text-xs opacity-60"
        style={{
          borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
          fontFamily: 'Georgia, "Noto Serif SC", serif'
        }}
      >
        <div className="flex items-center justify-between">
          <span>共 {history.length} 个对话</span>
          <span>
            {syncStatus === 'completed' ? '已同步' : 
             syncStatus === 'syncing' ? '同步中...' : 
             syncStatus === 'failed' ? '同步失败' : '离线模式'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ChatHistoryWithCache;

