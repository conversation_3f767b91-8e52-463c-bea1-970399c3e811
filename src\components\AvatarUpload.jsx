import React, { useState, useRef } from 'react';
import { Camera, Upload, X, Check } from 'lucide-react';

const AvatarUpload = ({ 
  currentAvatar, 
  onAvatarChange, 
  isDarkMode, 
  size = 'large',
  showUploadButton = true 
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState(null);
  const [error, setError] = useState('');
  const fileInputRef = useRef(null);

  const sizeClasses = {
    small: 'w-12 h-12',
    medium: 'w-16 h-16', 
    large: 'w-20 h-20',
    xlarge: 'w-24 h-24'
  };

  const iconSizes = {
    small: 'w-4 h-4',
    medium: 'w-5 h-5',
    large: 'w-6 h-6',
    xlarge: 'w-7 h-7'
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      setError('请选择图片文件');
      return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('图片大小不能超过5MB');
      return;
    }

    setError('');
    setIsUploading(true);

    // 创建预览 - 保持高质量
    const reader = new FileReader();
    reader.onload = (e) => {
      // 创建图片对象来优化质量
      const img = new Image();
      img.onload = () => {
        // 创建canvas来保持质量
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸为头像显示尺寸的2倍（高DPI支持）
        const displaySize = size === 'small' ? 48 : size === 'medium' ? 64 : size === 'large' ? 80 : 96;
        const scale = 2; // 2倍分辨率
        canvas.width = displaySize * scale;
        canvas.height = displaySize * scale;
        
        // 设置高质量渲染
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 计算智能裁剪参数（填满整个区域）
        const imgAspectRatio = img.width / img.height;
        const canvasAspectRatio = canvas.width / canvas.height;
        
        let sourceX, sourceY, sourceWidth, sourceHeight;
        let drawWidth, drawHeight, drawX, drawY;
        
        if (imgAspectRatio > canvasAspectRatio) {
          // 图片更宽，裁剪左右两边
          sourceHeight = img.height;
          sourceWidth = img.height * canvasAspectRatio;
          sourceX = (img.width - sourceWidth) / 2;
          sourceY = 0;
          
          drawWidth = canvas.width;
          drawHeight = canvas.height;
          drawX = 0;
          drawY = 0;
        } else {
          // 图片更高，裁剪上下两边
          sourceWidth = img.width;
          sourceHeight = img.width / canvasAspectRatio;
          sourceX = 0;
          sourceY = (img.height - sourceHeight) / 2;
          
          drawWidth = canvas.width;
          drawHeight = canvas.height;
          drawX = 0;
          drawY = 0;
        }
        
        // 绘制图片（智能裁剪，填满区域）
        ctx.drawImage(img, sourceX, sourceY, sourceWidth, sourceHeight, drawX, drawY, drawWidth, drawHeight);
        
        // 转换为高质量base64
        const highQualityDataUrl = canvas.toDataURL('image/jpeg', 0.95);
        setPreview(highQualityDataUrl);
        setIsUploading(false);
      };
      img.src = e.target.result;
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = () => {
    if (preview) {
      // 进一步优化保存的图片质量
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // 使用更高的分辨率保存
        const displaySize = size === 'small' ? 48 : size === 'medium' ? 64 : size === 'large' ? 80 : 96;
        const scale = 3; // 3倍分辨率保存
        canvas.width = displaySize * scale;
        canvas.height = displaySize * scale;
        
        // 最高质量设置
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 计算智能裁剪参数（填满整个区域）
        const imgAspectRatio = img.width / img.height;
        const canvasAspectRatio = canvas.width / canvas.height;
        
        let sourceX, sourceY, sourceWidth, sourceHeight;
        let drawWidth, drawHeight, drawX, drawY;
        
        if (imgAspectRatio > canvasAspectRatio) {
          // 图片更宽，裁剪左右两边
          sourceHeight = img.height;
          sourceWidth = img.height * canvasAspectRatio;
          sourceX = (img.width - sourceWidth) / 2;
          sourceY = 0;
          
          drawWidth = canvas.width;
          drawHeight = canvas.height;
          drawX = 0;
          drawY = 0;
        } else {
          // 图片更高，裁剪上下两边
          sourceWidth = img.width;
          sourceHeight = img.width / canvasAspectRatio;
          sourceX = 0;
          sourceY = (img.height - sourceHeight) / 2;
          
          drawWidth = canvas.width;
          drawHeight = canvas.height;
          drawX = 0;
          drawY = 0;
        }
        
        // 绘制图片（智能裁剪，填满区域）
        ctx.drawImage(img, sourceX, sourceY, sourceWidth, sourceHeight, drawX, drawY, drawWidth, drawHeight);
        
        // 使用PNG格式保存（无损压缩）
        const finalImage = canvas.toDataURL('image/png');
        
        // 保存到localStorage
        const avatarData = {
          image: finalImage,
          timestamp: Date.now()
        };
        localStorage.setItem('user_avatar', JSON.stringify(avatarData));
        
        // 通知父组件
        onAvatarChange(finalImage);
        setPreview(null);
      };
      img.src = preview;
    }
  };

  const handleCancel = () => {
    setPreview(null);
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveAvatar = () => {
    localStorage.removeItem('user_avatar');
    onAvatarChange(null);
  };

  const getCurrentAvatar = () => {
    if (preview) return preview;
    if (currentAvatar) return currentAvatar;
    
    // 从localStorage获取
    try {
      const saved = localStorage.getItem('user_avatar');
      if (saved) {
        const avatarData = JSON.parse(saved);
        return avatarData.image;
      }
    } catch (e) {
      console.error('Failed to load avatar from localStorage:', e);
    }
    
    return null;
  };

  const currentAvatarSrc = getCurrentAvatar();

  return (
    <div className="flex flex-col items-center gap-3">
      {/* 头像显示区域 */}
      <div className="relative group">
        <div
          className={`${sizeClasses[size]} rounded-full flex items-center justify-center border-4 transition-all duration-300 cursor-pointer overflow-hidden`}
          style={{
            backgroundColor: isDarkMode ? '#332B22' : '#FFFEF7',
            borderColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }}
          onClick={() => fileInputRef.current?.click()}
        >
          {currentAvatarSrc ? (
            <img
              src={currentAvatarSrc}
              alt="用户头像"
              className="w-full h-full object-cover"
            />
          ) : (
            <span style={{ fontSize: size === 'small' ? '16px' : size === 'medium' ? '24px' : size === 'large' ? '32px' : '40px' }}>
              👤
            </span>
          )}
        </div>

        {/* 上传按钮覆盖层 */}
        <div 
          className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"
        >
          <div className="flex flex-col items-center gap-1">
            <Camera className={`${iconSizes[size]} text-white`} />
            <span className="text-white text-xs font-medium">点击上传</span>
          </div>
        </div>

        {/* 删除按钮 */}
        {currentAvatarSrc && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleRemoveAvatar();
            }}
            className="absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200 z-10"
            style={{
              backgroundColor: isDarkMode ? '#8B4513' : '#D2691E',
              color: '#FEFCF5',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
              border: `1px solid ${isDarkMode ? '#A0522D' : '#CD853F'}`
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = isDarkMode ? '#A0522D' : '#CD853F';
              e.target.style.transform = 'scale(1.1)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = isDarkMode ? '#8B4513' : '#D2691E';
              e.target.style.transform = 'scale(1)';
            }}
            title="删除头像"
          >
            <X className="w-3 h-3" />
          </button>
        )}
      </div>

      {/* 文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />


      {/* 预览和操作按钮 */}
      {preview && (
        <div className="flex gap-3 mt-2">
          <button
            onClick={handleUpload}
            disabled={isUploading}
            className="flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50"
            style={{
              backgroundColor: isDarkMode ? '#2D5A2D' : '#166534',
              color: '#FEFCF5',
              border: `1px solid ${isDarkMode ? '#4A7C59' : '#22C55E'}`,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              fontFamily: 'Georgia, "Noto Serif SC", serif'
            }}
            onMouseEnter={(e) => {
              if (!isUploading) {
                e.target.style.backgroundColor = isDarkMode ? '#4A7C59' : '#22C55E';
                e.target.style.transform = 'translateY(-1px)';
                e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
              }
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = isDarkMode ? '#2D5A2D' : '#166534';
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
            }}
          >
            <Check className="w-4 h-4" />
            {isUploading ? '上传中...' : '确认'}
          </button>
          <button
            onClick={handleCancel}
            className="flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium"
            style={{
              backgroundColor: isDarkMode ? '#4A3F35' : '#E6D7B8',
              color: isDarkMode ? '#E8DCC6' : '#5D4037',
              border: `1px solid ${isDarkMode ? '#6B5B47' : '#D4C4A8'}`,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              fontFamily: 'Georgia, "Noto Serif SC", serif'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = isDarkMode ? '#6B5B47' : '#D4C4A8';
              e.target.style.transform = 'translateY(-1px)';
              e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = isDarkMode ? '#4A3F35' : '#E6D7B8';
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            }}
          >
            <X className="w-4 h-4" />
            取消
          </button>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="text-red-500 text-sm text-center">
          {error}
        </div>
      )}
    </div>
  );
};

export default AvatarUpload;
