/**
 * 唯一ID生成工具
 * 解决使用Date.now()可能产生重复ID的问题
 */

/**
 * 生成唯一ID
 * 结合时间戳和随机数，确保唯一性
 * @returns {string} 唯一ID
 */
export const generateUniqueId = () => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 生成简单的唯一ID（仅用于临时对象）
 * @returns {string} 简单唯一ID
 */
export const generateSimpleId = () => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * 生成基于时间戳的ID（用于需要排序的场景）
 * @returns {number} 时间戳ID
 */
export const generateTimestampId = () => {
  return Date.now();
};

/**
 * 生成递增ID（用于需要连续ID的场景）
 */
let incrementId = 0;
export const generateIncrementId = () => {
  return ++incrementId;
};

/**
 * 重置递增ID计数器
 */
export const resetIncrementId = () => {
  incrementId = 0;
};
