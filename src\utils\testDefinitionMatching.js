// 释义配对匹配测试工具
// 用于测试中英文释义是否按词性正确配对

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试释义配对匹配
const testDefinitionMatching = async () => {
  console.log('🧪 测试释义配对匹配...');
  console.log('='.repeat(50));
  
  const testWords = ['water', 'doing', 'nothing', 'school'];
  const results = {};
  
  for (const word of testWords) {
    try {
      console.log(`\n📖 测试单词: ${word}`);
      console.log('-'.repeat(30));
      
      const result = await getWordDetails(word, 'ecdict');
      
      if (result && !result.notFound) {
        console.log(`✅ ${word} 查询成功`);
        console.log(`📊 数据源: ${result.source}`);
        
        if (result.definitionPairs && result.definitionPairs.length > 0) {
          console.log(`🔗 配对释义数量: ${result.definitionPairs.length}`);
          
          result.definitionPairs.forEach((pair, index) => {
            console.log(`\n  ${pair.index}. 配对释义:`);
            console.log(`     🏷️  词性: ${pair.pos || '未知'}`);
            console.log(`     🇺🇸 英文: ${pair.english}`);
            console.log(`     🇨🇳 中文: ${pair.chinese}`);
            
            // 检查词性匹配
            const englishPos = pair.english.match(/^([a-z]+\.?)\s+/);
            const chinesePos = pair.chinese.match(/^([a-z]+\.?)\s+/);
            
            if (englishPos && chinesePos) {
              const engPos = englishPos[1];
              const chiPos = chinesePos[1];
              
              if (engPos === chiPos) {
                console.log(`     ✅ 词性匹配: ${engPos}`);
              } else {
                console.log(`     ❌ 词性不匹配: 英文(${engPos}) vs 中文(${chiPos})`);
              }
            } else {
              console.log(`     ⚠️  无法检测词性匹配`);
            }
          });
          
          results[word] = {
            success: true,
            pairsCount: result.definitionPairs.length,
            hasPairs: true,
            pairs: result.definitionPairs
          };
        } else {
          console.log('❌ 没有配对释义数据');
          
          results[word] = {
            success: true,
            pairsCount: 0,
            hasPairs: false
          };
        }
      } else {
        console.log(`❌ ${word} 查询失败`);
        console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
        
        results[word] = {
          success: false,
          error: result?.error || '未找到该单词'
        };
      }
    } catch (error) {
      console.log(`❌ ${word} 查询出错: ${error.message}`);
      results[word] = {
        success: false,
        error: error.message
      };
    }
  }
  
  // 统计结果
  console.log('\n📊 配对匹配统计:');
  console.log('='.repeat(50));
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const pairsCount = Object.values(results).filter(r => r.hasPairs).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`✅ 成功查询: ${successCount}/${totalCount}`);
  console.log(`🔗 有配对释义: ${pairsCount}/${totalCount}`);
  
  // 检查词性匹配情况
  let totalPairs = 0;
  let matchedPairs = 0;
  
  Object.entries(results).forEach(([word, result]) => {
    if (result.success && result.hasPairs) {
      console.log(`\n📝 ${word} 配对分析:`);
      
      result.pairs.forEach((pair, index) => {
        totalPairs++;
        
        const englishPos = pair.english.match(/^([a-z]+\.?)\s+/);
        const chinesePos = pair.chinese.match(/^([a-z]+\.?)\s+/);
        
        if (englishPos && chinesePos) {
          const engPos = englishPos[1];
          const chiPos = chinesePos[1];
          
          if (engPos === chiPos) {
            matchedPairs++;
            console.log(`  ✅ ${pair.index}. ${engPos} - 匹配`);
          } else {
            console.log(`  ❌ ${pair.index}. 英文(${engPos}) vs 中文(${chiPos}) - 不匹配`);
          }
        } else {
          console.log(`  ⚠️  ${pair.index}. 无法检测词性`);
        }
      });
    }
  });
  
  if (totalPairs > 0) {
    const matchRate = (matchedPairs / totalPairs * 100).toFixed(1);
    console.log(`\n🎯 词性匹配率: ${matchedPairs}/${totalPairs} (${matchRate}%)`);
  }
  
  return results;
};

// 测试特定单词的详细配对
const testSpecificWordMatching = async (word) => {
  console.log(`🧪 测试单词 "${word}" 的详细配对匹配...`);
  console.log('='.repeat(50));
  
  try {
    const result = await getWordDetails(word, 'ecdict');
    
    if (result && !result.notFound) {
      console.log('✅ 查询成功！');
      console.log(`📖 单词: ${result.word}`);
      console.log(`🔊 音标: ${result.phonetic || '无'}`);
      console.log(`📊 数据源: ${result.source}`);
      
      if (result.definitionPairs && result.definitionPairs.length > 0) {
        console.log(`\n🔗 配对释义详细分析 (${result.definitionPairs.length} 个):`);
        console.log('-'.repeat(50));
        
        result.definitionPairs.forEach((pair, index) => {
          console.log(`\n${pair.index}. 配对释义:`);
          console.log(`   词性标签: ${pair.pos || '未知'}`);
          console.log(`   英文释义: ${pair.english}`);
          console.log(`   中文释义: ${pair.chinese}`);
          
          // 检查词性匹配
          const englishPos = pair.english.match(/^([a-z]+\.?)\s+/);
          const chinesePos = pair.chinese.match(/^([a-z]+\.?)\s+/);
          
          if (englishPos && chinesePos) {
            const engPos = englishPos[1];
            const chiPos = chinesePos[1];
            
            if (engPos === chiPos) {
              console.log(`   ✅ 词性匹配: ${engPos}`);
            } else {
              console.log(`   ❌ 词性不匹配: 英文(${engPos}) vs 中文(${chiPos})`);
            }
          } else {
            console.log(`   ⚠️  无法检测词性匹配`);
          }
        });
        
        return result;
      } else {
        console.log('❌ 没有配对释义数据');
        return null;
      }
    } else {
      console.log('❌ 查询失败');
      console.log(`📝 错误: ${result?.error || '未找到该单词'}`);
      return null;
    }
  } catch (error) {
    console.error('❌ 查询过程中出现错误:', error);
    return null;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始释义配对匹配测试...');
  console.log('='.repeat(60));
  
  try {
    // 测试释义配对匹配
    const results = await testDefinitionMatching();
    
    // 测试特定单词匹配
    console.log('\n🔍 详细测试 "water" 单词配对:');
    await testSpecificWordMatching('water');
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    return results;
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testDefinitionMatching,
  testSpecificWordMatching,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testDefinitionMatching = {
    testDefinitionMatching,
    testSpecificWordMatching,
    runAllTests
  };
  
  console.log('🧪 释义配对匹配测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testDefinitionMatching.runAllTests() // 运行所有测试');
  console.log('  - window.testDefinitionMatching.testDefinitionMatching() // 测试配对匹配');
  console.log('  - window.testDefinitionMatching.testSpecificWordMatching("water") // 测试特定单词');
}