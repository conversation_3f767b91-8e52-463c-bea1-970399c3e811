/**
 * 简化版用量监控测试工具
 * 用于调试导入问题
 */

console.log('🧪 开始加载简化版用量监控测试工具...');

// 测试基本功能
const testBasicFunction = () => {
  console.log('✅ 基本函数测试通过');
  return true;
};

// 尝试导入服务
let userSettingsService = null;
let authService = null;

try {
  console.log('📦 尝试导入用户设置服务...');
  import('../services/user/userSettingsService.js').then(module => {
    console.log('✅ 用户设置服务导入成功');
    userSettingsService = module;
    
    // 暴露到全局对象
    window.userSettingsServiceModule = module;
    console.log('🌐 用户设置服务已暴露到全局对象');
  }).catch(error => {
    console.error('❌ 用户设置服务导入失败:', error);
  });
} catch (error) {
  console.error('❌ 用户设置服务导入异常:', error);
}

try {
  console.log('📦 尝试导入认证服务...');
  import('../services/auth/authService.js').then(module => {
    console.log('✅ 认证服务导入成功');
    authService = module;
    
    // 暴露到全局对象
    window.authServiceModule = module;
    console.log('🌐 认证服务已暴露到全局对象');
  }).catch(error => {
    console.error('❌ 认证服务导入失败:', error);
  });
} catch (error) {
  console.error('❌ 认证服务导入异常:', error);
}

// 简化的系统配置测试
const testSystemConfigSimple = async () => {
  console.log('🧪 开始简化系统配置测试...');
  
  try {
    if (!window.userSettingsServiceModule) {
      console.log('❌ 用户设置服务模块未加载');
      return false;
    }
    
    const { getSystemConfig } = window.userSettingsServiceModule;
    if (!getSystemConfig) {
      console.log('❌ getSystemConfig 函数不存在');
      return false;
    }
    
    console.log('📞 调用 getSystemConfig...');
    const config = await getSystemConfig();
    
    console.log('✅ 系统配置获取成功:', config);
    return true;
    
  } catch (error) {
    console.error('❌ 简化系统配置测试失败:', error);
    return false;
  }
};

// 获取当前用户ID
const getCurrentUserIdSimple = () => {
  try {
    if (!window.authServiceModule) {
      console.log('❌ 认证服务模块未加载');
      return null;
    }
    
    const { getCurrentUser } = window.authServiceModule;
    if (!getCurrentUser) {
      console.log('❌ getCurrentUser 函数不存在');
      return null;
    }
    
    const user = getCurrentUser();
    return user ? user.uid : null;
  } catch (error) {
    console.error('❌ 获取用户ID失败:', error);
    return null;
  }
};

// 运行所有简化测试
const runSimpleTests = async () => {
  console.log('🚀 开始运行简化测试...');
  console.log('='.repeat(50));
  
  // 等待模块加载
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const results = {};
  
  // 测试1: 基本功能
  console.log('1️⃣ 测试基本功能...');
  results.basicFunction = testBasicFunction();
  
  // 测试2: 获取用户ID
  console.log('2️⃣ 测试获取用户ID...');
  const userId = getCurrentUserIdSimple();
  results.userId = !!userId;
  console.log('用户ID:', userId);
  
  // 测试3: 系统配置
  console.log('3️⃣ 测试系统配置...');
  results.systemConfig = await testSystemConfigSimple();
  
  console.log('='.repeat(50));
  console.log('📊 简化测试结果:', results);
  
  return results;
};

// 暴露到全局对象
if (typeof window !== 'undefined') {
  window.simpleUsageTest = {
    runSimpleTests,
    testSystemConfigSimple,
    getCurrentUserIdSimple,
    testBasicFunction
  };
  
  console.log('🌐 简化测试工具已暴露到 window.simpleUsageTest');
  
  // 自动运行测试（延迟执行）
  setTimeout(() => {
    console.log('⏰ 自动运行简化测试...');
    runSimpleTests();
  }, 2000);
}

console.log('✅ 简化版用量监控测试工具加载完成');
