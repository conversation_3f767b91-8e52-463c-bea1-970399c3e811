import React, { useState, useEffect } from 'react';
import { Search, Volume2, ExternalLink, Trash2 } from 'lucide-react';
import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';
import { getSearchHistory, addSearchToHistory, clearSearchHistory } from '../services/writing/historyService';

const DictionaryLookup = ({ word, onClose, isDarkMode, dictionaryService }) => {
  const [searchTerm, setSearchTerm] = useState(word || '');
  const [wordData, setWordData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [history, setHistory] = useState([]);

  // 加载历史记录
  useEffect(() => {
    setHistory(getSearchHistory());
  }, []);

  // 当组件挂载或 word 属性变化时查询单词
  useEffect(() => {
    if (word) {
      lookupWord(word);
    }
  }, [word]);

  // 查询单词
  const lookupWord = async (term) => {
    if (!term.trim()) return;

    setLoading(true);
    setError(null);
    setSearchTerm(term); // 更新搜索框中的内容

    try {
      const data = await getWordDetails(term.trim(), dictionaryService);
      setWordData(data);
      
      // 如果查询成功且不是未找到，则添加到历史记录
      if (data && !data.notFound) {
        addSearchToHistory(term.trim());
        // 刷新历史记录显示
        setHistory(getSearchHistory());
      }
    } catch (err) {
      console.error('查询单词失败:', err);
      setError('无法查询该单词，请检查网络连接或稍后再试');
      setWordData(null); // 清除旧数据
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索表单提交
  const handleSubmit = (e) => {
    e.preventDefault();
    lookupWord(searchTerm);
  };

  // 清空历史记录
  const handleClearHistory = () => {
    clearSearchHistory();
    setHistory([]);
  };

  // 播放发音
  const playAudio = (audioUrl) => {
    if (!audioUrl) return;

    const audio = new Audio(audioUrl);
    audio.play().catch(err => {
      console.error('播放音频失败:', err);
    });
  };

  return (
    <div>
      {/* 搜索栏 */}
      <form onSubmit={handleSubmit} className="mb-6">
        <div className="relative" style={{ marginBottom: '8px' }}>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="输入要查询的单词..."
            className="w-full rounded-xl focus:outline-none"
            style={{
              backgroundColor: 'var(--color-bg-tertiary)',
              color: 'var(--color-text-primary)',
              fontFamily: 'Georgia, "Noto Serif SC", serif',
              letterSpacing: '0.05em',
              padding: '16px 20px 16px 48px',
              fontSize: '16px',
              border: '1px solid var(--color-border)',
              transition: 'all 0.3s ease',
              outline: 'none',
              boxShadow: '0 2px 8px rgba(93, 64, 55, 0.08)',
              borderRadius: '16px'
            }}
            onFocus={(e) => {
              e.target.style.backgroundColor = isDarkMode ? '#3D342A' : '#FFFDF0';
              e.target.style.borderColor = isDarkMode ? '#C4B59A' : '#8B4513';
              e.target.style.boxShadow = isDarkMode
                ? '0 4px 16px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(196, 181, 154, 0.3)'
                : '0 4px 16px rgba(93, 64, 55, 0.15), 0 0 0 1px rgba(139, 69, 19, 0.2)';
              e.target.style.transform = 'translateY(-1px)';
            }}
            onBlur={(e) => {
              e.target.style.backgroundColor = 'var(--color-bg-tertiary)';
              e.target.style.borderColor = 'var(--color-border)';
              e.target.style.boxShadow = '0 2px 8px rgba(93, 64, 55, 0.08)';
              e.target.style.transform = 'translateY(0)';
            }}
          />
          <button
            type="submit"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 p-1 rounded-lg transition-all duration-200 hover:scale-110"
            style={{
              color: 'var(--color-text-secondary)',
              background: 'none',
              border: 'none',
              cursor: 'pointer'
            }}
            title="搜索"
          >
            <Search className="w-5 h-5" />
          </button>
        </div>
      </form>

      {/* 历史记录 */}
      {history.length > 0 && (
        <div className="mb-6 pb-4" style={{ borderBottom: '1px solid #F0E6D2' }}>
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-semibold transition-colors duration-300" style={{ color: 'var(--color-text-secondary)' }}>搜索历史</h4>
            <button
              onClick={handleClearHistory}
              className="delete-btn flex items-center gap-1 text-xs p-1 rounded"
            >
              <Trash2 className="w-3 h-3" />
              清空
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {history.map((item, index) => (
              <button
                key={`history-${item}-${index}`}
                onClick={() => lookupWord(item)}
                className="word-tag-btn"
                style={{
                  backgroundColor: 'var(--color-suggestion-green-bg)',
                  color: 'var(--color-accent-green)'
                }}
              >
                {item}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 transition-colors duration-300" style={{ borderColor: 'var(--color-text-secondary)' }}></div>
        </div>
      )}

      {/* 错误信息 */}
      {error && !loading && (
        <div className="py-4 px-6 rounded-lg transition-colors duration-300" style={{ backgroundColor: 'var(--color-suggestion-grammar-bg)', color: 'var(--color-accent-red)' }}>
          {error}
        </div>
      )}

      {/* 单词数据 */}
      {!loading && !error && wordData && !wordData.notFound && (
        <div>
          {/* 单词标题和音标 */}
          <div className="mb-6">
            <h2 className="text-3xl font-bold transition-colors duration-300" style={{ color: 'var(--color-text-primary)', fontFamily: 'Georgia, "Noto Serif SC", serif' }}>
              {wordData.word}
            </h2>

            {wordData.phonetics && wordData.phonetics.length > 0 && (
              <div className="flex flex-wrap items-center gap-4 mt-2">
                {wordData.phonetics.map((phonetic, index) => (
                  <div key={`phonetic-${index}-${phonetic.text || ''}`} className="flex items-center gap-2">
                    {phonetic.text && (
                      <span className="transition-colors duration-300" style={{ color: 'var(--color-text-secondary)' }}>{phonetic.text}</span>
                    )}
                    {phonetic.audio && (
                      <button
                        onClick={() => playAudio(phonetic.audio)}
                        className="p-1 rounded-full transition-colors duration-200"
                        style={{ color: 'var(--color-accent-green)' }}
                        title="播放发音"
                      >
                        <Volume2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 词义列表 */}
          <div className="space-y-6">
            {wordData.meanings && Array.isArray(wordData.meanings) && wordData.meanings.map((meaning, meaningIndex) => (
              <div key={`meaning-${meaningIndex}-${meaning.partOfSpeech || ''}`} className="pb-4 transition-colors duration-300" style={{ borderBottom: meaningIndex < (wordData.meanings?.length || 0) - 1 ? '1px solid var(--color-border)' : 'none' }}>
                {/* 词性 */}
                <h3 className="text-lg font-semibold mb-3 transition-colors duration-300" style={{ color: 'var(--color-accent-green)', fontFamily: 'Georgia, "Noto Serif SC", serif' }}>
                  {meaning.partOfSpeech}
                </h3>

                {/* 定义列表 */}
                <div className="space-y-4">
                  {meaning.definitions.map((def, defIndex) => (
                    <div key={`def-${meaningIndex}-${defIndex}-${def.definition?.substring(0, 20) || ''}`} className="pl-4 transition-colors duration-300">
                      {/* 定义 */}
                      {def.definition && def.definition.trim() && (
                        <p className="mb-2 transition-colors duration-300" style={{ color: 'var(--color-text-primary)' }}>
                          {defIndex + 1}. {def.definition}
                        </p>
                      )}

                      {/* 中文翻译 */}
                      {def.translation && def.translation.trim() && (
                        <p className="mb-2 transition-colors duration-300" style={{ color: 'var(--color-accent-green)', fontFamily: '"Noto Serif SC", serif' }}>
                          {def.definition && def.definition.trim() ? '' : `${defIndex + 1}. `}{def.translation}
                        </p>
                      )}

                      {/* 例句 */}
                      {def.example && (
                        <p className="mb-2 italic transition-colors duration-300" style={{ color: 'var(--color-text-secondary)' }}>
                          "{def.example}"
                        </p>
                      )}

                      {/* 同义词 */}
                      {def.synonyms && def.synonyms.length > 0 && (
                        <div className="mb-2">
                          <span className="text-sm font-medium transition-colors duration-300" style={{ color: 'var(--color-accent-green)' }}>同义词: </span>
                          <span className="transition-colors duration-300" style={{ color: 'var(--color-text-primary)' }}>
                            {def.synonyms.join(', ')}
                          </span>
                        </div>
                      )}

                      {/* 反义词 */}
                      {def.antonyms && def.antonyms.length > 0 && (
                        <div>
                          <span className="text-sm font-medium transition-colors duration-300" style={{ color: 'var(--color-accent-red)' }}>反义词: </span>
                          <span className="transition-colors duration-300" style={{ color: 'var(--color-text-primary)' }}>
                            {def.antonyms.join(', ')}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* 词义级别的同义词 */}
                {meaning.synonyms && meaning.synonyms.length > 0 && (
                  <div className="mt-4 pt-3 transition-colors duration-300" style={{ borderTop: '1px dashed var(--color-border)' }}>
                    <span className="text-sm font-medium transition-colors duration-300" style={{ color: 'var(--color-accent-green)' }}>同义词: </span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {meaning.synonyms.map((syn, synIndex) => (
                        <button
                          key={synIndex}
                          onClick={() => lookupWord(syn)}
                          className="word-tag-btn"
                          style={{
                            backgroundColor: 'var(--color-suggestion-green-bg)',
                            color: 'var(--color-accent-green)'
                          }}
                        >
                          {syn}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* 词义级别的反义词 */}
                {meaning.antonyms && meaning.antonyms.length > 0 && (
                  <div className="mt-3">
                    <span className="text-sm font-medium transition-colors duration-300" style={{ color: 'var(--color-accent-red)' }}>反义词: </span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {meaning.antonyms.map((ant, antIndex) => (
                        <button
                          key={antIndex}
                          onClick={() => lookupWord(ant)}
                          className="word-tag-btn"
                          style={{
                            backgroundColor: 'var(--color-suggestion-grammar-bg)',
                            color: 'var(--color-accent-red)'
                          }}
                        >
                          {ant}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* 外部链接 */}
          <div className="mt-6 pt-4 transition-colors duration-300" style={{ borderTop: '1px solid var(--color-border)' }}>
            <div className="flex justify-between">
              {dictionaryService === 'ecdict' ? (
                // 英汉字典 - 显示中文词典链接
                <>
                  <a
                    href={`https://dict.youdao.com/w/${encodeURIComponent(wordData.word)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="external-link"
                  >
                    有道词典
                    <ExternalLink className="w-3 h-3" />
                  </a>

                  <a
                    href={`https://fanyi.baidu.com/#en/zh/${encodeURIComponent(wordData.word)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="external-link"
                  >
                    百度翻译
                    <ExternalLink className="w-3 h-3" />
                  </a>

                  <a
                    href={`https://www.iciba.com/word?w=${encodeURIComponent(wordData.word)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="external-link"
                  >
                    金山词霸
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </>
              ) : (
                // 英文字典 - 显示英文词典链接
                <>
                  <a
                    href={`https://www.merriam-webster.com/dictionary/${encodeURIComponent(wordData.word)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="external-link"
                  >
                    Merriam-Webster
                    <ExternalLink className="w-3 h-3" />
                  </a>

                  <a
                    href={`https://www.vocabulary.com/dictionary/${encodeURIComponent(wordData.word)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="external-link"
                  >
                    Vocabulary.com
                    <ExternalLink className="w-3 h-3" />
                  </a>

                  <a
                    href={`https://www.thesaurus.com/browse/${encodeURIComponent(wordData.word)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="external-link"
                  >
                    Thesaurus.com
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 未找到单词 */}
      {!loading && !error && wordData && wordData.notFound && (
        <div className="py-8 text-center">
          <p className="transition-colors duration-300" style={{ color: 'var(--color-text-secondary)', fontFamily: 'Georgia, "Noto Serif SC", serif' }}>
            未找到单词 "{searchTerm}"
          </p>
          <p className="mt-2 text-sm transition-colors duration-300" style={{ color: 'var(--color-text-primary)' }}>
            请检查拼写或尝试其他单词
          </p>
        </div>
      )}
    </div>
  );
};

export default DictionaryLookup;
