import React, { useState } from 'react';
import AuthModal from '../components/auth/AuthModal';
import { ThemedButton } from '../components/themed';

const LoginDemo = () => {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  return (
    <div 
      className={isDarkMode ? 'dark' : ''}
      style={{
        minHeight: '100vh',
        backgroundColor: isDarkMode ? '#1A1611' : '#F5EFE6',
        padding: '40px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '20px',
        transition: 'all 0.3s ease'
      }}
    >
      <h1 
        style={{
          fontSize: '32px',
          fontWeight: '700',
          color: isDarkMode ? '#E8DCC6' : '#5D4037',
          fontFamily: 'Georgia, "Noto Serif SC", serif',
          marginBottom: '20px',
          textAlign: 'center'
        }}
      >
        Studybuddy 登录界面演示
      </h1>
      
      <p 
        style={{
          fontSize: '18px',
          color: isDarkMode ? '#C4B59A' : '#8B4513',
          fontFamily: 'Georgia, "Noto Serif SC", serif',
          textAlign: 'center',
          marginBottom: '40px',
          maxWidth: '600px',
          lineHeight: '1.6'
        }}
      >
        这是改良后的登录界面设计，采用现代化的设计语言，保持了产品的温暖色调，
        增加了更好的用户体验和视觉效果。
      </p>

      <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
        <ThemedButton
          variant="primary"
          onClick={() => setIsAuthModalOpen(true)}
          style={{
            fontSize: '16px',
            padding: '16px 32px',
            background: isDarkMode 
              ? 'linear-gradient(135deg, #D2691E 0%, #B8591A 100%)'
              : 'linear-gradient(135deg, #B91C1C 0%, #991B1B 100%)',
            boxShadow: isDarkMode 
              ? '0 4px 16px rgba(210, 105, 30, 0.3)'
              : '0 4px 16px rgba(185, 28, 28, 0.2)'
          }}
        >
          打开登录界面
        </ThemedButton>

        <ThemedButton
          variant="secondary"
          onClick={() => setIsDarkMode(!isDarkMode)}
          style={{
            fontSize: '16px',
            padding: '16px 32px'
          }}
        >
          {isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
        </ThemedButton>
      </div>

      <div 
        style={{
          marginTop: '40px',
          padding: '24px',
          backgroundColor: isDarkMode ? '#2A241D' : '#FEFCF5',
          borderRadius: '16px',
          border: `1px solid ${isDarkMode ? '#4A3F35' : '#E5E7EB'}`,
          maxWidth: '800px',
          width: '100%'
        }}
      >
        <h3 
          style={{
            fontSize: '20px',
            fontWeight: '600',
            color: isDarkMode ? '#E8DCC6' : '#5D4037',
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            marginBottom: '16px'
          }}
        >
          设计改进亮点：
        </h3>
        
        <ul 
          style={{
            color: isDarkMode ? '#C4B59A' : '#8B4513',
            fontFamily: 'Georgia, "Noto Serif SC", serif',
            lineHeight: '1.8',
            paddingLeft: '20px'
          }}
        >
          <li>🎨 现代化的渐变背景和图标设计</li>
          <li>✨ 流畅的动画效果和过渡</li>
          <li>🔍 增强的输入框焦点状态和交互反馈</li>
          <li>👁️ 密码可见性切换功能</li>
          <li>⚡ 加载状态指示器</li>
          <li>🌙 完美的深色模式支持</li>
          <li>📱 响应式设计和更好的视觉层次</li>
          <li>🎯 保持了产品的温暖色调和品牌一致性</li>
        </ul>
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

export default LoginDemo;
