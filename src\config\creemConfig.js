// Creem 支付系统配置文件
// 管理Creem API密钥、环境配置和产品设置

import { getUserCreemApiKey } from '../services/user/userSettingsService.js';

// Creem API 环境配置
export const CREEM_ENVIRONMENTS = {
  production: {
    baseURL: 'https://api.creem.io',
    name: 'production'
  },
  test: {
    baseURL: 'https://test-api.creem.io',
    name: 'test'
  }
};

// 当前环境 - 默认使用测试环境进行开发
export const CURRENT_ENVIRONMENT = process.env.NODE_ENV === 'production' ? 'production' : 'test';

// 获取Creem API密钥的函数
const getCreemAPIKey = async () => {
  try {
    return await getUserCreemApiKey();
  } catch (error) {
    console.error('获取Creem API密钥失败:', error);
    // 如果从用户设置获取失败，尝试从环境变量获取
    return process.env.VITE_CREEM_API_KEY || '';
  }
};

// Creem API 配置
export const CREEM_CONFIG = {
  // 当前环境配置
  environment: CURRENT_ENVIRONMENT,
  baseURL: CREEM_ENVIRONMENTS[CURRENT_ENVIRONMENT].baseURL,
  
  // 动态获取API密钥
  async getApiKey() {
    return await getCreemAPIKey();
  },
  
  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    maxRetries: 3,  // 最大重试次数
  },
  
  // 产品配置
  products: {
    // 基础版 - 月付
    basic_monthly: {
      name: '基础版 - 月付',
      description: '基础英语写作助手功能，每月订阅',
      features: [
        '基础语法检查',
        '简单风格建议',
        '每月1000次AI分析',
        '基础词典功能'
      ]
    },
    
    // 专业版 - 月付
    pro_monthly: {
      name: '专业版 - 月付',
      description: '专业英语写作助手功能，每月订阅',
      features: [
        '高级语法检查',
        '深度风格优化',
        '无限次AI分析',
        '高级词典功能',
        '抄袭检测',
        '引用帮助',
        '优先客服支持'
      ]
    },
    
    // 专业版 - 年付
    pro_yearly: {
      name: '专业版 - 年付',
      description: '专业英语写作助手功能，年度订阅（享受2个月免费）',
      features: [
        '高级语法检查',
        '深度风格优化',
        '无限次AI分析',
        '高级词典功能',
        '抄袭检测',
        '引用帮助',
        '优先客服支持',
        '年付优惠 - 相当于10个月价格'
      ]
    }
  },
  
  // 用量限制配置
  usageLimits: {
    free: {
      aiAnalysisPerMonth: 10,
      dictionaryLookups: 50
    },
    basic: {
      aiAnalysisPerMonth: 1000,
      dictionaryLookups: -1 // 无限制
    },
    pro: {
      aiAnalysisPerMonth: -1, // 无限制
      dictionaryLookups: -1   // 无限制
    }
  }
};

// 检查Creem API密钥是否已配置
export const isCreemAPIKeyConfigured = async () => {
  try {
    const apiKey = await getCreemAPIKey();
    return apiKey && apiKey.trim().length > 0;
  } catch (error) {
    console.error('检查Creem API密钥配置失败:', error);
    return false;
  }
};

// 获取Creem API配置状态
export const getCreemAPIStatus = async () => {
  const configured = await isCreemAPIKeyConfigured();
  return {
    configured,
    environment: CURRENT_ENVIRONMENT,
    baseURL: CREEM_CONFIG.baseURL
  };
};

// 获取当前用户的订阅计划
export const getUserSubscriptionPlan = (subscription) => {
  if (!subscription || subscription.status !== 'active') {
    return 'free';
  }
  
  // 根据产品ID判断订阅计划
  const productId = subscription.product?.id || subscription.product_id;
  if (!productId) return 'free';
  
  // 这里需要根据实际的Creem产品ID来判断
  // 暂时使用产品名称或描述来判断
  const productName = subscription.product?.name?.toLowerCase() || '';
  
  if (productName.includes('pro') || productName.includes('专业')) {
    return 'pro';
  } else if (productName.includes('basic') || productName.includes('基础')) {
    return 'basic';
  }
  
  return 'free';
};

// 检查用户是否有权限使用某个功能
export const checkFeatureAccess = (userPlan, feature) => {
  const limits = CREEM_CONFIG.usageLimits[userPlan] || CREEM_CONFIG.usageLimits.free;
  
  switch (feature) {
    case 'aiAnalysis':
      return limits.aiAnalysisPerMonth === -1 || limits.aiAnalysisPerMonth > 0;
    case 'dictionaryLookup':
      return limits.dictionaryLookups === -1 || limits.dictionaryLookups > 0;
    case 'plagiarismCheck':
      return userPlan === 'pro';
    case 'citationHelp':
      return userPlan === 'pro';
    default:
      return true;
  }
};

// 获取功能使用限制
export const getFeatureLimit = (userPlan, feature) => {
  const limits = CREEM_CONFIG.usageLimits[userPlan] || CREEM_CONFIG.usageLimits.free;
  
  switch (feature) {
    case 'aiAnalysis':
      return limits.aiAnalysisPerMonth;
    case 'dictionaryLookup':
      return limits.dictionaryLookups;
    default:
      return -1; // 无限制
  }
};

export default CREEM_CONFIG;
