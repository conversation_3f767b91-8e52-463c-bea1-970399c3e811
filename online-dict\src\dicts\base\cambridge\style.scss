.pos-body h3.bb::after{
  display: none;
}

.pos-body .eg{
  color: var(--color-font) !important;
}

.dictCambridge-Header {
  display: flex;
  align-items: baseline;
}

.dictCambridge-Title {
  font-size: 1.5em;
  margin-right: 0.5em;
}

.dictCambridge-Entry {
  margin-bottom: 1em;

  a {
    color: var(--color-font) !important;
    text-decoration: none !important;
  }
}

#d-cambridge-entry-related {
  ul {
    padding-left: 1em;

    li {
      list-style-type: disc;

      a {
        color: var(--color-theme);
      }
    }
  }
}

.dimg {
  text-align: center;

  img {
    display: inline-block;
  }
}

.inline {
  margin-left: 0;
  list-style: none;
}

.inline li {
  display: inline;
  margin: 0 15px 0 0;
}

.unstyled,
.unstyled-nest li ul {
  margin-left: 0;
  padding: 0;
  list-style: none;
}

.unstyled li {
  margin: 0 0 5px 0;
}

.unstyled-nest li ul {
  margin-top: 5px;
}

.link-list {
  margin: 5px 0 10px;
}

.link-list a {
  font-weight: 700;
  text-decoration: none;
}

.link-list a:hover {
  text-decoration: underline;
}

.divided li {
  margin: 0;
  padding: 20px 5px;
  border-bottom: solid 1px #e3e3e8;
}

.divided li:first-child {
  padding-top: 10px;
}

.divided li:last-child {
  border-width: 0;
}

.checklist {
  margin-left: 0;
  padding-left: 25px;
  list-style: none;
}

.checklist li:before {
  font-weight: 300;
  content: '\f00c';
  position: absolute;
  margin-left: -25px;
  color: #d1a14c;
}

.tiles__tile {
  float: left;
  width: 50%;
  padding: 0 2px;
}

.tiles__tile label {
  float: left;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px 0;
  background: #fff;
  text-align: center;
  border: solid 3px #e0e0e5;
  cursor: pointer;
  position: relative;
}

.tiles__tile input[type='radio'],
.tiles__tile input[type='checkbox'] {
  display: none;
}

.tiles__tile input[type='radio']:checked + label,
.tiles__tile input[type='checkbox']:checked + label {
  background: #d0a44c;
  border-color: #d0a44c;
}

.tiles__tile input[type='radio']:checked + label:before,
.tiles__tile input[type='checkbox']:checked + label:before {
  content: '\f00c';
  font-weight: 300;
  padding-right: 7px;
  color: #fff;
  font-size: 0.9em;
}

.form .tiles {
  margin-bottom: 0;
}

.with-el {
  position: relative;
}

.with-el__el {
  position: absolute;
  top: 0;
  right: 0;
}

.with-el__el--l {
  right: auto;
  left: 0;
}

.with-el__el--b {
  top: auto;
  bottom: 0;
}

.with-el__el--icons {
  top: -5px;
  vertical-align: 0;
}

.with-icons__content {
  display: inline-block;
  padding: 5px 0;
}

.with-icons__icons {
  float: right;
}

.trend {
  position: relative;
  display: inline-block;
  vertical-align: 1px;
  color: rgba(36, 46, 78, 0.65);
  text-transform: uppercase;
  font-weight: 700;
  font-size: 0.6em;
}

.trend i {
  color: #0096ac;
  font-size: 1.75em;
}

.trend--down i {
  color: #e84427;
}

.prefix {
  display: inline-block;
  margin-right: 5px;
  width: 40px;
  color: #a9b3d0;
  font-size: 2em;
  vertical-align: -8px;
  text-align: center;
}

.divided .prefix {
  margin-left: -5px;
}

.prefix-float .prefix {
  float: left;
  display: block;
}

.prefix-float .prefix-item {
  overflow: hidden;
  display: block;
}

.prefix-block > * {
  position: relative;
  margin: 0 0 5px;
  padding: 10px 10px 10px 50px;
  background: #eef1f5;
}

.prefix-block .prefix {
  position: absolute;
  top: 0;
  left: 0;
  padding-top: 10px;
  height: 100%;
  font-size: 1em;
  color: #fff;
}

.progress {
  position: relative;
  width: 95px;
  height: 95px;
}

.progress svg {
  position: absolute;
  top: 0;
  left: 0;
}

.progress__indicator {
  position: relative;
  border: solid 8px #ccd2e1;
  width: 100%;
  height: 100%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

.progress__indicator__done {
  background: #d0a44c;
}

.progress__label {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 40px;
  line-height: 40px;
  width: 65px;
  margin: -20px 0 0 -32.5px;
  text-align: center;
  font-size: 1.5em;
  color: #848fae;
}

.cycler {
  position: relative;
  padding: 0 20px;
  background: #11326f;
  color: #fff;
}

.cycler > div {
  overflow: hidden;
  width: 100%;
  padding: 0 20px 10px;
}

.cycler__nav {
  position: absolute;
  top: 10px;
  left: 7px;
  height: 40px;
  line-height: 32px;
  padding: 0 15px;
  text-align: center;
  color: #fff;
  font-size: 2.5em;
}

.cycler__nav--next {
  left: auto;
  right: 7px;
}

.cycler__items {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
}

.cycler__items > * {
  white-space: normal;
  display: inline-block;
  width: 20%;
  text-align: center;
}

.cycler__items.unstyled > li {
  margin: 0;
}

.cycler__items a {
  color: #acb7c5;
  font-size: 2.8125em;
  line-height: 1.3em;
}

.cycler__items a.on,
.cycler__items .on a,
.cycler__items a:hover {
  color: #fff;
}

.contain--pad {
  padding: 15px 0;
}

.nocontain {
  margin: 0 -10px;
}

h1.hw,
.h1.hw {
  margin-bottom: 10px;
  font-size: 1em;
  font-weight: 700;
}

.definition-src {
  margin-bottom: 40px;
}

.di {
  margin-bottom: 10px;
}

.entry-body__el--smalltop {
  padding-top: 10px;
}

.entry-box__el:last-child,
.entry-body__el:last-child {
  margin-bottom: 10px;
}

.mod.entry h3,
.mod.entry .h3 {
  font-size: 1em;
}

.di-head {
  padding: 12px 20px;
  border-left: solid 1px #e6e6eb;
  border-right: solid 1px #e6e6eb;
}

.di-head h2,
.di-head .h2 {
  font-size: 1em;
}

.di-head .see-all-translations {
  margin-left: 1px;
  display: table;
  line-height: 22px;
}

.di-title {
  font-size: 1.2em;
  font-weight: bold;
  line-height: 1.3;
  // border-bottom: 1px solid currentColor;
}

.normal-entry .di-title {
  line-height: 1.3em;
}

.di-head.normal-entry {
  position: relative;
  background: #fff;
  margin-bottom: 20px;
  padding: 0;
  border-width: 0;
}

.di .pos-header {
  position: relative;
  margin: 5px 0 15px;
  color: rgba(17, 50, 111, 0.91);
}

.di .irreg-infls,
.di .inf {
  color: #292929;
}

.di .pos-head .pos-info {
  margin: 0 0 20px;
}

.cdo-section-title-hw {
  display: inline;
  word-wrap: break-word;
}

.cdo-section-title-hw .headword,
.di-head.normal-entry .cdo-section-title-hw {
  display: block;
  margin: 0 0 10px;
  font-size: 2.5em;
  line-height: 1.075em;
  font-weight: 700;
}

.di-head.normal-entry .cdo-section-title-hw {
  margin: 0;
}

.cdo-section-title-hw .posgram,
.di-head.normal-entry .posgram,
.pos-head .pos-info .posgram,
.HeadwordCtn .GeographicalUsage {
  font-style: italic;
  // font-size: 1.25em;
  color: #444;
}

.gcs {
  vertical-align: -1px;
}

.pos-head .pos-info .posgram {
  margin-right: 5px;
}

.pos-head .pos-info .pron,
.di-head.normal-entry .pron {
  margin-left: 5px;
}

.freq,
.epp-xref {
  margin-right: 3px;
  padding: 2px 5px;
  color: #fff;
  font-weight: 700;
  font-size: 0.8em;
  min-width: 14px;
  text-align: center;
  background-color: #444;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
}

.freq {
  display: none;
}

.cdo-topic {
  font-weight: 700;
}

.extraexamps li[class='eg'] {
  position: relative;
  margin-left: 1.2em;
  list-style-type: disc;
}

.runon-info .posgram .pos {
  color: #555;
}

.i {
  font-style: italic;
}

.lab {
  display: inline;
  font-variant: small-caps;
}

.lu {
  font-weight: 700;
}

.uk,
.us,
.superentry .irreg-infls {
  margin-left: 5px;
}

.uk > .region,
.us > .region {
  text-transform: uppercase;
  color: #e84427;
  font-size: 1em;
  font-weight: 700;
}

.superentry .pron {
  font-size: 1.063em;
}

.sense-block .hw,
.sense-block .phrase {
  font-weight: 700;
  font-size: 1.1em;
}

.sense-block .pos {
  font-style: italic;
}

.gram {
  color: #555;
  margin-right: 3px;

  a {
    color: inherit;
  }
}

.sense-block .guideword {
  margin-left: 8px;
}

.sense-block .guideword span {
  vertical-align: -1px;
}

.emphasized .gram {
  font-style: normal;
}

.pos-head .pos-info .fcdo {
  font-size: 14px;
  vertical-align: -2px;
}

.fav-entry {
  width: 22px;
  height: 22px;
}

.fav-entry .fcdo {
  line-height: 22px;
}

.def-block {
  position: relative;
}

.def-block .fav-entry {
  position: absolute;
  top: 2px;
  left: 0;
}

.phrase-block .def-block .fav-entry {
  top: 4px;
}

.entry-divide {
  position: relative;
  border-top: solid 1px #e6e6eb;
  border-bottom: solid 1px #e6e6eb;
  height: 20px;
}

.results.link-list a {
  font-weight: 400;
}

.results.link-list em {
  font-style: italic;
  color: #242b4e;
}

.results.link-list a:hover {
  text-decoration: none;
}

.results.link-list a:hover b {
  text-decoration: underline;
}

.feature-w,
.feature-w-big {
  font-size: 1.25em;
  line-height: 1em;
  font-weight: 400;
}

.feature-w-big {
  font-size: 2.2em;
  line-height: 0.9em;
}

.resp {
  display: none;
}

.resp.open {
  display: block;
}

.oflow-hide {
  overflow: hidden;
}

.center {
  text-align: center;
}

.left {
  text-align: left;
}

.right {
  text-align: right;
}

.lower {
  text-transform: lowercase;
}

.upper {
  text-transform: uppercase;
}

.clr {
  clear: both;
}

.clr-left {
  clear: left;
}

.clr-right {
  clear: right;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.title {
  padding-bottom: 5px;
  border-bottom: solid 1px #e6e6eb;
}

.fade {
  opacity: 0.7;
}

.hide {
  display: none;
}

.hide.open {
  display: block;
}

.hide-txt {
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
}

.hidden {
  visibility: hidden;
}

.flush,
div.flush {
  margin-bottom: 0;
}

.tight,
.semi-flush {
  margin-bottom: 5px;
}

.nudge-top {
  margin-top: 2px;
}

.normal-top {
  margin-top: 15px;
}

.normal-base {
  margin-bottom: 15px;
}

.space-top {
  margin-top: 5px;
}

.space-base {
  margin-bottom: 10px;
}

.space-both {
  margin-top: 5px;
  margin-bottom: 10px;
}

.spaced {
  margin-bottom: 20px;
}

.spaced-top {
  margin-top: 20px;
}

.spaced-out {
  margin: 5px 0 25px;
}

.spaced-big {
  margin-bottom: 30px;
}

.spaced-big-top {
  margin-top: 30px;
}

.pad {
  padding: 0 5px;
}

.pad-indent {
  padding-left: 1em;
}

.pad-indent-both {
  padding-left: 1em;
  padding-right: 1em;
}

.pad-all {
  padding: 1em;
}

.pad-all-sml {
  padding: 0.5em;
}

.pad-extra {
  padding: 0 0.5em;
}

.pad-l-flush {
  padding-left: 0;
}

.pad-l-sml {
  padding-left: 5px;
}

.pad-l {
  padding-left: 10px;
}

.pad-l-lrg {
  padding-left: 15px;
}

.pad-r-flush {
  padding-right: 0;
}

.pad-r-sml {
  padding-right: 5px;
}

.pad-r {
  padding-right: 10px;
}

.pad-r-lrg {
  padding-right: 15px;
}

.pad-t-flush {
  padding-top: 0;
}

.pad-t-sml {
  padding-top: 5px;
}

.pad-t {
  padding-top: 10px;
}

.pad-t-lrg {
  padding-top: 15px;
}

.pad-b-flush {
  padding-bottom: 0;
}

.pad-b-sml {
  padding-bottom: 5px;
}

.pad-b {
  padding-bottom: 10px;
}

.pad-b-lrg {
  padding-bottom: 15px;
}

.pad-sides {
  padding-left: 10px;
  padding-right: 10px;
}

.pad-sides-sml {
  padding-left: 5px;
  padding-right: 5px;
}

.underline {
  text-decoration: underline;
}

.fig-frame {
  width: 100%;
  text-align: center;
}

.fig-frame img {
  border: solid 6px #fff;
}

.leader {
  font-size: 1.25em;
  line-height: 1.4em;
}

.meta {
  color: #686868;
}

.standout {
  color: #242e4e;
}

.pointer {
  cursor: pointer;
}

.small {
  font-size: 0.875em;
}

.smaller {
  font-size: 0.8em;
}

.bigger {
  font-size: 1.125em;
}

.light {
  color: #888;
}

.bg-h:after {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  z-index: 1;
}

.accessibility {
  overflow: hidden;
  position: absolute;
  top: -9999px;
  left: -9999px;
  float: none;
  width: auto;
  margin: 0;
  padding: 0;
}

.contain:before,
.clrd:before,
.tabs:before,
.cdo-search:before,
.stacks:before,
.tiles:before,
.tabs__content > .block-wrap:before {
  content: ' ';
  display: table;
}

.contain:after,
.clrd:after,
.tabs:after,
.cdo-search:after,
.stacks:after,
.tiles:after,
.tabs__content > .block-wrap:after {
  content: ' ';
  display: table;
  clear: both;
}

.dropdown__box {
  z-index: 9000;
}

.site-msg {
  z-index: 9999;
}

.english-french .pad-indent .runon.pad-indent,
.french-english .pad-indent .runon.pad-indent {
  margin-left: -30px;
}

.english-french .runon-body.pad-indent,
.french-english .runon-body.pad-indent {
  margin-left: -20px;
}

.relativDiv {
  position: relative;
}

.divBlock {
  display: block;
}

.img-thumb {
  margin-bottom: 10px;
}

a.a--b {
  font-weight: bold !important;
}

a.a--rev,
a.a--none {
  text-decoration: none !important;
}

a.a--rev:hover {
  text-decoration: underline !important;
}

label,
.label {
  display: block;
  margin: 0;
  padding: 11px 0;
  font-weight: bold;
}

input[type='text'],
input[type='email'],
input[type='password'],
input.text,
textarea,
select {
  padding: 11px;
  width: 100%;
  border: 1px solid #ddd;
  background: #f1f1f1;
  box-sizing: border-box;
  border-radius: 2px;
  box-shadow: inset 1px 1px 2px 0 rgba(0, 0, 0, 0.1);
  color: #444;
}

input[type='text'],
input[type='email'],
input[type='password'],
input.text {
  height: 44px;
}

.input-wrap {
  display: inline-block;
  width: 100%;
  vertical-align: bottom;
}

.form div em {
  display: block;
  margin: 5px 0 20px;
  color: var(--color-font-grey);
}

.form > div {
  clear: both;
  margin-bottom: 10px;
}

.form input.btn {
  margin: 20px 0 10px;
}

.form__inline label {
  float: none;
  display: inline;
  padding: 0 10px 0 0;
  font-weight: normal;
}

.form__inline input {
  float: left;
  clear: left;
  margin: 3px 5px 5px 0;
  width: auto;
  border: 0 none;
}

.form__list {
  display: inline-block;
  line-height: 1.3em;
  padding-top: 11px;
}

.form--restrict input[type='text'],
.form--restrict input[type='email'],
.form--restrict input[type='password'],
.form--restrict input.text,
.form--restrict textarea,
.form--restrict select {
  max-width: 450px;
}

.text--ico-key input.text,
.text--ico-key textarea,
.text--ico-key select {
  padding-right: 40px;
}

.csstransforms3d .point {
  display: block !important;
  position: absolute;
  top: 0;
  left: 0;
  height: 10px;
  width: 10px;
  background: #fff;
  -moz-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.csstransforms3d .tiles--pointer input[type='radio']:checked + label:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  height: 16px;
  width: 16px;
  margin-left: -8px;
  background: #d0a44c;
  -moz-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.btn {
  display: inline-block;
  padding: 10px 12px;
  text-align: center;
  color: #fff;
  text-decoration: none;
  line-height: 1em;
  cursor: pointer;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

.btn--impact {
  background: #caa54c;
  border-color: #caa54c;
  color: #111;
  font-weight: bold;
}

.btn--impact:hover {
  background: #b79441;
}

.btn--impact2 {
  padding: 11px 13px;
  border-width: 0;
  background: rgba(0, 0, 0, 0.34);
  font-weight: bold;
}

.btn--impact2:hover {
  background: rgba(0, 0, 0, 0.45);
}

.btn--alt {
  background: #dde2f0;
  border-color: #dde2f0;
}

.btn--alt:hover {
  background: #c7cee2;
}

.btn--white {
  background: #fff;
  border-color: #fff;
}

.btn--white:hover {
  background: #f4f4f4;
}

.btn--white,
.btn--alt,
.btn--alt2 {
  color: #292929;
}

.btn--lrg {
  font-size: 1.1em;
  font-weight: bold;
  padding: 12px 20px;
}

.btn--small {
  font-size: 0.875em;
}

.btn--bold,
input.btn {
  font-weight: bold;
}

.btn--dropdown {
  padding-right: 30px;
}

.btn--dropdown-pad {
  padding-right: 40px;
}

.btn--dropdown:after {
  position: absolute;
  content: '\f078';
  top: 50%;
  right: 10px;
  margin-top: -8px;
  font-size: 16px;
  line-height: 1em;
  color: #d0a44c;
  font-weight: 300;
}

.btn--dropdown.on:after {
  content: '\f077';
}

.btn--options {
  font-size: 0.8em;
  padding: 10px 30px 10px 10px;
  background: #dde2f0;
  color: #292929;
  border: 0;
  border-radius: 0;
}

.btn--options:hover {
  background: #dde2f0;
}

.btn--options:after {
  font-size: 0.9em;
  margin-top: -5px;
}

.btn--plus.on {
  border-radius: 2px 2px 0 0;
}

.btn--plus.on .fcdo:before {
  content: '\f068';
}

.btn--input {
  height: 44px;
  padding: 12px 16px;
  border-radius: 0 2px 2px 0;
}

.btn--input--nudge {
  padding-bottom: 13px;
}

.btn--translate:before {
  content: ' ';
  position: absolute;
}

.btn--translate {
  position: relative;
  padding: 12px 12px 12px 43px;
}

.btn--translate:before {
  width: 31px;
  height: 31px;
  top: 4px;
  left: 10px;
  background-position: -550px 0;
}

.btn--social {
  display: block;
  border: 0;
  padding: 12px;
  margin: 0 auto 10px;
  font-weight: bold;
  max-width: 300px;
}

.btn--social .fcdo {
  font-size: 1.2em;
  padding-right: 5px;
}

.btn .fcdo {
  color: inherit;
}

.btn--alt .fcdo {
  color: #354da5;
}

.btn--small .fcdo {
  font-size: 1.14286em;
}

.btn--ico-l {
  position: relative;
  padding-left: 33px;
}

.btn--ico-l--extra-pad {
  padding-left: 40px;
}

.btn--ico-l .fcdo {
  position: absolute;
  top: 50%;
  left: 10px;
  height: 100%;
  margin-top: -12px;
  line-height: 100%;
  font-size: 22px;
}

.btn--ico-l .fcdo-quiz {
  left: 10px;
  margin-top: -13px;
  font-size: 26px;
}

.cols,
.cols__col {
  box-sizing: border-box;
}

.cols .cols__col:first-child {
  margin-left: 0;
}

.cols--icons .cols__col {
  padding: 70px 0 20px;
}

.txt-block {
  display: block;
  font-weight: normal;
  box-sizing: border-box;
  text-decoration: none;
  border-bottom: 1px solid rgba(199, 110, 6, 0.5);
}

.txt-block--shallow {
  padding: 4px 20px;
}

.txt-block--padder {
  padding: 15px 20px;
}

.txt-block--alt3 {
  background: #e84427;
  color: #fff;
}

.txt-block--impact {
  background: #d0a44c;
  color: #111;
}

.txt-block--padl {
  padding-left: 70px;
}

.txt-block--padr {
  padding-right: 70px;
}

.txt-block--alt h2,
.txt-block--alt h3,
.txt-block--alt h4,
.txt-block--alt h5,
.txt-block--alt2 h2,
.txt-block--alt2 h3,
.txt-block--alt2 h4,
.txt-block--alt2 h5,
.txt-block--alt .h2,
.txt-block--alt .h3,
.txt-block--alt .h4,
.txt-block--alt .h5,
.txt-block--alt2 .h2,
.txt-block--alt2 .h3,
.txt-block--alt2 .h4,
.txt-block--alt2 .h5 {
  color: inherit;
}

.txt-block--alt h3 span,
.txt-block--alt .h3 span {
  color: #a7b5c9;
}

a.txt-block:hover span {
  text-decoration: underline;
}

a.txt-block {
  font-weight: bold;
}

a.txt-block--impact .fcdo {
  color: #303076;
}

a.txt-block:hover {
  opacity: 0.9;
}

.txt-block .with-el__el {
  top: 13px;
  right: 20px;
}

.txt-block .with-el__el--icons {
  top: 8px;
}

.txt-block.with-icons {
  padding: 8px 20px;
}

.txt-block.item-tag {
  position: relative;
}

.txt-block.item-tag h2,
.txt-block.item-tag h3,
.txt-block.item-tag h4,
.txt-block.item-tag h5,
.txt-block.item-tag .h2,
.txt-block.item-tag .h3,
.txt-block.item-tag .h4,
.txt-block.item-tag .h5,
.txt-block.item-tag p {
  margin-bottom: 0;
}

.txt-block .item-tag__tag--clear {
  background: transparent;
}

.cols__col--product {
  position: relative;
}

.cols__col--product-img {
  height: 100px;
  margin: 0 0 15px;
  position: absolute;
  top: 0;
  left: 0;
}

.cols__col--product .cols__col--product-img {
  float: left;
  position: initial;
  margin-right: 1em;
}

.section .smaller,
ul.accord > li > ul li a span.alt {
  position: relative;
  top: -0.1em;
}

.spr--ico-key-translation:before {
  background-position: -114px -239px;
  width: 47px;
  height: 47px;
}

.trends--egt .title {
  padding-bottom: 0;
  border-bottom: 0;
}

.circle.bg--more.open .fcdo-minus,
.circle.bg--more .bg--more {
  display: inline-block;
}

.bg--white {
  background: #fff;
}

.bg--def,
.bg--more {
  background: #e84427;
}

.bg--di {
  background: #0091ff;
}

.bg--fb {
  background: #3b5998;
}

.bg--gp {
  background: #dc4e41;
}

.bg--re {
  background: #ff4500;
}

.bg--su {
  background: #eb4924;
}

.bg--tu {
  background: #36465d;
}

.bg--tw {
  background: #55acee;
}

.helper {
  background: #bfcdea;
  padding: 20px;
  margin-top: 15px;
  color: #111;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  font-size: 0.875em;
}

.helper .point {
  display: none;
  background: #bfcdea;
  top: 0;
  left: 50%;
  margin: -5px 0 0 -5px;
}

a.helper {
  display: block;
  padding: 11px 30px 11px 20px;
  text-decoration: none;
  font-weight: bold;
  cursor: pointer;
}

a.helper p {
  overflow: hidden;
  margin: 0;
  white-space: nowrap;
  line-height: 1em;
  text-overflow: ellipsis;
}

a.helper:hover,
a.helper:hover .point {
  background: #b2c0de;
}

.circle.bg--more .fcdo-minus,
.circle.bg--more.open .fcdo-plus,
.translator_layout .content2,
.translator_layout .content1 {
  display: none;
}

.translator_layout .translate-tool .virtualKeyboard {
  z-index: 5;
}

.kernerman-copyright-img {
  height: 20px;
  margin-right: 5px;
}

.translator_layout .dropdown[data-selectbox-id='languageTo'] .dropdown__box {
  right: 0;
}

.translator_layout .cdo-tpl-alt {
  margin-top: 5px;
}

.translator_layout .with-el {
  border-top: 0;
}

.translator_layout .translate-tool__from__input {
  border: 0;
  resize: none;
  margin-bottom: 30px;
  outline: 0;
}

.translator_layout .translate-tool__from__keyboard-trig {
  background: transparent;
}

.translator_layout .translate-tool__from__keyboard-trig {
  bottom: 0;
}

.cdo-search__button,
.cdo-search__dataset {
  float: right;
}

.cdo-hdr__blocks--home .cdo-search.cdo-search-centered {
  float: none;
  width: 100%;
}

.cdo-hdr__profile a.hdr-btn .fcdo {
  opacity: 0.5;
  color: inherit;
}

.cdo-hdr__profile a.hdr-btn:hover .fcdo,
.cdo-hdr__profile a.hdr-btn.on .fcdo {
  opacity: 1;
  color: inherit;
}

.mod-browser .pos {
  color: gray;
  font-style: italic;
}

.sense-block .cdo-cloud-content .pos {
  color: inherit;
}

.dropdown--options .dropdown__box .btn {
  text-align: inherit;
  font-weight: inherit;
}

.dropdown--options .dropdown__box a {
  margin-bottom: 5px;
}

.modal-confirm {
  max-width: 400px;
  min-height: 0;
}

a {
  cursor: pointer;
}

.mod-quiz .incorrect {
  color: #f00;
}

.mod-quiz .correct {
  color: #2e8b57;
}

.mod-quiz .virtualKeyboard {
  margin-top: 1em;
}

.mod-quiz .fcdo {
  color: inherit;
  vertical-align: top;
  font-size: 1.33333333em;
  line-height: 0.75em;
}

.mod-quiz .circle .fcdo {
  vertical-align: middle;
  font-size: inherit;
  line-height: inherit;
}

#informational-content .circle-btn--sml {
  line-height: 22px;
}

#informational-content .txt-block {
  padding: 11px 20px;
}

#informational-content .txt-block--alt4 {
  background: #bfcce9;
  padding: 5px 20px;
}

#informational-content pre.linktous {
  white-space: normal;
  word-break: break-word;
  overflow: hidden;
  background-color: #f0f0f0;
  font-size: 1.2em;
  padding: 0.5em;
  margin: 0.25em 1px;
}

.mod-quiz .questionary-resume-item {
  padding: 10px 20px;
  margin-bottom: 1em;
}

.mod-quiz .questionary-resume {
  text-align: left;
}

.di.english-chinese-simplified .trans,
.di.english-chinese-traditional .trans {
  font-weight: normal;
}

div.entry_title .results .pos {
  font-style: italic;
  font-size: 95%;
}

.mod-define > a:before {
  content: '\e903';
}

.pronunciation-english.entry-body__el {
  min-height: 0;
}

.pronunciation-english .pronunciation-item {
  margin-bottom: 10px;
}

.pronunciation-english .sound {
  display: inline-block;
  background: #e84427;
  color: #fff;
  border-radius: 3px;
  padding: 2px 10px 0;
  text-transform: uppercase;
  font-weight: bold;
  margin-right: 5px;
}

.pronunciation-english .sound .fcdo {
  font-size: 1.5em;
  display: inline-block;
  margin: 0 5px 3px 0;
}

.lab {
  font-size: 1.1em;
}

.lab .region {
  text-transform: lowercase;
  font-style: normal;
}

.pronVideos {
  max-width: 560px;
  margin: 0 auto 20px auto;
}

.pronVideo {
  width: 100%;
  height: 315px;
  margin-bottom: 10px;
}

#browseGroups div {
  display: inline;
}

.caro__el img {
  min-width: 100%;
}

#mobEntryDictName {
  text-transform: capitalize;
}

.default_layout .cdo-search,
.translator_layout .cdo-search {
  padding: 7px 20px 6px;
}

.wordlist-popup * {
  box-sizing: border-box;
}

.wordlist-popup {
  position: absolute;
  bottom: 25px;
  right: -10px;
  width: 240px;
  margin: 10px 0;
  z-index: 6;
  background: #fff;
  border: 1px solid #c5c5c5;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.18);
  font-size: 14px;
  text-align: left;
}

.wordlist-popup:before {
  content: '';
  display: inline-block;
  position: absolute;
  right: 11px;
  width: 0;
  height: 0;
  border-style: solid;
  bottom: -10px;
  border-width: 10px 10px 0 10px;
  border-color: #c5c5c5 transparent transparent transparent;
}

.wordlist-popup.under {
  top: 25px;
  bottom: auto;
}

.wordlist-popup.right {
  right: auto;
  left: 0;
}

.wordlist-popup.right:before {
  right: auto;
  left: 0;
}

.wordlist-popup.under:before {
  bottom: auto;
  top: -10px;
  border-width: 0 10px 10px 10px;
  border-color: transparent transparent #c5c5c5 transparent;
}

.wordlist-popup ul {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 0;
}

.wordlist-popup li {
  margin: 0;
  clear: both;
}

.wordlist-popup .title,
.wordlist-popup .login-button {
  text-decoration: underline;
}

.wordlist-popup .title {
  font-weight: bold;
  padding: 8px;
  border-bottom: 1px solid #c5c5c5;
  margin: 0;
  display: block;
}

.wordlist-popup .spinner {
  display: table;
  margin: 30px auto;
}

.wordlist-popup .name {
  float: left;
  display: block;
  padding: 8px;
  text-overflow: ellipsis;
  width: 180px;
  overflow: hidden;
  white-space: nowrap;
}

.wordlist-popup .name:hover {
  background: #d8d8ff;
  text-decoration: underline;
}

.wordlist-popup .add {
  margin: 3px 5px;
  float: right;
}

.wordlist-popup .error {
  padding: 8px;
  background: #a22f1b;
  color: #fff;
  font-size: 14px;
}

.wordlist-popup .info {
  padding: 8px;
  background: #bfcce9;
  color: #11326f;
  text-overflow: ellipsis;
  width: 240px;
  overflow: hidden;
  font-size: 14px;
}

.wordlist-popup .circle i {
  font-size: 1.2em;
  line-height: 1.9rem;
}

.wordlist-popup form {
  position: relative;
  border-top: solid 1px #c5c5c5;
  padding: 2px 0;
}

.wordlist-popup form input {
  border: 0;
}

.wordlist-popup form input[type='submit'] {
  float: right;
  line-height: 24px;
}

.wordlist-popup form input[type='text'] {
  width: 100%;
  margin: 3px;
  margin-right: 0;
  outline: 0;
  padding: 8px;
  background: 0;
  box-shadow: none;
  margin: 0;
}

.word-list {
  word-wrap: break-word;
  line-break: after-white-space;
}

.cdo-search__input[type='text'] {
  outline: 0;
}

.tiles__tile {
  text-transform: capitalize;
}

.ipa {
  display: inline-block;
  padding: 0 2px 0 1px;
}

.cycler__items a {
  white-space: nowrap;
}

.i {
  font-style: italic;
}

.gl {
  font-style: normal;
}

.lex {
  text-transform: none;
  font-style: italic;
}

.b {
  font-weight: bold;
}

.sp,
.nu {
  font-size: 66%;
  position: relative;
  bottom: 0.5em;
}

.sb,
.dn {
  font-size: 66%;
  position: relative;
  top: 0.3em;
}

.cle-xeg,
.xeg {
  text-decoration: line-through;
}

.u {
  text-decoration: underline;
}

.email-form {
  text-align: center;
}

.tiles__tile label {
  padding: 20px 0;
}

.tiles__tile input[type='radio']:checked + label:before,
.tiles__tile input[type='checkbox']:checked + label:before {
  padding: 1px 3px 2px 2px;
  position: absolute;
  top: 0;
  left: 0;
  color: #000;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0 0 8px 0;
}

.wordlist-panel h1 {
  font-size: 1.5em;
}

.wordlist-panel h1.breadcrumb {
  font-size: 1em;
}

.wordlist-panel h1 a {
  text-decoration: none;
}

.wordlist-panel h1 a:hover {
  text-decoration: underline;
}

.fav-entry .fcdo.fcdo-plus {
  line-height: 31px;
}

.sect.sect--bg h2 {
  margin-bottom: 15px;
  font-size: 1.35em;
  font-weight: normal;
}

h1.cdo-hero__title span {
  color: #d0a44c;
}

.margin-bottom {
  margin-bottom: 50px;
}

#browseResults .title {
  padding: 0;
  border: 0 none;
}

.padding-15 {
  padding: 15px 0;
}

.margin-top-15 {
  margin-top: 15px;
}

.txt-block--alt a:hover,
.txt-block--alt2 a:hover {
  text-decoration: underline;
}

.trans[lang='ar'] {
  font-size: 2em;
  font-weight: normal;
  unicode-bidi: -webkit-plaintext;
}

.rv + .rv:before {
  content: ' / ';
}

.img-thumb ~ .extraexamps,
.img-thumb ~ .smartt {
  clear: both;
}

.sense-body:after {
  content: '';
  display: table;
  clear: both;
}

.img-thumb {
  position: relative;
  border: 1px solid #bfcce9;
  display: inline-block;
}

.img-thumb .img-copyright {
  border-radius: 0;
  top: auto;
  left: auto;
  right: 0;
  bottom: 0;
  color: #fff;
  background: rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.img-thumb .img-copyright span {
  word-break: normal;
}

.entry-body.british-grammar .section {
  margin-top: 20px;
}

.entry-body.british-grammar .section ~ .section {
  margin-top: 40px;
}

.entry-body.british-grammar .section_anchor {
  height: 0;
}

.entry-body.british-grammar .panel {
  margin: 20px 0;
}

.entry-body.british-grammar blockquote {
  font-size: inherit;
  font-style: inherit;
  color: var(--color-font-grey);
}

.entry-body.british-grammar blockquote .utterance {
  clear: both;
  padding-left: 2em;
}

.entry-body.british-grammar blockquote .speaker {
  float: left;
  font-weight: bold;
  font-style: normal;
  margin-left: -2em;
  margin-top: 2px;
}

.entry-body.british-grammar .nav p,
.entry-body.british-grammar td p {
  margin: 0;
}

.entry-body.british-grammar .nav > p {
  margin-top: 20px;
  line-height: 1.5em;
  font-weight: 700;
}

.entry-body.british-grammar .nav ul {
  margin-left: 30px;
  list-style-type: none;
}

.entry-body.british-grammar .nav a {
  font-weight: 700;
  text-decoration: none;
}

.entry-body.british-grammar .nav a:hover {
  text-decoration: underline;
}

.entry-body.british-grammar td {
  background: #eef1f5;
}

.entry-body.british-grammar blockquote::before {
  content: '';
}

.ruby {
  display: inline-block;
  text-align: text-bottom;
}

.rt {
  display: block;
  font-size: 80%;
  text-align: center;
  font-style: normal;
}

.rb {
  display: block;
}

.intonation-arrow {
  display: inline-block;
  height: 2.25em;
  vertical-align: bottom;
  width: 0;
  font-weight: normal;
}

.entry-body.british-grammar h1 {
  margin: 0 0 5px;
}

.entry-body.british-grammar .header {
  margin-bottom: 20px;
}

.cloud {
  margin-bottom: 15px;
}

.cloud.txt-block {
  padding-right: 0;
  background: #eff1f6;
}

.cloud ul {
  margin-bottom: 10px;
  text-align: center;
  line-height: 1.8em;
}

.cloud li {
  display: inline-block;
  margin-right: 25px;
}

.cloud li a {
  color: #16a085;
}

.cloud li a i {
  font-style: normal;
}

.cloud li a .pos {
  font-style: italic;
}

.cloud li a.odd {
  color: #16a085;
}

.cloud .topic_0 {
  font-size: 0.9em;
}

.cloud .topic_2 {
  font-size: 1.15em;
}

.cloud .topic_3 {
  font-size: 1.5em;
}

.cloud .topic_4 {
  font-size: 1.8em;
}

.def-body {
  display: list;

  .trans {
    display: block;
    margin: 0 0 5px 0;
    font-style: normal;
    color: var(--color-font-grey);

    &:first-child {
      font-weight: bold;
    }
  }

  .examp {
    margin-left: 1.3em;
    display: list-item;
  }
}

.phrase-body.pad-indent {
  padding: 0;
}

.cols {
  .item {
    display: list-item;
    margin-left: 2.2em;
  }

  a {
    color: inherit;
  }
}

.js-accord {
  margin-left: 1em;
  display: inline-block;
  padding: 0 8px;
  border-radius: 5px;
  color: #fff;
  background: #797979;
  cursor: pointer;
  user-select: none;

  &::before {
    content: '+ ';
  }

  + * {
    display: none;

    a[title^='Synonyms and related'] {
      color: inherit;
    }
  }

  &.open + * {
    display: block;
  }
}

.see_also {
  a {
    margin-left: 1em;
    color: #16a085;
  }
}

.def-head {
  margin-bottom: 0;

  a {
    color: inherit;
  }
}

.share {
  display: none !important;
}

ul.daccord_b {
  padding-left: 1.3em;
}

li.dexamp {
  list-style-type: disc;
}

.amp-accordion {
  > .daccord_h {
    font-weight: bold;
    cursor: pointer;
  }

  & > :last-child {
    display: none;
    padding: 0 1em 1em;

    ul {
      padding-left: 1em;
    }

    li {
      list-style-type: disc;
    }
  }

  .i-plus:before {
    content: '+';
  }

  &.open {
    .i-plus:before {
      content: '-';
    }

    & > :last-child {
      display: block;
    }
  }
}

.dphrase-block {
  padding: 5px;
}

.dwl {
  position: relative;
  margin-top: 2px;
  border-top: solid thin #c76e06;
}
