/**
 * 统一存储服务
 * 实现Firebase + 本地缓存的混合架构
 * 策略：Firebase作为主要存储，localStorage作为缓存加速
 */

import { 
  chatHistoryService, 
  aiAnalysisService, 
  dictionarySearchService, 
  writingHistoryService, 
  diaryHistoryService 
} from '../history/firebaseHistoryService';
import { generateUniqueId } from '../../utils/idGenerator';

// 缓存键名
const CACHE_KEYS = {
  CHAT_HISTORY: 'cache_chat_history',
  ANALYSIS_HISTORY: 'cache_analysis_history',
  DICTIONARY_SEARCH: 'cache_dictionary_search',
  WRITING_HISTORY: 'cache_writing_history',
  DIARY_HISTORY: 'cache_diary_history',
  LAST_SYNC: 'cache_last_sync'
};

// 缓存配置
const CACHE_CONFIG = {
  EXPIRY_TIMES: {
    CHAT_HISTORY: 5 * 60 * 1000,      // 5分钟
    ANALYSIS_HISTORY: 10 * 60 * 1000,  // 10分钟
    DICTIONARY_SEARCH: 30 * 60 * 1000, // 30分钟
    WRITING_HISTORY: 15 * 60 * 1000,   // 15分钟
    DIARY_HISTORY: 5 * 60 * 1000,      // 5分钟
  },
  SYNC_INTERVALS: {
    BACKGROUND_SYNC: 2 * 60 * 1000,    // 2分钟后台同步
    FOREGROUND_SYNC: 30 * 1000,        // 30秒前台同步
  }
};

class UnifiedStorageService {
  constructor() {
    this.userId = null;
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.syncCallbacks = [];
    
    // 监听网络状态
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.triggerBackgroundSync();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * 初始化服务
   * @param {string} userId - 用户ID
   */
  init(userId) {
    // 避免重复初始化
    if (this.userId === userId) {
      console.log('🚀 统一存储服务已初始化，跳过重复初始化');
      return;
    }
    
    this.userId = userId;
    console.log('🚀 统一存储服务初始化:', userId);
    
    // 启动后台同步（但不会立即触发）
    this.startBackgroundSync();
    
    // 延迟启动首次同步，给迁移过程一些时间
    setTimeout(() => {
      if (this.isOnline && this.userId) {
        console.log('🔄 启动首次后台同步...');
        this.triggerBackgroundSync();
      }
    }, 2000); // 2秒后启动同步
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 数据
   * @param {number} timestamp - 时间戳
   */
  setCache(key, data, timestamp = Date.now()) {
    try {
      const cacheItem = {
        data,
        timestamp,
        version: '1.0'
      };
      localStorage.setItem(key, JSON.stringify(cacheItem));
    } catch (error) {
      console.error('设置缓存失败:', error);
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @param {number} expiryTime - 过期时间（毫秒）
   * @returns {any|null} 缓存数据或null
   */
  getCache(key, expiryTime) {
    try {
      const cached = localStorage.getItem(key);
      if (!cached) return null;

      const cacheItem = JSON.parse(cached);
      const isExpired = Date.now() - cacheItem.timestamp > expiryTime;
      
      if (isExpired) {
        localStorage.removeItem(key);
        return null;
      }

      return cacheItem.data;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键
   */
  clearCache(key) {
    localStorage.removeItem(key);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    const stats = {
      totalSize: 0,
      itemCount: 0,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress
    };

    Object.values(CACHE_KEYS).forEach(key => {
      const cached = localStorage.getItem(key);
      if (cached) {
        stats.totalSize += cached.length;
        stats.itemCount++;
      }
    });

    return stats;
  }

  /**
   * 添加同步状态监听器
   * @param {Function} callback - 回调函数
   */
  addSyncListener(callback) {
    this.syncCallbacks.push(callback);
  }

  /**
   * 移除同步状态监听器
   * @param {Function} callback - 回调函数
   */
  removeSyncListener(callback) {
    const index = this.syncCallbacks.indexOf(callback);
    if (index > -1) {
      this.syncCallbacks.splice(index, 1);
    }
  }

  /**
   * 触发同步状态变化
   * @param {string} status - 同步状态
   */
  triggerSyncStatus(status) {
    this.syncCallbacks.forEach(callback => callback(status));
  }

  /**
   * 启动后台同步
   */
  startBackgroundSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      if (this.isOnline && this.userId && !this.syncInProgress) {
        this.triggerBackgroundSync();
      }
    }, CACHE_CONFIG.SYNC_INTERVALS.BACKGROUND_SYNC);
  }

  /**
   * 停止后台同步
   */
  stopBackgroundSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * 触发后台同步
   */
  async triggerBackgroundSync() {
    if (this.syncInProgress || !this.userId) return;

    this.syncInProgress = true;
    this.triggerSyncStatus('syncing');

    try {
      console.log('🔄 开始后台同步...');
      
      // 同步所有数据类型
      await Promise.all([
        this.syncChatHistory(),
        this.syncAnalysisHistory(),
        this.syncDictionarySearch(),
        this.syncWritingHistory(),
        this.syncDiaryHistory()
      ]);

      // 更新最后同步时间
      this.setCache(CACHE_KEYS.LAST_SYNC, Date.now());
      
      console.log('✅ 后台同步完成');
      this.triggerSyncStatus('completed');
    } catch (error) {
      console.error('❌ 后台同步失败:', error);
      this.triggerSyncStatus('failed');
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 手动触发同步
   */
  async manualSync() {
    if (this.syncInProgress) {
      console.log('⏳ 同步已在进行中...');
      return;
    }

    this.triggerSyncStatus('syncing');
    await this.triggerBackgroundSync();
  }

  // ==================== 聊天历史 ====================

  /**
   * 获取聊天历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 聊天历史
   */
  async getChatHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.CHAT_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.CHAT_HISTORY;

    // 优先使用缓存
    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取聊天历史');
        return cached.slice(0, limit);
      }
    }

    // 从Firebase获取
    console.log('🌐 从Firebase获取聊天历史');
    try {
      const data = await chatHistoryService.getChatHistory(this.userId, limit);
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      // 尝试返回过期缓存
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 保存聊天会话
   * @param {Object} sessionData - 会话数据
   * @returns {Promise<Object>} 保存的会话数据
   */
  async saveChatSession(sessionData) {
    try {
      // 保存到Firebase
      const result = await chatHistoryService.saveChatSession(sessionData, this.userId);
      
      // 更新本地缓存
      const cached = this.getCache(CACHE_KEYS.CHAT_HISTORY, Infinity) || [];
      const updatedCache = [result, ...cached.filter(s => s.id !== result.id)];
      this.setCache(CACHE_KEYS.CHAT_HISTORY, updatedCache);
      
      return result;
    } catch (error) {
      console.error('保存聊天会话失败:', error);
      throw error;
    }
  }

  /**
   * 同步聊天历史
   */
  async syncChatHistory() {
    try {
      if (!this.userId) {
        console.error('❌ 用户ID未设置，跳过聊天历史同步');
        return;
      }
      console.log('🔄 开始同步聊天历史，用户ID:', this.userId);
      const data = await chatHistoryService.getChatHistory(this.userId, 100);
      this.setCache(CACHE_KEYS.CHAT_HISTORY, data);
      console.log('✅ 聊天历史同步完成');
    } catch (error) {
      console.error('❌ 聊天历史同步失败:', error);
    }
  }

  // ==================== AI分析历史 ====================

  /**
   * 获取AI分析历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 分析历史
   */
  async getAnalysisHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.ANALYSIS_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.ANALYSIS_HISTORY;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取分析历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从Firebase获取分析历史');
    try {
      const data = await aiAnalysisService.getAnalysisHistory(this.userId, limit);
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('获取分析历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 保存AI分析记录
   * @param {Object} analysisData - 分析数据
   * @returns {Promise<Object>} 保存的分析数据
   */
  async saveAnalysis(analysisData) {
    try {
      // 确保有唯一ID
      if (!analysisData.id) {
        analysisData.id = generateUniqueId();
      }

      // 保存到Firebase
      const result = await aiAnalysisService.saveAnalysis(analysisData, this.userId);
      
      // 更新本地缓存
      const cached = this.getCache(CACHE_KEYS.ANALYSIS_HISTORY, Infinity) || [];
      const updatedCache = [result, ...cached.filter(a => a.id !== result.id)];
      this.setCache(CACHE_KEYS.ANALYSIS_HISTORY, updatedCache);
      
      return result;
    } catch (error) {
      console.error('保存AI分析记录失败:', error);
      throw error;
    }
  }

  /**
   * 同步AI分析历史
   */
  async syncAnalysisHistory() {
    try {
      const data = await aiAnalysisService.getAnalysisHistory(this.userId, 100);
      this.setCache(CACHE_KEYS.ANALYSIS_HISTORY, data);
      console.log('✅ AI分析历史同步完成');
    } catch (error) {
      console.error('❌ AI分析历史同步失败:', error);
    }
  }

  // ==================== 字典搜索历史 ====================

  /**
   * 获取字典搜索历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 搜索历史
   */
  async getDictionarySearchHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.DICTIONARY_SEARCH;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.DICTIONARY_SEARCH;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取字典搜索历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从Firebase获取字典搜索历史');
    try {
      const data = await dictionarySearchService.getSearchHistory(this.userId, limit);
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('获取字典搜索历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 保存字典搜索记录
   * @param {Object} searchData - 搜索数据
   * @returns {Promise<Object>} 保存的搜索数据
   */
  async saveDictionarySearch(searchData) {
    try {
      if (!searchData.id) {
        searchData.id = generateUniqueId();
      }

      const result = await dictionarySearchService.saveSearch(searchData, this.userId);
      
      const cached = this.getCache(CACHE_KEYS.DICTIONARY_SEARCH, Infinity) || [];
      const updatedCache = [result, ...cached.filter(s => s.id !== result.id)];
      this.setCache(CACHE_KEYS.DICTIONARY_SEARCH, updatedCache);
      
      return result;
    } catch (error) {
      console.error('保存字典搜索记录失败:', error);
      throw error;
    }
  }

  /**
   * 同步字典搜索历史
   */
  async syncDictionarySearch() {
    try {
      if (!this.userId) {
        console.error('❌ 用户ID未设置，跳过字典搜索历史同步');
        return;
      }
      console.log('🔄 开始同步字典搜索历史，用户ID:', this.userId);
      const data = await dictionarySearchService.getSearchHistory(this.userId, 100);
      this.setCache(CACHE_KEYS.DICTIONARY_SEARCH, data);
      console.log('✅ 字典搜索历史同步完成');
    } catch (error) {
      console.error('❌ 字典搜索历史同步失败:', error);
    }
  }

  // ==================== 写作历史 ====================

  /**
   * 获取写作历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 写作历史
   */
  async getWritingHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.WRITING_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.WRITING_HISTORY;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取写作历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从Firebase获取写作历史');
    try {
      const data = await writingHistoryService.getWritingHistory(this.userId, limit);
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('获取写作历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 保存写作记录
   * @param {Object} writingData - 写作数据
   * @returns {Promise<Object>} 保存的写作数据
   */
  async saveWritingHistory(writingData) {
    try {
      if (!writingData.id) {
        writingData.id = generateUniqueId();
      }

      const result = await writingHistoryService.saveWriting(writingData, this.userId);
      
      const cached = this.getCache(CACHE_KEYS.WRITING_HISTORY, Infinity) || [];
      const updatedCache = [result, ...cached.filter(w => w.id !== result.id)];
      this.setCache(CACHE_KEYS.WRITING_HISTORY, updatedCache);
      
      return result;
    } catch (error) {
      console.error('保存写作记录失败:', error);
      throw error;
    }
  }

  /**
   * 同步写作历史
   */
  async syncWritingHistory() {
    try {
      if (!this.userId) {
        console.error('❌ 用户ID未设置，跳过写作历史同步');
        return;
      }
      console.log('🔄 开始同步写作历史，用户ID:', this.userId);
      const data = await writingHistoryService.getWritingHistory(this.userId, 100);
      this.setCache(CACHE_KEYS.WRITING_HISTORY, data);
      console.log('✅ 写作历史同步完成');
    } catch (error) {
      console.error('❌ 写作历史同步失败:', error);
    }
  }

  // ==================== 日记历史 ====================

  /**
   * 获取日记历史（带缓存）
   * @param {number} limit - 限制数量
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Array>} 日记历史
   */
  async getDiaryHistory(limit = 50, forceRefresh = false) {
    const cacheKey = CACHE_KEYS.DIARY_HISTORY;
    const expiryTime = CACHE_CONFIG.EXPIRY_TIMES.DIARY_HISTORY;

    if (!forceRefresh) {
      const cached = this.getCache(cacheKey, expiryTime);
      if (cached) {
        console.log('📱 从缓存获取日记历史');
        return cached.slice(0, limit);
      }
    }

    console.log('🌐 从Firebase获取日记历史');
    try {
      const data = await diaryHistoryService.getDiaryHistory(this.userId, limit);
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('获取日记历史失败:', error);
      const staleCache = this.getCache(cacheKey, Infinity);
      if (staleCache) {
        console.log('⚠️ 返回过期缓存数据');
        return staleCache.slice(0, limit);
      }
      throw error;
    }
  }

  /**
   * 保存日记记录
   * @param {Object} diaryData - 日记数据
   * @returns {Promise<Object>} 保存的日记数据
   */
  async saveDiaryHistory(diaryData) {
    try {
      if (!diaryData.id) {
        diaryData.id = generateUniqueId();
      }

      const result = await diaryHistoryService.saveDiary(diaryData, this.userId);
      
      const cached = this.getCache(CACHE_KEYS.DIARY_HISTORY, Infinity) || [];
      const updatedCache = [result, ...cached.filter(d => d.id !== result.id)];
      this.setCache(CACHE_KEYS.DIARY_HISTORY, updatedCache);
      
      return result;
    } catch (error) {
      console.error('保存日记记录失败:', error);
      throw error;
    }
  }

  /**
   * 同步日记历史
   */
  async syncDiaryHistory() {
    try {
      if (!this.userId) {
        console.error('❌ 用户ID未设置，跳过日记历史同步');
        return;
      }
      console.log('🔄 开始同步日记历史，用户ID:', this.userId);
      const data = await diaryHistoryService.getDiaryHistory(this.userId, 100);
      this.setCache(CACHE_KEYS.DIARY_HISTORY, data);
      console.log('✅ 日记历史同步完成');
    } catch (error) {
      console.error('❌ 日记历史同步失败:', error);
    }
  }
}

// 创建单例实例
const unifiedStorageService = new UnifiedStorageService();

export default unifiedStorageService;
