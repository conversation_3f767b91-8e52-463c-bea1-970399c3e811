import { beforeEach, describe, expect, it, vi } from 'vitest';
import simpleStorageService from '../storage/simpleStorageService'; // Add this line
import { analyzeText, generateDemoResponse } from './aiService';

// Mock dependencies
vi.mock('../history/hybridHistoryService', () => ({
  hybridAnalysisService: {
    saveAnalysis: vi.fn()
  }
}));

vi.mock('../storage/simpleStorageService', () => ({
  default: {
    saveAnalysis: vi.fn()
  }
}));

vi.mock('./promptLoader', () => ({
  loadPrompt: vi.fn()
}));

vi.mock('../user/userSettingsService', () => ({
  getDoubaoApiKey: vi.fn(),
  checkApiUsageLimit: vi.fn(),
  recordApiUsage: vi.fn()
}));

vi.mock('../../utils/idGenerator', () => ({
  generateUniqueId: vi.fn(() => 'test-id-123')
}));

// Mock fetch
global.fetch = vi.fn();

describe('aiService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('analyzeText', () => {
    it('应该抛出错误当文本为空', async () => {
      await expect(analyzeText('')).rejects.toThrow('文本不能为空');
      await expect(analyzeText('   ')).rejects.toThrow('文本不能为空');
    });

    it('应该抛出错误当没有API密钥', async () => {
      const { getDoubaoApiKey } = await import('../user/userSettingsService');
      getDoubaoApiKey.mockResolvedValue(null);

      await expect(analyzeText('test text')).rejects.toThrow('系统API密钥未配置，请联系管理员');
    });

    it('应该抛出错误当使用量超限', async () => {
      const { getDoubaoApiKey, checkApiUsageLimit } = await import('../user/userSettingsService');
      getDoubaoApiKey.mockResolvedValue('test-key');
      checkApiUsageLimit.mockResolvedValue({
        canUse: false,
        maxRequests: 10,
        remainingRequests: 0
      });

      await expect(analyzeText('test text', 'user123')).rejects.toThrow('免费版今日写作纠错次数已达上限 (10次)，剩余 0 次');
    });

    it('应该成功分析文本', async () => {
      const { getDoubaoApiKey, checkApiUsageLimit, recordApiUsage } = await import('../user/userSettingsService');
      const { loadPrompt } = await import('./promptLoader');
      const { hybridAnalysisService } = await import('../history/hybridHistoryService');

      getDoubaoApiKey.mockResolvedValue('test-key');
      checkApiUsageLimit.mockResolvedValue({ canUse: true });
      loadPrompt.mockResolvedValue('test prompt');
      recordApiUsage.mockResolvedValue();

      // Mock fetch responses
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: 'Test analysis' } }]
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: 'Test suggestions' } }]
          })
        });

      const result = await analyzeText('test text', 'user123');

      expect(result).toHaveProperty('rawAnalysis');
      expect(result).toHaveProperty('suggestions');
      expect(result.rawAnalysis).toBe('Test analysis');
      expect(simpleStorageService.saveAnalysis).toHaveBeenCalledWith(
        'test text',
        'Test analysis',
        [] // Mocked suggestions array
      );
      expect(recordApiUsage).toHaveBeenCalledWith('user123', 'writing');
    });

    it('应该处理API调用失败', async () => {
      const { getDoubaoApiKey, checkApiUsageLimit } = await import('../user/userSettingsService');
      const { loadPrompt } = await import('./promptLoader');

      getDoubaoApiKey.mockResolvedValue('test-key');
      checkApiUsageLimit.mockResolvedValue({ canUse: true });
      loadPrompt.mockResolvedValue('test prompt');

      // Mock fetch to fail
      global.fetch.mockResolvedValue({
        ok: false,
        status: 500,
        text: () => Promise.resolve('Internal Server Error')
      });

      const result = await analyzeText('test text');

      expect(result).toHaveProperty('rawAnalysis');
      expect(result).toHaveProperty('suggestions');
      expect(result.rawAnalysis).toContain('分析与改进建议');
    });
  });

  describe('generateDemoResponse', () => {
    it('应该为包含常见错误的文本生成建议', () => {
      const text = 'i want to reach my dream alot';
      const result = generateDemoResponse(text);

      expect(result).toContain('"i" 应该大写为 "I"');
      expect(result).toContain('"alot" 应该写成两个单词 "a lot"');
      expect(result).toContain('"reach my dream" 是不自然的表达');
    });

    it('应该为没有明显错误的文本生成通用建议', () => {
      const text = 'This is a good text without obvious errors';
      const result = generateDemoResponse(text);

      expect(result).toContain('整体表达比较口语化');
      expect(result).toContain('建议避免重复使用"very"等强调词');
      expect(result).toContain('风格优化');
    });

    it('应该包含所有建议类别', () => {
      const text = 'i alot reach my dream';
      const result = generateDemoResponse(text);

      expect(result).toContain('拼写与标点问题');
      expect(result).toContain('语法与表达问题');
      expect(result).toContain('风格优化');
    });

    it('应该处理空文本', () => {
      const result = generateDemoResponse('');
      expect(result).toContain('整体表达比较口语化');
      expect(result).toContain('建议避免重复使用"very"等强调词');
    });
  });
});
