import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import unifiedStorageService from './unifiedStorageService';

// Mock Firebase services
vi.mock('../history/firebaseHistoryService', () => ({
  chatHistoryService: {
    getChatHistory: vi.fn(),
    saveChatSession: vi.fn()
  },
  aiAnalysisService: {
    getAnalysisHistory: vi.fn(),
    saveAnalysis: vi.fn()
  },
  dictionarySearchService: {
    getSearchHistory: vi.fn(),
    saveSearch: vi.fn()
  },
  writingHistoryService: {
    getWritingHistory: vi.fn(),
    saveWriting: vi.fn()
  },
  diaryHistoryService: {
    getDiaryHistory: vi.fn(),
    saveDiary: vi.fn()
  }
}));

// Mock idGenerator
vi.mock('../../utils/idGenerator', () => ({
  generateUniqueId: vi.fn(() => 'mock-id-123')
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
};

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true
});

describe('UnifiedStorageService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });
    
    // Reset service state
    unifiedStorageService.userId = null;
    unifiedStorageService.syncInProgress = false;
    unifiedStorageService.syncCallbacks = [];
  });

  afterEach(() => {
    // Clean up timers
    if (unifiedStorageService.syncInterval) {
      clearInterval(unifiedStorageService.syncInterval);
      unifiedStorageService.syncInterval = null;
    }
  });

  describe('初始化', () => {
    it('应该正确初始化服务', () => {
      const userId = 'test-user-123';
      
      unifiedStorageService.init(userId);
      
      expect(unifiedStorageService.userId).toBe(userId);
    });

    it('应该跳过重复初始化', () => {
      const userId = 'test-user-123';
      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      unifiedStorageService.init(userId);
      unifiedStorageService.init(userId);
      
      expect(consoleLogSpy).toHaveBeenCalledWith('🚀 统一存储服务已初始化，跳过重复初始化');
      
      consoleLogSpy.mockRestore();
    });
  });

  describe('缓存功能', () => {
    it('应该设置缓存', () => {
      const key = 'test_key';
      const data = { message: 'test' };
      const timestamp = 1234567890;
      
      unifiedStorageService.setCache(key, data, timestamp);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        key,
        JSON.stringify({
          data,
          timestamp,
          version: '1.0'
        })
      );
    });

    it('应该获取有效缓存', () => {
      const key = 'test_key';
      const data = { message: 'test' };
      const mockCacheItem = {
        data,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = unifiedStorageService.getCache(key, 60000);
      
      expect(result).toEqual(data);
    });

    it('应该返回null当缓存过期时', () => {
      const key = 'test_key';
      const expiredCacheItem = {
        data: { message: 'test' },
        timestamp: Date.now() - 120000, // 2分钟前
        version: '1.0'
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredCacheItem));
      
      const result = unifiedStorageService.getCache(key, 60000); // 1分钟过期
      
      expect(result).toBeNull();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(key);
    });

    it('应该清除指定缓存', () => {
      const key = 'test_key';
      
      unifiedStorageService.clearCache(key);
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(key);
    });

    it('应该清除所有缓存', () => {
      unifiedStorageService.clearAllCache();
      
      expect(localStorageMock.removeItem).toHaveBeenCalledTimes(6); // 6个缓存键
    });
  });

  describe('聊天历史功能', () => {
    it('应该从缓存获取聊天历史', async () => {
      const mockHistory = [
        { id: '1', message: 'Hello' },
        { id: '2', message: 'World' }
      ];
      const mockCacheItem = {
        data: mockHistory,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = await unifiedStorageService.getChatHistory(10, false);
      
      expect(result).toEqual(mockHistory.slice(0, 10));
    });

    it('应该从Firebase获取聊天历史当缓存过期时', async () => {
      const { chatHistoryService } = await import('../history/firebaseHistoryService');
      const mockHistory = [{ id: '1', message: 'Hello' }];
      
      chatHistoryService.getChatHistory.mockResolvedValue(mockHistory);
      localStorageMock.getItem.mockReturnValue(null);
      unifiedStorageService.userId = 'test-user';
      
      const result = await unifiedStorageService.getChatHistory(10, false);
      
      expect(chatHistoryService.getChatHistory).toHaveBeenCalledWith('test-user', 10);
      expect(result).toEqual(mockHistory);
    });

    it('应该保存聊天会话', async () => {
      const { chatHistoryService } = await import('../history/firebaseHistoryService');
      const mockSessionData = { message: 'Hello' };
      const mockResult = { id: '1', message: 'Hello', saved: true };
      
      chatHistoryService.saveChatSession.mockResolvedValue(mockResult);
      unifiedStorageService.userId = 'test-user';
      localStorageMock.getItem.mockReturnValue('[]'); // 空缓存
      
      const result = await unifiedStorageService.saveChatSession(mockSessionData);
      
      expect(chatHistoryService.saveChatSession).toHaveBeenCalledWith(mockSessionData, 'test-user');
      expect(result).toEqual(mockResult);
    });
  });

  describe('AI分析历史功能', () => {
    it('应该从缓存获取分析历史', async () => {
      const mockHistory = [
        { id: '1', analysis: 'Good' },
        { id: '2', analysis: 'Bad' }
      ];
      const mockCacheItem = {
        data: mockHistory,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = await unifiedStorageService.getAnalysisHistory(10, false);
      
      expect(result).toEqual(mockHistory.slice(0, 10));
    });

    it('应该保存AI分析记录', async () => {
      const { aiAnalysisService } = await import('../history/firebaseHistoryService');
      const { generateUniqueId } = await import('../../utils/idGenerator');
      const mockAnalysisData = { analysis: 'Good' };
      const mockResult = { id: 'mock-id-123', analysis: 'Good', saved: true };
      
      aiAnalysisService.saveAnalysis.mockResolvedValue(mockResult);
      generateUniqueId.mockReturnValue('mock-id-123');
      unifiedStorageService.userId = 'test-user';
      localStorageMock.getItem.mockReturnValue('[]');
      
      const result = await unifiedStorageService.saveAnalysis(mockAnalysisData);
      
      expect(generateUniqueId).toHaveBeenCalled();
      expect(aiAnalysisService.saveAnalysis).toHaveBeenCalledWith(
        { ...mockAnalysisData, id: 'mock-id-123' },
        'test-user'
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('字典搜索历史功能', () => {
    it('应该从缓存获取字典搜索历史', async () => {
      const mockHistory = [
        { id: '1', term: 'hello' },
        { id: '2', term: 'world' }
      ];
      const mockCacheItem = {
        data: mockHistory,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = await unifiedStorageService.getDictionarySearchHistory(10, false);
      
      expect(result).toEqual(mockHistory.slice(0, 10));
    });

    it('应该保存字典搜索记录', async () => {
      const { dictionarySearchService } = await import('../history/firebaseHistoryService');
      const { generateUniqueId } = await import('../../utils/idGenerator');
      const mockSearchData = { term: 'hello' };
      const mockResult = { id: 'mock-id-123', term: 'hello', saved: true };
      
      dictionarySearchService.saveSearch.mockResolvedValue(mockResult);
      generateUniqueId.mockReturnValue('mock-id-123');
      unifiedStorageService.userId = 'test-user';
      localStorageMock.getItem.mockReturnValue('[]');
      
      const result = await unifiedStorageService.saveDictionarySearch(mockSearchData);
      
      expect(generateUniqueId).toHaveBeenCalled();
      expect(dictionarySearchService.saveSearch).toHaveBeenCalledWith(
        { ...mockSearchData, id: 'mock-id-123' },
        'test-user'
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('写作历史功能', () => {
    it('应该从缓存获取写作历史', async () => {
      const mockHistory = [
        { id: '1', content: 'Hello world' },
        { id: '2', content: 'Good morning' }
      ];
      const mockCacheItem = {
        data: mockHistory,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = await unifiedStorageService.getWritingHistory(10, false);
      
      expect(result).toEqual(mockHistory.slice(0, 10));
    });

    it('应该保存写作记录', async () => {
      const { writingHistoryService } = await import('../history/firebaseHistoryService');
      const { generateUniqueId } = await import('../../utils/idGenerator');
      const mockWritingData = { content: 'Hello world' };
      const mockResult = { id: 'mock-id-123', content: 'Hello world', saved: true };
      
      writingHistoryService.saveWriting.mockResolvedValue(mockResult);
      generateUniqueId.mockReturnValue('mock-id-123');
      unifiedStorageService.userId = 'test-user';
      localStorageMock.getItem.mockReturnValue('[]');
      
      const result = await unifiedStorageService.saveWritingHistory(mockWritingData);
      
      expect(generateUniqueId).toHaveBeenCalled();
      expect(writingHistoryService.saveWriting).toHaveBeenCalledWith(
        { ...mockWritingData, id: 'mock-id-123' },
        'test-user'
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('日记历史功能', () => {
    it('应该从缓存获取日记历史', async () => {
      const mockHistory = [
        { id: '1', content: 'Today is good' },
        { id: '2', content: 'Yesterday was bad' }
      ];
      const mockCacheItem = {
        data: mockHistory,
        timestamp: Date.now(),
        version: '1.0'
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCacheItem));
      
      const result = await unifiedStorageService.getDiaryHistory(10, false);
      
      expect(result).toEqual(mockHistory.slice(0, 10));
    });

    it('应该保存日记记录', async () => {
      const { diaryHistoryService } = await import('../history/firebaseHistoryService');
      const { generateUniqueId } = await import('../../utils/idGenerator');
      const mockDiaryData = { content: 'Today is good' };
      const mockResult = { id: 'mock-id-123', content: 'Today is good', saved: true };
      
      diaryHistoryService.saveDiary.mockResolvedValue(mockResult);
      generateUniqueId.mockReturnValue('mock-id-123');
      unifiedStorageService.userId = 'test-user';
      localStorageMock.getItem.mockReturnValue('[]');
      
      const result = await unifiedStorageService.saveDiaryHistory(mockDiaryData);
      
      expect(generateUniqueId).toHaveBeenCalled();
      expect(diaryHistoryService.saveDiary).toHaveBeenCalledWith(
        { ...mockDiaryData, id: 'mock-id-123' },
        'test-user'
      );
      expect(result).toEqual(mockResult);
    });
  });

  describe('同步功能', () => {
    it('应该添加和移除同步监听器', () => {
      const callback = vi.fn();
      
      unifiedStorageService.addSyncListener(callback);
      expect(unifiedStorageService.syncCallbacks).toContain(callback);
      
      unifiedStorageService.removeSyncListener(callback);
      expect(unifiedStorageService.syncCallbacks).not.toContain(callback);
    });

    it('应该触发同步状态变化', () => {
      const callback = vi.fn();
      unifiedStorageService.addSyncListener(callback);
      
      unifiedStorageService.triggerSyncStatus('syncing');
      
      expect(callback).toHaveBeenCalledWith('syncing');
    });

    it('应该启动后台同步定时器', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      
      unifiedStorageService.startBackgroundSync();
      
      expect(setIntervalSpy).toHaveBeenCalled();
      expect(unifiedStorageService.syncInterval).toBeTruthy();
    });

    it('应该停止后台同步定时器', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      unifiedStorageService.syncInterval = setInterval(() => {}, 1000);
      
      unifiedStorageService.stopBackgroundSync();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(unifiedStorageService.syncInterval).toBeNull();
    });
  });

  describe('缓存统计功能', () => {
    it('应该返回缓存统计信息', () => {
      const mockData1 = '{"data":"test1"}';
      const mockData2 = '{"data":"test2"}';
      
      localStorageMock.getItem
        .mockReturnValueOnce(mockData1) // CACHE_KEYS.CHAT_HISTORY
        .mockReturnValueOnce(mockData2) // CACHE_KEYS.ANALYSIS_HISTORY
        .mockReturnValueOnce(null)      // CACHE_KEYS.DICTIONARY_SEARCH
        .mockReturnValueOnce(null)      // CACHE_KEYS.WRITING_HISTORY
        .mockReturnValueOnce(null)      // CACHE_KEYS.DIARY_HISTORY
        .mockReturnValueOnce(null);     // CACHE_KEYS.LAST_SYNC
      
      const stats = unifiedStorageService.getCacheStats();
      
      expect(stats.totalSize).toBe(mockData1.length + mockData2.length);
      expect(stats.itemCount).toBe(2);
      expect(stats.isOnline).toBe(true);
      expect(stats.syncInProgress).toBe(false);
    });
  });

  describe('错误处理', () => {
    it('应该处理localStorage错误', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      const result = unifiedStorageService.getCache('test', 60000);
      
      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith('获取缓存失败:', expect.any(Error));
      
      consoleErrorSpy.mockRestore();
    });

    it('应该处理设置缓存错误', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      unifiedStorageService.setCache('test', { data: 'test' });
      
      expect(consoleErrorSpy).toHaveBeenCalledWith('设置缓存失败:', expect.any(Error));
      
      consoleErrorSpy.mockRestore();
    });

    it('应该处理保存数据错误', async () => {
      const { chatHistoryService } = await import('../history/firebaseHistoryService');
      const error = new Error('保存失败');
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      chatHistoryService.saveChatSession.mockRejectedValue(error);
      unifiedStorageService.userId = 'test-user';
      
      await expect(unifiedStorageService.saveChatSession({})).rejects.toThrow(error);
      expect(consoleErrorSpy).toHaveBeenCalledWith('保存聊天会话失败:', error);
      
      consoleErrorSpy.mockRestore();
    });
  });
});
