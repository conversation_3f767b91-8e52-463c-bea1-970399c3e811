import wordnet from 'wordnet';

// 初始化 WordNet
const initWordNet = () => {
  return new Promise((resolve, reject) => {
    wordnet.init({
      cache: true, // 启用缓存以提高性能
    }, (err) => {
      if (err) {
        console.error('WordNet 初始化失败:', err);
        reject(err);
      } else {
        console.log('WordNet 初始化成功');
        resolve();
      }
    });
  });
};

// 查询单词定义
const lookupDefinition = (word) => {
  return new Promise((resolve, reject) => {
    wordnet.lookup(word, (err, definitions) => {
      if (err) {
        console.error(`查询单词 "${word}" 失败:`, err);
        reject(err);
      } else {
        resolve(definitions);
      }
    });
  });
};

// 查询同义词
const lookupSynonyms = async (word) => {
  try {
    const definitions = await lookupDefinition(word);
    const synonyms = new Set();
    
    definitions.forEach(def => {
      if (def.synonyms) {
        def.synonyms.forEach(syn => {
          if (syn !== word) {
            synonyms.add(syn);
          }
        });
      }
    });
    
    return Array.from(synonyms);
  } catch (error) {
    console.error(`查询同义词失败:`, error);
    return [];
  }
};

// 查询反义词
const lookupAntonyms = async (word) => {
  try {
    const definitions = await lookupDefinition(word);
    const antonyms = new Set();
    
    definitions.forEach(def => {
      if (def.antonyms) {
        def.antonyms.forEach(ant => {
          antonyms.add(ant);
        });
      }
    });
    
    return Array.from(antonyms);
  } catch (error) {
    console.error(`查询反义词失败:`, error);
    return [];
  }
};

// 查询单词的详细信息
const getWordDetails = async (word) => {
  try {
    const definitions = await lookupDefinition(word);
    const synonyms = await lookupSynonyms(word);
    const antonyms = await lookupAntonyms(word);
    
    return {
      word,
      definitions: definitions.map(def => ({
        partOfSpeech: def.pos,
        glossary: def.gloss,
        examples: def.exp || []
      })),
      synonyms,
      antonyms
    };
  } catch (error) {
    console.error(`获取单词详情失败:`, error);
    return {
      word,
      definitions: [],
      synonyms: [],
      antonyms: []
    };
  }
};

export {
  initWordNet,
  lookupDefinition,
  lookupSynonyms,
  lookupAntonyms,
  getWordDetails
};