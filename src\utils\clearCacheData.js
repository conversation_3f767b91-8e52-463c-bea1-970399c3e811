/**
 * 清理缓存数据工具
 * 用于清理使用旧ID格式的缓存数据
 */

/**
 * 清理所有相关的缓存数据
 */
export const clearAllCacheData = () => {
  console.log('🧹 开始清理所有缓存数据...');
  
  const keysToRemove = [
    'english_chat_history',
    'ai_analysis_history', 
    'dictionary_search_history',
    'writing_history',
    'diary_history',
    'current_chat_session_id',
    'migration_completed_',
    'cache_',
    'last_sync_'
  ];
  
  let removedCount = 0;
  
  // 清理localStorage
  for (let i = localStorage.length - 1; i >= 0; i--) {
    const key = localStorage.key(i);
    if (key) {
      // 检查是否需要清理这个键
      const shouldRemove = keysToRemove.some(pattern => {
        if (pattern.endsWith('_')) {
          return key.startsWith(pattern);
        }
        return key === pattern;
      });
      
      if (shouldRemove) {
        console.log('🗑️ 删除缓存键:', key);
        localStorage.removeItem(key);
        removedCount++;
      }
    }
  }
  
  console.log(`✅ 清理完成，共删除 ${removedCount} 个缓存项`);
  return removedCount;
};

/**
 * 清理特定类型的缓存数据
 */
export const clearSpecificCache = (type) => {
  const cacheKeys = {
    chat: 'english_chat_history',
    analysis: 'ai_analysis_history',
    dictionary: 'dictionary_search_history', 
    writing: 'writing_history',
    diary: 'diary_history'
  };
  
  const key = cacheKeys[type];
  if (key && localStorage.getItem(key)) {
    console.log(`🗑️ 删除${type}缓存:`, key);
    localStorage.removeItem(key);
    return true;
  }
  
  console.log(`⚠️ 未找到${type}缓存数据`);
  return false;
};

/**
 * 检查缓存数据是否使用旧ID格式
 */
export const checkCacheDataFormat = () => {
  console.log('🔍 检查缓存数据格式...');
  
  const keys = [
    'english_chat_history',
    'ai_analysis_history',
    'dictionary_search_history',
    'writing_history',
    'diary_history'
  ];
  
  const results = {};
  
  keys.forEach(key => {
    const data = localStorage.getItem(key);
    if (data) {
      try {
        const parsed = JSON.parse(data);
        if (Array.isArray(parsed) && parsed.length > 0) {
          const firstItem = parsed[0];
          const hasOldIdFormat = firstItem.id && /^\d{13}$/.test(firstItem.id.toString());
          results[key] = {
            count: parsed.length,
            hasOldIdFormat,
            sampleId: firstItem.id
          };
        }
      } catch (error) {
        results[key] = { error: error.message };
      }
    } else {
      results[key] = { count: 0 };
    }
  });
  
  console.log('📊 缓存数据检查结果:', results);
  return results;
};

// 导出到全局对象
if (typeof window !== 'undefined') {
  window.cacheUtils = {
    clearAllCacheData,
    clearSpecificCache,
    checkCacheDataFormat
  };
  
  console.log('🔧 缓存清理工具已加载到 window.cacheUtils');
  console.log('💡 使用方法:');
  console.log('  - window.cacheUtils.clearAllCacheData() // 清理所有缓存');
  console.log('  - window.cacheUtils.clearSpecificCache("chat") // 清理特定类型');
  console.log('  - window.cacheUtils.checkCacheDataFormat() // 检查数据格式');
}
