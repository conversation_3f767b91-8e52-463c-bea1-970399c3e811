// 词典切换功能测试工具
// 用于测试用户选择不同词典服务的效果

import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

// 测试单词列表
const TEST_WORDS = [
  'hello',
  'beautiful', 
  'computer',
  'education',
  'technology'
];

// 测试不同词典服务
const testDictionaryServices = async () => {
  console.log('🧪 测试词典服务切换功能...');
  console.log('='.repeat(50));
  
  const services = [
    { name: 'ecdict', label: 'ECDICT 本地词典' },
    { name: 'free_dictionary', label: 'Free Dictionary API' }
  ];
  
  const results = {};
  
  for (const service of services) {
    console.log(`\n📚 测试 ${service.label}...`);
    results[service.name] = {};
    
    for (const word of TEST_WORDS) {
      try {
        const startTime = performance.now();
        const result = await getWordDetails(word, service.name);
        const endTime = performance.now();
        
        const queryTime = endTime - startTime;
        
        if (result && !result.notFound) {
          console.log(`  ✅ ${word}: ${result.source} (${queryTime.toFixed(2)}ms)`);
          results[service.name][word] = {
            success: true,
            source: result.source,
            time: queryTime,
            hasTranslation: !!result.translation,
            hasPhonetic: !!result.phonetic,
            hasExchange: !!result.exchange
          };
        } else {
          console.log(`  ❌ ${word}: 未找到`);
          results[service.name][word] = {
            success: false,
            source: 'none',
            time: queryTime
          };
        }
      } catch (error) {
        console.error(`  ❌ ${word}: 查询失败 -`, error.message);
        results[service.name][word] = {
          success: false,
          error: error.message
        };
      }
    }
  }
  
  // 输出测试结果汇总
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(50));
  
  for (const service of services) {
    const serviceResults = results[service.name];
    const successCount = Object.values(serviceResults).filter(r => r.success).length;
    const totalCount = Object.keys(serviceResults).length;
    const avgTime = Object.values(serviceResults)
      .filter(r => r.time)
      .reduce((sum, r) => sum + r.time, 0) / Object.values(serviceResults).filter(r => r.time).length;
    
    console.log(`\n${service.label}:`);
    console.log(`  成功率: ${successCount}/${totalCount} (${((successCount/totalCount)*100).toFixed(1)}%)`);
    if (avgTime) {
      console.log(`  平均查询时间: ${avgTime.toFixed(2)}ms`);
    }
    
    // 显示每个单词的详细结果
    for (const [word, result] of Object.entries(serviceResults)) {
      if (result.success) {
        console.log(`    ${word}: ${result.source} ${result.time ? `(${result.time.toFixed(2)}ms)` : ''}`);
      } else {
        console.log(`    ${word}: 失败 ${result.error ? `(${result.error})` : ''}`);
      }
    }
  }
  
  return results;
};

// 测试特定单词在不同服务中的表现
const testWordAcrossServices = async (word) => {
  console.log(`\n🔍 测试单词 "${word}" 在不同服务中的表现...`);
  console.log('='.repeat(50));
  
  const services = [
    { name: 'ecdict', label: 'ECDICT 本地词典' },
    { name: 'free_dictionary', label: 'Free Dictionary API' }
  ];
  
  const results = {};
  
  for (const service of services) {
    try {
      const startTime = performance.now();
      const result = await getWordDetails(word, service.name);
      const endTime = performance.now();
      
      const queryTime = endTime - startTime;
      
      console.log(`\n${service.label}:`);
      if (result && !result.notFound) {
        console.log(`  ✅ 查询成功 (${queryTime.toFixed(2)}ms)`);
        console.log(`  📖 数据源: ${result.source}`);
        console.log(`  🔤 单词: ${result.word}`);
        if (result.phonetic) {
          console.log(`  🔊 音标: ${result.phonetic}`);
        }
        if (result.translation) {
          console.log(`  🇨🇳 中文释义: ${result.translation}`);
        }
        if (result.definition) {
          console.log(`  🇺🇸 英文释义: ${result.definition}`);
        }
        if (result.exchange && Object.keys(result.exchange).length > 0) {
          console.log(`  🔄 词形变化:`, result.exchange);
        }
        if (result.tags && result.tags.length > 0) {
          console.log(`  🏷️ 标签: ${result.tags.join(', ')}`);
        }
        if (result.collins > 0) {
          console.log(`  ⭐ 柯林斯星级: ${result.collins}`);
        }
        if (result.oxford) {
          console.log(`  📚 牛津3000: 是`);
        }
        if (result.bnc > 0) {
          console.log(`  📊 BNC词频: ${result.bnc}`);
        }
        if (result.frq > 0) {
          console.log(`  📈 当代词频: ${result.frq}`);
        }
        
        results[service.name] = {
          success: true,
          source: result.source,
          time: queryTime,
          data: result
        };
      } else {
        console.log(`  ❌ 查询失败 (${queryTime.toFixed(2)}ms)`);
        console.log(`  📝 错误: ${result?.error || '未找到该单词'}`);
        
        results[service.name] = {
          success: false,
          time: queryTime,
          error: result?.error || '未找到该单词'
        };
      }
    } catch (error) {
      console.error(`  ❌ ${service.label} 查询出错:`, error.message);
      results[service.name] = {
        success: false,
        error: error.message
      };
    }
  }
  
  return results;
};

// 性能对比测试
const performanceComparison = async () => {
  console.log('\n⚡ 性能对比测试...');
  console.log('='.repeat(50));
  
  const testWord = 'hello';
  const iterations = 5;
  
  const services = [
    { name: 'ecdict', label: 'ECDICT 本地词典' },
    { name: 'free_dictionary', label: 'Free Dictionary API' }
  ];
  
  const performanceResults = {};
  
  for (const service of services) {
    console.log(`\n测试 ${service.label}...`);
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = performance.now();
        await getWordDetails(testWord, service.name);
        const endTime = performance.now();
        
        const queryTime = endTime - startTime;
        times.push(queryTime);
        console.log(`  第 ${i + 1} 次: ${queryTime.toFixed(2)}ms`);
      } catch (error) {
        console.log(`  第 ${i + 1} 次: 失败 - ${error.message}`);
      }
    }
    
    if (times.length > 0) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      
      performanceResults[service.name] = {
        average: avgTime,
        min: minTime,
        max: maxTime,
        successRate: (times.length / iterations) * 100
      };
      
      console.log(`  平均时间: ${avgTime.toFixed(2)}ms`);
      console.log(`  最快时间: ${minTime.toFixed(2)}ms`);
      console.log(`  最慢时间: ${maxTime.toFixed(2)}ms`);
      console.log(`  成功率: ${((times.length / iterations) * 100).toFixed(1)}%`);
    }
  }
  
  return performanceResults;
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始词典切换功能测试...');
  console.log('='.repeat(60));
  
  try {
    // 测试不同词典服务
    const serviceResults = await testDictionaryServices();
    
    // 测试特定单词
    const wordResults = await testWordAcrossServices('beautiful');
    
    // 性能对比
    const perfResults = await performanceComparison();
    
    console.log('\n🎯 测试完成！');
    console.log('='.repeat(60));
    
    return {
      serviceResults,
      wordResults,
      performanceResults: perfResults
    };
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return null;
  }
};

// 导出测试函数
export {
  testDictionaryServices,
  testWordAcrossServices,
  performanceComparison,
  runAllTests
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testDictionarySwitching = {
    testDictionaryServices,
    testWordAcrossServices,
    performanceComparison,
    runAllTests
  };
  
  console.log('🧪 词典切换功能测试工具已加载');
  console.log('💡 使用方法:');
  console.log('  - window.testDictionarySwitching.runAllTests() // 运行所有测试');
  console.log('  - window.testDictionarySwitching.testWordAcrossServices("hello") // 测试特定单词');
  console.log('  - window.testDictionarySwitching.performanceComparison() // 性能对比');
}
