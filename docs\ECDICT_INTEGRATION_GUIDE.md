# ECDICT 词典集成指南

## 概述

本项目已成功集成了 [ECDICT](https://github.com/skywind3000/ECDICT) 开源英汉词典数据库，为用户提供更丰富的词典查询功能。

## 功能特性

### 1. 多数据源支持
- **ECDICT 本地词典**: 包含 76 万词条的中英双解词典
- **Free Dictionary API**: 在线词典服务，提供同义词、反义词等
- **统一查询接口**: 自动按优先级尝试不同数据源

### 2. 丰富的词典信息
- **基础信息**: 单词、音标、英文释义、中文释义
- **词性标注**: 详细的词性信息和使用频率
- **词形变化**: 动词时态、形容词比较级、名词复数等
- **考试标签**: 四六级、雅思、托福、GRE 等考试词汇标记
- **词频信息**: BNC 语料库和当代语料库词频排名
- **柯林斯星级**: 词汇重要程度评级
- **牛津 3000**: 核心词汇标记

### 3. 智能查询功能
- **精确查询**: 直接查询单词
- **模糊搜索**: 支持部分匹配和相似词查找
- **词形变化查询**: 自动识别动词时态、复数形式等
- **多语言支持**: 中英文双语释义

## 技术实现

### 文件结构
```
src/services/dictionary/
├── ecdictService.js           # ECDICT 词典服务
├── unifiedDictionaryService.js # 统一词典服务
├── freeDictionaryService.js   # Free Dictionary API 服务
└── ...

public/data/dictionary/
└── ecdict.mini.csv           # ECDICT 词典数据文件
```

### 核心服务

#### 1. ECDICT 服务 (`ecdictService.js`)
- 解析 CSV 格式的词典数据
- 提供单词查询、模糊搜索功能
- 支持词形变化解析
- 本地数据存储，查询速度快

#### 2. 统一词典服务 (`unifiedDictionaryService.js`)
- 整合多个词典数据源
- 按优先级自动选择最佳数据源
- 提供统一的查询接口
- 支持服务状态监控

### 数据格式

ECDICT 使用 CSV 格式存储，包含以下字段：

| 字段 | 说明 | 示例 |
|------|------|------|
| word | 单词名称 | hello |
| phonetic | 音标 | həˈloʊ |
| definition | 英文释义 | A greeting or expression of goodwill |
| translation | 中文释义 | 你好；问候 |
| pos | 词性 | n:80/v:20 |
| collins | 柯林斯星级 | 3 |
| oxford | 牛津3000 | 1 |
| tag | 考试标签 | cet4 cet6 |
| bnc | BNC词频 | 1234 |
| frq | 当代词频 | 567 |
| exchange | 词形变化 | p:helloed/d:helloed/i:helloing |
| detail | 扩展信息 | JSON格式 |
| audio | 音频URL | 发音链接 |

## 使用方法

### 1. 基本查询
```javascript
import { getWordDetails } from './services/dictionary/unifiedDictionaryService';

// 查询单词
const result = await getWordDetails('hello');
console.log(result);
```

### 2. 模糊搜索
```javascript
import { searchWords } from './services/dictionary/unifiedDictionaryService';

// 模糊搜索
const results = await searchWords('hel', { limit: 10, fuzzy: true });
console.log(results);
```

### 3. 获取统计信息
```javascript
import { getDictionaryStats } from './services/dictionary/unifiedDictionaryService';

// 获取词典统计
const stats = await getDictionaryStats();
console.log(stats);
```

### 4. 在组件中使用
```jsx
import { getWordDetails } from '../services/dictionary/unifiedDictionaryService';

const MyComponent = () => {
  const [wordData, setWordData] = useState(null);
  
  const lookupWord = async (word) => {
    const result = await getWordDetails(word);
    setWordData(result);
  };
  
  return (
    <div>
      {/* 使用 wordData 渲染词典信息 */}
    </div>
  );
};
```

## 测试工具

项目提供了完整的测试工具来验证集成效果：

### 控制台测试
在浏览器控制台中运行：
```javascript
// 运行所有测试
window.testEcdictIntegration.runAllTests();

// 测试单个单词查询
window.testEcdictIntegration.testWordLookup('hello');

// 测试模糊搜索
window.testEcdictIntegration.testFuzzySearch('hel');

// 测试服务状态
window.testEcdictIntegration.testInitialization();
```

### 测试内容
- 服务初始化测试
- 单词查询功能测试
- 模糊搜索功能测试
- 词典统计信息测试
- 性能测试（查询时间）

## 配置选项

### 词典服务优先级
在 `unifiedDictionaryService.js` 中配置：
```javascript
const DICTIONARY_CONFIG = {
  FREE_DICTIONARY: 1,  // 优先级 1
  ECDICT: 2,           // 优先级 2
};
```

### 查询选项
```javascript
const options = {
  limit: 20,        // 搜索结果数量限制
  fuzzy: true       // 是否启用模糊搜索
};
```

## 性能优化

### 1. 数据加载
- 词典数据在应用启动时异步加载
- 使用 Map 数据结构提高查询效率
- 支持数据预加载和缓存

### 2. 查询优化
- 本地数据查询速度快（毫秒级）
- 在线服务作为备用数据源
- 智能缓存机制

### 3. 内存管理
- 按需加载词典数据
- 支持数据清理和重新加载
- 内存使用监控

## 故障排除

### 常见问题

1. **词典数据加载失败**
   - 检查 `public/data/dictionary/ecdict.mini.csv` 文件是否存在
   - 确认文件路径正确
   - 查看控制台错误信息

2. **查询结果为空**
   - 确认单词拼写正确
   - 尝试模糊搜索
   - 检查网络连接（在线服务）

3. **性能问题**
   - 使用 mini 版本数据文件进行测试
   - 检查浏览器内存使用情况
   - 考虑数据分片加载

### 调试工具
```javascript
// 检查服务状态
const status = await checkServiceStatus();
console.log(status);

// 获取词典统计
const stats = await getDictionaryStats();
console.log(stats);
```

## 扩展功能

### 1. 添加新的词典服务
```javascript
// 在 unifiedDictionaryService.js 中添加新服务
const DICTIONARY_CONFIG = {
  FREE_DICTIONARY: 1,
  ECDICT: 2,
  NEW_SERVICE: 3  // 新服务
};
```

### 2. 自定义查询逻辑
```javascript
// 实现自定义查询函数
const customQuery = async (word) => {
  // 自定义查询逻辑
  return result;
};
```

### 3. 数据格式转换
```javascript
// 转换数据格式以适配现有接口
const convertData = (ecdictData) => {
  // 数据转换逻辑
  return convertedData;
};
```

## 更新日志

### v1.0.0 (2024-01-XX)
- 集成 ECDICT 词典数据库
- 实现统一词典服务
- 添加模糊搜索功能
- 支持词形变化查询
- 提供完整的测试工具

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目使用 MIT 许可证，ECDICT 数据遵循原项目许可证。

## 相关链接

- [ECDICT 项目主页](https://github.com/skywind3000/ECDICT)
- [Free Dictionary API](https://dictionaryapi.dev/)
- [项目文档](../README.md)