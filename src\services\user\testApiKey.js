// API密钥获取测试工具
import { getDoubaoApiKey, getSystemConfig } from './userSettingsService.js';

/**
 * 测试API密钥获取功能
 */
export const testApiKeyRetrieval = async () => {
  console.log('🧪 开始测试API密钥获取功能...');
  
  try {
    // 测试1: 获取系统配置
    console.log('\n📋 测试1: 获取系统配置');
    const systemConfig = await getSystemConfig();
    console.log('✅ 系统配置获取成功:', {
      hasApiKey: !!systemConfig.doubaoApiKey,
      apiKeyLength: systemConfig.doubaoApiKey?.length || 0,
      configKeys: Object.keys(systemConfig)
    });
    
    // 测试2: 获取API密钥
    console.log('\n🔑 测试2: 获取API密钥');
    const apiKey = await getDoubaoApiKey();
    console.log('✅ API密钥获取成功:', {
      hasKey: !!apiKey,
      keyLength: apiKey?.length || 0,
      keyPreview: apiKey ? `${apiKey.substring(0, 8)}...` : '无'
    });
    
    // 测试3: 验证API密钥格式
    console.log('\n🔍 测试3: 验证API密钥格式');
    if (apiKey && apiKey.includes('-')) {
      console.log('✅ API密钥格式正确 (包含连字符)');
    } else {
      console.log('⚠️ API密钥格式可能不正确');
    }
    
    console.log('\n🎉 所有测试完成！');
    return {
      success: true,
      systemConfig,
      apiKey,
      message: 'API密钥获取功能正常'
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message,
      message: 'API密钥获取功能异常'
    };
  }
};

/**
 * 测试Firebase连接状态
 */
export const testFirebaseConnection = async () => {
  console.log('🔥 测试Firebase连接状态...');
  
  try {
    const config = await getSystemConfig();
    console.log('✅ Firebase连接正常，可以访问Firestore');
    return true;
  } catch (error) {
    console.error('❌ Firebase连接失败:', error);
    return false;
  }
};

/**
 * 运行所有测试
 */
export const runAllApiKeyTests = async () => {
  console.log('🚀 运行所有API密钥相关测试...\n');
  
  const firebaseTest = await testFirebaseConnection();
  const apiKeyTest = await testApiKeyRetrieval();
  
  console.log('\n📊 测试结果汇总:');
  console.log(`Firebase连接: ${firebaseTest ? '✅ 正常' : '❌ 失败'}`);
  console.log(`API密钥获取: ${apiKeyTest.success ? '✅ 正常' : '❌ 失败'}`);
  
  if (apiKeyTest.success) {
    console.log('🎯 建议: 应用可以正常使用AI功能');
  } else {
    console.log('⚠️ 建议: 检查Firebase配置和网络连接');
  }
  
  return {
    firebase: firebaseTest,
    apiKey: apiKeyTest.success
  };
};

// 在浏览器控制台中暴露测试函数
if (typeof window !== 'undefined') {
  window.testApiKey = {
    testApiKeyRetrieval,
    testFirebaseConnection,
    runAllApiKeyTests
  };
  console.log('🔧 API密钥测试工具已加载，使用 window.testApiKey.runAllApiKeyTests() 运行测试');
}
