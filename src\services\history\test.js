/**
 * Firebase历史记录服务测试文件
 * 用于验证服务功能是否正常工作
 */

import { 
  hybridChatHistoryService,
  hybridAnalysisService,
  hybridDictionarySearchService,
  hybridWritingHistoryService,
  hybridDiaryHistoryService,
  getCurrentStorageStatus,
  STORAGE_MODE
} from './initHistoryService';

/**
 * 测试聊天历史服务
 */
export const testChatHistoryService = async () => {
  console.log('🧪 开始测试聊天历史服务...');
  
  try {
    // 测试保存聊天会话
    const sessionData = {
      id: 'test-session-' + Date.now(),
      title: '测试对话',
      messages: [
        { type: 'user', content: 'Hello', timestamp: new Date().toISOString() },
        { type: 'ai', content: 'Hi there!', timestamp: new Date().toISOString() }
      ],
      timestamp: new Date().toISOString()
    };
    
    const savedSession = await hybridChatHistoryService.saveChatSession(sessionData);
    console.log('✅ 聊天会话保存成功:', savedSession);
    
    // 测试获取聊天历史
    const history = await hybridChatHistoryService.getChatHistory(10);
    console.log('✅ 获取聊天历史成功，数量:', history.length);
    
    // 测试删除会话
    await hybridChatHistoryService.deleteChatSession(savedSession.id);
    console.log('✅ 聊天会话删除成功');
    
    return true;
  } catch (error) {
    console.error('❌ 聊天历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试AI分析历史服务
 */
export const testAnalysisService = async () => {
  console.log('🧪 开始测试AI分析历史服务...');
  
  try {
    // 测试保存分析记录
    const analysisData = {
      text: 'This is a test text for analysis.',
      rawAnalysis: 'AI analysis result',
      analysis: { sentiment: 'positive', language: 'en' }
    };
    
    const savedAnalysis = await hybridAnalysisService.saveAnalysis(analysisData);
    console.log('✅ AI分析记录保存成功:', savedAnalysis);
    
    // 测试获取分析历史
    const history = await hybridAnalysisService.getAnalysisHistory(10);
    console.log('✅ 获取AI分析历史成功，数量:', history.length);
    
    // 测试删除记录
    await hybridAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('✅ AI分析记录删除成功');
    
    return true;
  } catch (error) {
    console.error('❌ AI分析历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试字典搜索历史服务
 */
export const testDictionarySearchService = async () => {
  console.log('🧪 开始测试字典搜索历史服务...');
  
  try {
    // 测试保存搜索词
    await hybridDictionarySearchService.saveSearchTerm('hello');
    await hybridDictionarySearchService.saveSearchTerm('world');
    console.log('✅ 搜索词保存成功');
    
    // 测试获取搜索历史
    const history = await hybridDictionarySearchService.getSearchHistory(10);
    console.log('✅ 获取搜索历史成功，数量:', history.length);
    
    // 测试清空搜索历史
    await hybridDictionarySearchService.clearSearchHistory();
    console.log('✅ 搜索历史清空成功');
    
    return true;
  } catch (error) {
    console.error('❌ 字典搜索历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试写作历史服务
 */
export const testWritingHistoryService = async () => {
  console.log('🧪 开始测试写作历史服务...');
  
  try {
    // 测试保存写作记录
    const writingData = {
      content: 'This is a test writing piece.',
      type: 'essay',
      language: 'en',
      wordCount: 10
    };
    
    const savedWriting = await hybridWritingHistoryService.saveWriting(writingData);
    console.log('✅ 写作记录保存成功:', savedWriting);
    
    // 测试获取写作历史
    const history = await hybridWritingHistoryService.getWritingHistory(10);
    console.log('✅ 获取写作历史成功，数量:', history.length);
    
    return true;
  } catch (error) {
    console.error('❌ 写作历史服务测试失败:', error);
    return false;
  }
};

/**
 * 测试日记历史服务
 */
export const testDiaryHistoryService = async () => {
  console.log('🧪 开始测试日记历史服务...');
  
  try {
    // 测试保存日记
    const diaryData = {
      date: new Date().toISOString().split('T')[0],
      content: 'Today was a great day for testing!',
      mood: 'excited',
      weather: 'sunny'
    };
    
    const savedDiary = await hybridDiaryHistoryService.saveDiary(diaryData);
    console.log('✅ 日记保存成功:', savedDiary);
    
    // 测试获取日记历史
    const history = await hybridDiaryHistoryService.getDiaryHistory(10);
    console.log('✅ 获取日记历史成功，数量:', history.length);
    
    return true;
  } catch (error) {
    console.error('❌ 日记历史服务测试失败:', error);
    return false;
  }
};

/**
 * 运行所有测试
 */
export const runAllTests = async () => {
  console.log('🚀 开始运行所有历史记录服务测试...');
  
  const results = {
    chat: await testChatHistoryService(),
    analysis: await testAnalysisService(),
    dictionary: await testDictionarySearchService(),
    writing: await testWritingHistoryService(),
    diary: await testDiaryHistoryService()
  };
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log('📊 测试结果汇总:');
  console.log('✅ 通过:', passed);
  console.log('❌ 失败:', total - passed);
  console.log('📈 成功率:', Math.round((passed / total) * 100) + '%');
  
  return results;
};

/**
 * 测试存储状态
 */
export const testStorageStatus = () => {
  console.log('🧪 测试存储状态...');
  
  const status = getCurrentStorageStatus();
  console.log('📱 当前存储状态:', status);
  
  return status;
};

/**
 * 测试存储模式切换
 */
export const testStorageModeSwitch = () => {
  console.log('🧪 测试存储模式切换...');
  
  console.log('📱 当前模式:', getCurrentStorageStatus().mode);
  
  // 注意：这个测试需要在有用户登录的情况下运行
  if (getCurrentStorageStatus().userId) {
    console.log('✅ 用户已登录，可以使用Firebase模式');
  } else {
    console.log('ℹ️ 用户未登录，使用本地模式');
  }
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中
  window.testHistoryServices = {
    runAllTests,
    testChatHistoryService,
    testAnalysisService,
    testDictionarySearchService,
    testWritingHistoryService,
    testDiaryHistoryService,
    testStorageStatus,
    testStorageModeSwitch
  };
  
  console.log('🧪 历史记录服务测试工具已加载到 window.testHistoryServices');
  console.log('使用方法: testHistoryServices.runAllTests()');
}
