
import React from 'react';
import Header from '../components/Header';
import VintageTextEditor from '../components/VintageTextEditor';
import SuggestionBubble from '../components/SuggestionBubble';
import AIResponseSidebar from '../components/AIResponseSidebar';
import HistoryModal from '../components/HistoryModal';
import TextSelectionMenu from '../components/TextSelectionMenu';
import EdgeNavigationArrow from '../components/EdgeNavigationArrow';
import ConfirmDialog from '../components/ConfirmDialog';
import AuthModal from '../components/auth/AuthModal';
import { useImmersiveMode } from '../hooks/useImmersiveMode';
import { useConfirmDialog } from '../hooks/useConfirmDialog';
import { useAppContext } from '../context/AppContext';
import { useEditorAuth } from '../hooks/useEditorAuth';
import { useEditorText } from '../hooks/useEditorText';
import { useEditorAnalysis } from '../hooks/useEditorAnalysis';
import { useEditorEvents } from '../hooks/useEditorEvents';

export default function EditorPage() {
  const { state, dispatch, openModal, closeModal, isModalOpen } = useAppContext();
  const { isDarkMode } = state;

  // 自定义Hooks
  const { dialogState, showConfirm, hideConfirm } = useConfirmDialog();
  const { isImmersiveMode, toggleImmersiveMode } = useImmersiveMode();
  
  // 认证管理
  const { user, showAuthModal, handleShowAuth, handleLogout, handleCloseAuthModal } = useEditorAuth();
  
  // AI分析管理
  const { isAnalyzing, suggestions, setSuggestions, rawAIResponse, handleAnalyze } = useEditorAnalysis(user, handleShowAuth);
  
  // 文本管理
  const {
    text,
    setText,
    hoveredSuggestion,
    setHoveredSuggestion,
    activeBubble,
    setActiveBubble,
    bubblePosition,
    setBubblePosition,
    handleTextChange: handleTextChangeBase,
    handleNewDocument: handleNewDocumentBase,
    handleSelectHistory: handleSelectHistoryBase
  } = useEditorText(setSuggestions);
  
  // 事件处理
  const {
    applySuggestion,
    dismissSuggestion,
    handleLookupWord,
    handleShowApiConfig,
    handleShowDictionary,
    handleSwitchToChat
  } = useEditorEvents(text, setText, suggestions, setSuggestions, dispatch);

  // 包装文本变化处理函数
  const handleTextChange = (newText) => {
    handleTextChangeBase(newText, suggestions);
  };

  // 包装新文档处理函数
  const handleNewDocument = () => {
    handleNewDocumentBase(showConfirm);
  };

  // 包装历史记录选择处理函数
  const handleSelectHistory = (historyRecord) => {
    handleSelectHistoryBase(historyRecord, closeModal);
  };

  // 包装AI分析处理函数
  const handleAnalyzeWrapper = () => {
    handleAnalyze(text);
  };

  return (
    <div className="min-h-screen transition-colors duration-300 editor-scrollbar" style={{
      backgroundColor: isDarkMode ? '#1A1611' : '#F5EFE6',
      height: isImmersiveMode ? '100vh' : 'auto',
      overflow: isImmersiveMode ? 'hidden' : 'auto',
      marginRight: isModalOpen('aiResponse') ? `calc(min(400px, 40vw) + 16px)` : '0',
      transition: 'margin-right 0.3s ease-in-out'
    }}>
      {!isImmersiveMode && (
        <Header
          onShowApiConfig={handleShowApiConfig}
          onShowAuth={handleShowAuth}
          onLogout={handleLogout}
          onToggleImmersiveMode={toggleImmersiveMode}
          isDarkMode={isDarkMode}
          user={user}
        />
      )}

      <div className="max-w-7xl mx-auto" style={{
        padding: isImmersiveMode ? '80px 32px 32px 32px' : '32px 32px 48px 32px'
      }}>
        <VintageTextEditor
          text={text}
          setText={handleTextChange}
          suggestions={suggestions}
          isAnalyzing={isAnalyzing}
          onAnalyze={handleAnalyzeWrapper}
          hoveredSuggestion={hoveredSuggestion}
          setHoveredSuggestion={setHoveredSuggestion}
          setBubblePosition={setBubblePosition}
          setActiveBubble={setActiveBubble}
          isImmersiveMode={isImmersiveMode}
          onShowAIResponse={() => openModal('aiResponse')}
          rawAIResponse={rawAIResponse}
          onShowHistory={() => openModal('history')}
          onShowDictionary={handleShowDictionary}
          onNewDocument={handleNewDocument}
          isDarkMode={isDarkMode}
        />
      </div>

      {activeBubble && (
        <SuggestionBubble
          suggestion={activeBubble}
          position={bubblePosition}
          onApply={applySuggestion}
          onDismiss={dismissSuggestion}
          onClose={() => setActiveBubble(null)}
          isDarkMode={isDarkMode}
        />
      )}

      <AIResponseSidebar
        isOpen={isModalOpen('aiResponse')}
        onClose={closeModal}
        content={rawAIResponse}
        isDarkMode={isDarkMode}
      />

      <HistoryModal
        isOpen={isModalOpen('history')}
        onClose={closeModal}
        onSelectHistory={handleSelectHistory}
        isDarkMode={isDarkMode}
      />

      <TextSelectionMenu onLookupWord={handleLookupWord} isDarkMode={isDarkMode} />

      {/* 认证弹窗 */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={handleCloseAuthModal}
        isDarkMode={isDarkMode}
      />

      {/* 边缘导航箭头 - 当显示建议时隐藏 */}
      <EdgeNavigationArrow
        onSwitchToChat={handleSwitchToChat}
        isDarkMode={isDarkMode}
        isHidden={activeBubble !== null || isModalOpen('aiResponse')}
      />

      {/* 自定义确认对话框 */}
      <ConfirmDialog
        isOpen={dialogState.isOpen}
        onClose={hideConfirm}
        onConfirm={dialogState.onConfirm}
        title={dialogState.title}
        message={dialogState.message}
        confirmText={dialogState.confirmText}
        cancelText={dialogState.cancelText}
        type={dialogState.type}
        isDarkMode={isDarkMode}
      />
    </div>
  );
}
